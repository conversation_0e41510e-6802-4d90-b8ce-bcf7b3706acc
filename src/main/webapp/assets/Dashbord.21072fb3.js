var j=Object.defineProperty;var C=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable;var L=(r,c,h)=>c in r?j(r,c,{enumerable:!0,configurable:!0,writable:!0,value:h}):r[c]=h,I=(r,c)=>{for(var h in c||(c={}))V.call(c,h)&&L(r,h,c[h]);if(C)for(var h of C(c))D.call(c,h)&&L(r,h,c[h]);return r};import{C as E,o as P,b as T,e as f,D as O,r as S,f as w,w as _,t as k,g as W,F as Y,k as q,n as F,p as N,h as z}from"./vendor.45bae780.js";import{_ as A,a as G}from"./index.7c530c9d.js";import{s as H}from"./index.ce5e7832.js";import"./request.3dc6f249.js";var R={exports:{}};(function(r,c){(function(h,p){r.exports=p()})(E,function(){function h(v,t){for(var i=0;i<t.length;i++){var e=t[i];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(v,e.key,e)}}function p(v){return function(t){if(Array.isArray(t)){for(var i=0,e=new Array(t.length);i<t.length;i++)e[i]=t[i];return e}}(v)||function(t){if(Symbol.iterator in Object(t)||Object.prototype.toString.call(t)==="[object Arguments]")return Array.from(t)}(v)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var d=window.devicePixelRatio||1,o=10*d,u=o/2;return function(){function v(t,i){(function(e,s){if(!(e instanceof s))throw new TypeError("Cannot call a class as a function")})(this,v),this.canvas=function(e){var s=document.getElementById(e),a=s.parentNode.clientWidth,l=s.parentNode.clientHeight;return s.style.width=a+"px",s.style.height=l+"px",s.width=a*d,s.height=l*d,s}(t),this.ctx=this.canvas.getContext("2d"),this.type="bar",this.showValue=!0,this.showGrid=!0,this.topPadding=60*d,this.leftPadding=50*d,this.rightPadding=10*d,this.bottomPadding=50*d,this.yEqual=5,this.yLength=0,this.xLength=0,this.ySpace=0,this.xRorate=0,this.yRorate=0,this.xRotate=0,this.yRotate=0,this.bgColor="#fff",this.axisColor="#666",this.gridColor="#eee",this.title={text:"",color:"#666",position:"top",font:"bold "+18*d+"px Arial",top:o,bottom:u},this.legend={display:!0,position:"top",color:"#666",font:14*d+"px Arial",top:45*d,bottom:15*d,textWidth:0},this.radius=100*d,this.innerRadius=60*d,this.colorList=["#4A90E2","#F5A623","#ff5858","#5e64ff","#2AC766","#743ee2","#b554ff","#199475"],this.init(i)}return function(t,i,e){i&&h(t.prototype,i),e&&h(t,e)}(v,[{key:"init",value:function(t){if(t.title=Object.assign({},this.title,t.title),t.legend=Object.assign({},this.legend,t.legend),Object.assign(this,t),!t.labels||!t.labels.length)throw new Error("\u7F3A\u5C11\u4E3B\u8981\u53C2\u6570labels");if(!t.datasets||!t.datasets.length)throw new Error("\u7F3A\u5C11\u4E3B\u8981\u53C2\u6570datasets");this.drawBackground(),this.type==="bar"||this.type==="line"?this.renderBarChart():this.renderPieChart(),this.drawLegend()}},{key:"renderBarChart",value:function(){this.yLength=Math.floor((this.canvas.height-this.topPadding-this.bottomPadding-o)/this.yEqual),this.xLength=Math.floor((this.canvas.width-this.leftPadding-this.rightPadding-o)/this.labels.length),this.ySpace=function(t,i){var e=t.map(function(l){return l.data.reduce(function(n,g){return g<n?n:g})}),s=Math.ceil(Math.max.apply(Math,p(e))/i),a=s.toString().length-1;return a=2<a?2:a,Math.ceil(s/Math.pow(10,a))*Math.pow(10,a)}(this.datasets,this.yEqual),this.drawXAxis(),this.drawYAxis(),this.drawBarContent()}},{key:"drawBarContent",value:function(){var t=this.ctx,i=this.datasets.length;t.beginPath();for(var e=0;e<i;e++){t.font=this.legend.font,this.legend.textWidth+=Math.ceil(t.measureText(this.datasets[e].label).width),t.fillStyle=t.strokeStyle=this.datasets[e].fillColor||this.colorList[e];for(var s=this.datasets[e].data,a=0;a<s.length;a++)if(!(a>this.labels.length-1)){var l=this.xLength/(i+1),n=this.yLength/this.ySpace,g=this.leftPadding+this.xLength*a+l*(e+.5),x=g+l,m=this.canvas.height-this.bottomPadding,y=m-s[a]*n;if(this.type==="bar")t.fillRect(g,y,x-g,m-y),this.drawValue(s[a],g+l/2,y-u);else if(this.type==="line"){var b=this.leftPadding+this.xLength*(a+.5);t.beginPath(),t.arc(b,y,3*d,0,2*Math.PI,!0),t.fill(),a!==0&&(t.beginPath(),t.strokeStyle=this.datasets[e].fillColor||this.colorList[e],t.lineWidth=2*d,t.moveTo(b-this.xLength,m-s[a-1]*n),t.lineTo(b,y),t.stroke(),t.lineWidth=1*d),this.drawValue(s[a],b,y-o)}}}t.stroke()}},{key:"renderPieChart",value:function(){for(var t=this.ctx,i=this.labels.length,e=this.datasets[0],s=e.data,a=s.reduce(function(B,$){return B+$}),l=-Math.PI/2,n=this.canvas.width/2,g=this.canvas.height/2,x=0;x<i;x++){t.font=this.legend.font,this.legend.textWidth+=Math.ceil(t.measureText(this.labels[x]).width),t.beginPath(),t.strokeStyle=t.fillStyle=e.colorList&&e.colorList[x]||this.colorList[x],t.moveTo(n,g);var m=l,y=l+=s[x]/a*2*Math.PI;t.arc(n,g,this.radius,m,y),t.closePath(),t.fill();var b=(m+y)/2;this.drawPieValue(s[x],b)}this.type==="ring"&&(t.beginPath(),t.fillStyle=this.bgColor,t.arc(n,g,this.innerRadius,0,2*Math.PI),t.closePath(),t.fill())}},{key:"drawValue",value:function(t,i,e){var s=this.ctx;this.showValue&&(s.textBaseline="middle",s.font=12*d+"px Arial",s.textAlign="center",s.fillText(t,i,e))}},{key:"drawPieValue",value:function(t,i){var e=this.ctx;if(this.showValue){var s=this.canvas.width/2,a=this.canvas.height/2,l=Math.ceil(Math.abs(this.radius*Math.cos(i))),n=Math.floor(Math.abs(this.radius*Math.sin(i)));e.textBaseline="middle",this.showValue&&(i<=0?(e.textAlign="left",e.moveTo(s+l,a-n),e.lineTo(s+l+o,a-n-o),e.moveTo(s+l+o,a-n-o),e.lineTo(s+l+3*o,a-n-o),e.stroke(),e.fillText(t,s+l+3.5*o,a-n-o)):0<i&&i<=Math.PI/2?(e.textAlign="left",e.moveTo(s+l,a+n),e.lineTo(s+l+o,a+n+o),e.moveTo(s+l+o,a+n+o),e.lineTo(s+l+3*o,a+n+o),e.stroke(),e.fillText(t,s+l+3.5*o,a+n+o)):i>Math.PI/2&&i<Math.PI?(e.textAlign="right",e.moveTo(s-l,a+n),e.lineTo(s-l-o,a+n+o),e.moveTo(s-l-o,a+n+o),e.lineTo(s-l-3*o,a+n+o),e.stroke(),e.fillText(t,s-l-3.5*o,a+n+o)):(e.textAlign="right",e.moveTo(s-l,a-n),e.lineTo(s-l-o,a-n-o),e.moveTo(s-l-o,a-n-o),e.lineTo(s-l-3*o,a-n-o),e.stroke(),e.fillText(t,s-l-3.5*o,a-n-o)))}}},{key:"drawBackground",value:function(){this.ctx.fillStyle=this.bgColor,this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height),this.drawTitle()}},{key:"drawTitle",value:function(){var t=this.title;if(t.text){var i=this.ctx;i.beginPath(),i.font=t.font,i.textAlign="center",i.fillStyle=t.color,t.position==="top"?(i.textBaseline="top",i.fillText(t.text,this.canvas.width/2,t.top)):(i.textBaseline="bottom",i.fillText(t.text,this.canvas.width/2,this.canvas.height-t.bottom))}}},{key:"drawXAxis",value:function(){var t=this.ctx,i=this.canvas.height-this.bottomPadding+.5;t.beginPath(),t.strokeStyle=this.axisColor,t.moveTo(this.leftPadding,i),t.lineTo(this.canvas.width-this.rightPadding,i),t.stroke(),this.drawXPoint()}},{key:"drawXPoint",value:function(){var t=this.ctx;t.beginPath(),t.font=12*d+"px Microsoft YaHei",t.textAlign=this.xRorate||this.xRotate?"right":"center",t.textBaseline="top",t.fillStyle=this.axisColor;for(var i=0;i<this.labels.length;i++){var e=this.labels[i],s=this.leftPadding+this.xLength*(i+1)+.5,a=this.canvas.height-this.bottomPadding;this.showGrid?(t.strokeStyle=this.gridColor,t.moveTo(s,a),t.lineTo(s,this.topPadding+o)):(t.moveTo(s,a),t.lineTo(s,a-u)),t.stroke(),t.save(),t.translate(s-this.xLength/2,a+u),this.xRorate?t.rotate(-this.xRorate*Math.PI/180):t.rotate(-this.xRotate*Math.PI/180),t.fillText(e,0,0),t.restore()}}},{key:"drawYAxis",value:function(){var t=this.ctx;t.beginPath(),t.strokeStyle=this.axisColor,t.moveTo(this.leftPadding-.5,this.canvas.height-this.bottomPadding+.5),t.lineTo(this.leftPadding-.5,this.topPadding+.5),t.stroke(),this.drawYPoint()}},{key:"drawYPoint",value:function(){var t=this.ctx;t.font=12*d+"px Microsoft YaHei",t.textAlign="right",t.textBaseline="middle",t.beginPath();for(var i=0;i<this.yEqual;i++){var e=this.leftPadding,s=this.canvas.height-this.bottomPadding-this.yLength*(i+1)+.5;this.showGrid?(t.strokeStyle=this.gridColor,t.moveTo(e,s),t.lineTo(this.canvas.width-this.rightPadding-o,s)):(t.strokeStyle=this.axisColor,t.moveTo(e-u,s),t.lineTo(e,s)),t.stroke(),t.save(),t.fillStyle=this.axisColor,t.translate(e-o,s),this.yRorate?t.rotate(-this.yRorate*Math.PI/180):t.rotate(-this.yRotate*Math.PI/180),t.fillText(this.ySpace*(i+1),0,0),t.restore()}}},{key:"drawLegend",value:function(){var t=this.legend;if(t.display){var i=this.ctx,e=this.type==="pie"||this.type==="ring";i.beginPath(),i.font=t.font,i.textAlign="left",i.textBaseline="middle";for(var s=e?this.labels.length:this.datasets.length,a=(this.canvas.width-(this.legend.textWidth+(5*s-2)*o))/2,l=0,n=0;n<s;n++){var g=e?this.datasets[0]:this.datasets[n],x=(e?this.labels[n]:g.label)||"";i.fillStyle=g.colorList&&g.colorList[n]||g.fillColor||this.colorList[n],t.position==="top"?(this.drawLegendIcon(a+5*o*n+l,t.top-u,2*o,o),i.fillStyle=t.color,i.fillText(x,a+(5*n+3)*o+l,t.top)):t.position==="bottom"?(this.drawLegendIcon(a+5*o*n+l,this.canvas.height-t.bottom-u,2*o,o),i.fillStyle=t.color,i.fillText(x,a+(5*n+3)*o+l,this.canvas.height-t.bottom)):(i.fillRect(o,t.top+2*o*n,2*o,o),i.fillStyle=t.color,i.fillText(x,4*o,t.top+2*o*n+.5*o)),l+=Math.ceil(i.measureText(x).width)}}}},{key:"drawLegendIcon",value:function(t,i,e,s){var a=this.ctx;this.type==="line"?(a.beginPath(),a.strokeStyle=a.fillStyle,a.lineWidth=2*d,a.moveTo(t,i+u),a.lineTo(t+2*o,i+u),a.stroke(),a.lineWidth=1*d,a.arc(t+o,i+u,3*d,0,2*Math.PI,!0),a.fill()):a.fillRect(t,i,e,s)}}]),v}()})})(R);var X=R.exports;const J={props:{canvasId:{type:String,default:"",required:!0},options:{type:Object,required:!0}},mounted(){this.renderChart()},methods:{renderChart(){if(!this.checkOptions())return;const r=I({},this.options);new X(this.canvasId,r)},checkOptions(){const r=this.options;return!(!r.datasets||!r.datasets.length||!r.labels||!r.labels.length)}},watch:{options:{handler(r,c){this.renderChart()},deep:!0}}},K=["id"];function Q(r,c,h,p,d,o){return P(),T("div",null,[f("canvas",{id:h.canvasId},null,8,K)])}var U=A(J,[["render",Q]]);const Z={name:"dashboard",setup(){const r=O([]),c=()=>{H().then(e=>{r.value=e.data,console.log(r.value)}).catch(function(e){console.log(e)})};let h=new Date,p=h.getFullYear(),d=h.getMonth()+1,o=h.getDate(),u=h.getHours(),v=h.getMinutes(),t=h.getSeconds();const i=`${p}-${d}-${o} ${u}:${v}:${t}`;return c(),{startData:r,name:localStorage.getItem("ms_username"),currentTime:i}},components:{Schart:U},computed:{role(){return this.name==="admin"?"\u8D85\u7EA7\u7BA1\u7406\u5458":"\u666E\u901A\u7528\u6237"}},methods:{changeDate(){const r=new Date().getTime();this.data.forEach((c,h)=>{const p=new Date(r-(6-h)*864e5);c.name=`${p.getFullYear()}/${p.getMonth()+1}/${p.getDate()}`})}}},M=r=>(N("data-v-5c103bf5"),r=r(),z(),r),tt={style:{"padding-bottom":"8px","margin-right":"8px"}},et={style:{height:"200px"}},it={style:{display:"flex","align-items":"center","justify-content":"space-between","padding-bottom":"20px","border-bottom":"2px solid #ccc","margin-bottom":"20px"}},st=M(()=>f("div",{class:"user-avator",style:{display:"flex","align-items":"center"}},[f("img",{src:G,style:{width:"120px",height:"120px","border-radius":"50%"}})],-1)),at={style:{"font-size":"30px",color:"#222",width:"100%"}},ot=M(()=>f("span",{style:{"font-size":"14px",color:"#999"}},"\u8D85\u7EA7\u7BA1\u7406\u5458",-1)),nt={style:{"font-size":"14px",color:"#999","line-height":"25px"}},lt={style:{"margin-left":"70px"}},rt=M(()=>f("i",{class:"el-icon-lx-people grid-con-icon"},null,-1)),ht={class:"grid-cont-right"},dt={class:"grid-num"};function ct(r,c,h,p,d,o){const u=S("el-card"),v=S("el-col"),t=S("el-row");return P(),T("div",null,[w(t,{gutter:20,class:"mgb20"},{default:_(()=>[w(v,{span:24},{default:_(()=>[f("div",tt,[w(u,{shadow:"hover","body-style":{padding:"20px"},style:{width:"400px",height:"250px"}},{default:_(()=>[f("div",et,[f("div",it,[st,f("div",null,[f("div",at,k(p.name),1),ot])]),f("div",nt,[W(" \u5F53\u524D\u65F6\u95F4\uFF1A "),f("span",lt,k(p.currentTime),1)])])]),_:1})])]),_:1})]),_:1}),w(t,{gutter:20,class:"mgb20"},{default:_(()=>[(P(!0),T(Y,null,q(p.startData,(i,e)=>(P(),T("div",{key:e,style:{"padding-bottom":"8px","margin-right":"8px"}},[w(u,{shadow:"hover","body-style":{padding:"20px"},style:{width:"400px",height:"250px"}},{default:_(()=>[f("div",{class:F(i.icon),style:{display:"flex","align-items":"center",height:"200px"}},[rt,f("div",ht,[f("div",dt,k(i.total),1),f("div",null,k(i.title),1)])],2)]),_:2},1024)]))),128))]),_:1})])}var vt=A(Z,[["render",ct],["__scopeId","data-v-5c103bf5"]]);export{vt as default};
