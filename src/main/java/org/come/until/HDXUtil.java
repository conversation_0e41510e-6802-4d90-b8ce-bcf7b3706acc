package org.come.until;

import come.tool.Stall.AssetUpdate;
import io.netty.channel.ChannelHandlerContext;
import org.come.action.monitor.MonitorUtil;
import org.come.bean.LoginResult;
import org.come.bean.NChatBean;
import org.come.handler.SendMessage;
import org.come.model.HDXData;
import org.come.model.HDXRole;
import org.come.protocol.Agreement;
import org.come.server.GameServer;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 黄大小
 * <AUTHOR>
 *
 */
public class HDXUtil {
    public static List<HDXRole> HdxRoleList = new ArrayList<>();
    public static List<HDXData> HdxDataList = new ArrayList<>();
    public static List<HDXRole> HdxRoleRecordList = new ArrayList<>();

    public static String[] number= {"1","2","3","4","5","6"};
    //计算竞猜结果
    public static Random random=new Random();
    //中奖人数
    public static int total= 0;
    //中奖大话币
    public static long totalMoney= 0;
    //中奖仙玉
    public static long totalMoney1= 0;
    //头彩号1
    private static String tch;
    //头彩号2
    private static String tch1;
    public static ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
    public static void HDXMan() {
//		int mathMaxInt = 0;
//		if(HdxDataList.size()>0) {
//			mathMaxInt = HdxDataList.stream().mapToInt( HDXData::getStage ).max().getAsInt(); //获取开奖期数
//		}
        //开始抽奖

        //第一个号
        String num1 = number[random.nextInt(number.length)];
        //第二个号
        String num2 = number[random.nextInt(number.length)];
        //发送消息前端开骰子
        sendMessage(num1, num2);
        //判断中奖发送奖励
        KJ(num1,num2);
        System.out.println("开始摇号");
        Runnable task = () -> {
            try {
                //系统公告
                NChatBean beand = new NChatBean();
                beand.setId(5);
                beand.setMessage("#W开奖啦！！头彩号码为#Y" + tch + "#W和#Y" + tch1 + "#W开盘号码为#Y" + num1 + "#W和#Y" + num2 + " " );
                String msgk = Agreement.getAgreement().chatAgreement(GsonUtil.getGsonUtil().getgson().toJson(beand));
                SendMessage.sendMessageToAllRoles(msgk);
            } catch (Exception e) {
                // 添加异常处理，记录日志或执行其他逻辑
                e.printStackTrace();
            }
        };
        executor.schedule(task, 5, TimeUnit.SECONDS);

        //保存开奖数据
        HDXData hdxData = new HDXData();
//		hdxData.setStage(mathMaxInt+1);
        hdxData.setPrizeNumber(num1+num2);
        hdxData.setTotal(total);
        hdxData.setTotalMoney(hdxData.getTotalMoney() + totalMoney);
        MonitorUtil.getHdxMoney().add(hdxData.getTotalMoney());
        MonitorUtil.getHdxMoney1().add(hdxData.getTotalMoney1());

        HdxDataList.add(hdxData);

        HdxRoleList = new ArrayList<>();
        total = 0;
        totalMoney = 0;

//		NChatBean beand = new NChatBean();
//		beand.setId(6);
//		beand.setMessage("#W开奖啦！！头彩号码为#Y" + tch + "#W和#Y" + tch1 + "#W开盘号码为#Y" + num1 + "#W和#Y" + num2 + " " );
//		String msgk = Agreement.getAgreement().chatAgreement(GsonUtil.getGsonUtil().getgson().toJson(beand));
//		SendMessage.sendMessageToMapRoles(1197L,msgk);
//		beand.setMessage("#R黄大小第#G[" + (mathMaxInt + 1) + "]#R期开奖结果：#G[" + num1 + " ，" + num2 + "]，" + "#R当期头彩号码是：#G[" + tch + " ，" + tch1 + "]");
    }

    public static void TCH(){
        System.out.println("刷新头牌");
        if (tch == null&&tch1 == null) {
            //第一个号
            tch = number[random.nextInt(number.length)];
            //第二个号
            tch1 = number[random.nextInt(number.length)];
        }else{
            Runnable task = () -> {
                try {// 获取协议信息
                    //第一个号
                    tch = number[random.nextInt(number.length)];
                    //第二个号
                    tch1 = number[random.nextInt(number.length)];
                } catch (Exception e) {
                    // 添加异常处理，记录日志或执行其他逻辑
                    e.printStackTrace();
                }
            };
            executor.schedule(task, 5, TimeUnit.SECONDS);
        }
    }

    public static void run(){
        Runnable task = () -> {
            try {
                String msgk = Agreement.getAgreement().PromptAgreement("买定离手：倒计时10秒开奖，各位客官抓紧时间下押！#12");
                SendMessage.sendMessageToMapRoles(1197L, msgk);
                System.out.println("10秒开奖");
            } catch (Exception e) {
                e.printStackTrace();
            }
        };
        executor.schedule(task, 290, TimeUnit.SECONDS);
        //10秒倒数计时，需要的话放开注释
//		hdxUtil = new HDXThread();
//		Thread T1 = new Thread(hdxUtil);
//		T1.start();
    }
    public static void KJ(String num1, String num2) {
        if (HdxRoleList.size() > 0) {
            //所有参与者
            for (HDXRole hdxRole : HdxRoleList) {
                LoginResult roleInfo = AllServiceUtil.getRoleTableService().selectRoleID(hdxRole.getRole_id());
                ChannelHandlerContext ctx1 = GameServer.getRoleNameMap().get(roleInfo.getRolename());
                LoginResult loginResult = GameServer.getAllLoginRole().get(ctx1);
                String rolenum = hdxRole.getPrizeNumber();
                String b = String.valueOf(identical(num1, num2));

                //发奖励
                if (hdxRole.getTpye() == 5) {
                    if (b.equals("1") && rolenum.equals(b)) {
                        total = total + 1;
                        totalMoney = totalMoney + (hdxRole.getMoney() * 25 - hdxRole.getMoney());
                        AssetUpdate assetUpdate = new AssetUpdate();
                        assetUpdate.updata("D=" + hdxRole.getMoney() * 25);
                        loginResult.setGold(loginResult.getGold().add((new BigDecimal(hdxRole.getMoney() * 25))));
                        SendMessage.sendMessageByRoleName(loginResult.getRolename(), Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
                        SendMessage.sendMessageToSlef(ctx1, Agreement.getAgreement().PromptAgreement("#Y头彩#W开中，赢" + (hdxRole.getMoney() * 25) + "两！"));
                        NChatBean bean = new NChatBean();
                        bean.setId(5);
                        bean.setMessage("#Y头彩#W开中，庄东#Y" + roleInfo.getRolename() + "#W赢 "+ (hdxRole.getMoney() * 25) + "两，庄家赔");
                        String msg = Agreement.getAgreement().chatAgreement(GsonUtil.getGsonUtil().getgson().toJson(bean));
                        SendMessage.sendMessageToAllRoles(msg);
                    } else if (b.equals("2") && rolenum.equals(b)) {
                        total = total + 1;
                        totalMoney = totalMoney + (hdxRole.getMoney() * 10 - hdxRole.getMoney());
                        AssetUpdate assetUpdate = new AssetUpdate();
                        assetUpdate.updata("D=" + hdxRole.getMoney() * 10);
                        loginResult.setGold(loginResult.getGold().add((new BigDecimal(hdxRole.getMoney() * 10))));
                        SendMessage.sendMessageByRoleName(loginResult.getRolename(), Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
                        SendMessage.sendMessageToSlef(ctx1, Agreement.getAgreement().PromptAgreement("你中#Y双对#W，共得到" + (hdxRole.getMoney() * 10) + "两金"));
                    } else if (b.equals("3") && rolenum.equals(b)) {
                        total = total + 1;
                        totalMoney = totalMoney + (hdxRole.getMoney() * 5 - hdxRole.getMoney());
                        AssetUpdate assetUpdate = new AssetUpdate();
                        assetUpdate.updata("D=" + hdxRole.getMoney() * 5);
                        loginResult.setGold(loginResult.getGold().add((new BigDecimal(hdxRole.getMoney() * 5))));
                        SendMessage.sendMessageByRoleName(loginResult.getRolename(), Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
                        SendMessage.sendMessageToSlef(ctx1, Agreement.getAgreement().PromptAgreement("你中#Y七星#W，共得到" + (hdxRole.getMoney() * 5 + "两金")));
                    } else if (b.equals("4") && rolenum.equals(b)) {
                        total = total + 1;
                        totalMoney = totalMoney + (hdxRole.getMoney() * 2 - hdxRole.getMoney());
                        AssetUpdate assetUpdate = new AssetUpdate();
                        assetUpdate.updata("D=" + hdxRole.getMoney() * 2);
                        loginResult.setGold(loginResult.getGold().add((new BigDecimal(hdxRole.getMoney() * 2))));
                        SendMessage.sendMessageByRoleName(loginResult.getRolename(), Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
                        SendMessage.sendMessageToSlef(ctx1, Agreement.getAgreement().PromptAgreement("你中#Y散星#W，共得到" + (hdxRole.getMoney() * 2 + "两金")));
                    } else {
                        SendMessage.sendMessageToSlef(ctx1, Agreement.getAgreement().PromptAgreement("未投中，庄家赢！#28"));
                    }
                } else if (hdxRole.getTpye() == 6) {
                    if (b.equals("1") && rolenum.equals(b)) {
                        total = total + 1;
                        totalMoney1 = totalMoney1 + (hdxRole.getMoney() * 25 - hdxRole.getMoney());
                        AssetUpdate assetUpdate = new AssetUpdate();
                        assetUpdate.updata("X=" + hdxRole.getMoney() * 25);
                        loginResult.setCodecard(loginResult.getCodecard().add((new BigDecimal(hdxRole.getMoney() * 25))));
                        SendMessage.sendMessageByRoleName(loginResult.getRolename(), Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
                        SendMessage.sendMessageToSlef(ctx1, Agreement.getAgreement().PromptAgreement("#Y头彩#W开中，赢" + (hdxRole.getMoney() * 25) + "仙玉！"));
                        NChatBean bean = new NChatBean();
                        bean.setId(5);
//						bean.setMessage("#R恭喜玩家#Y[" + roleInfo.getRolename() + "]在黄大小第#G[" + hdxRole.getStage() + "]#G期中得头彩！！获得#Y" + (hdxRole.getMoney() * 25) + "仙玉真是可喜可贺！！！");
                        bean.setMessage("#Y头彩#W开中，庄东#Y" + roleInfo.getRolename() + "#W赢 "+ (hdxRole.getMoney() * 25) + "两，庄家赔");
                        String msg = Agreement.getAgreement().chatAgreement(GsonUtil.getGsonUtil().getgson().toJson(bean));
                        SendMessage.sendMessageToAllRoles(msg);
                    } else if (b.equals("2") && rolenum.equals(b)) {
                        total = total + 1;
                        totalMoney1 = totalMoney1 + (hdxRole.getMoney() * 10 - hdxRole.getMoney());
                        AssetUpdate assetUpdate = new AssetUpdate();
                        assetUpdate.updata("X=" + hdxRole.getMoney() * 10);
                        loginResult.setCodecard(loginResult.getCodecard().add((new BigDecimal(hdxRole.getMoney() * 10))));
                        SendMessage.sendMessageByRoleName(loginResult.getRolename(), Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
                        SendMessage.sendMessageToSlef(ctx1, Agreement.getAgreement().PromptAgreement("你中#Y双对#W，共得到" + (hdxRole.getMoney() * 10) + "仙玉"));
                    } else if (b.equals("3") && rolenum.equals(b)) {
                        total = total + 1;
                        totalMoney1 = totalMoney1 + (hdxRole.getMoney() * 5 - hdxRole.getMoney());
                        AssetUpdate assetUpdate = new AssetUpdate();
                        assetUpdate.updata("X=" + hdxRole.getMoney() * 5);
                        loginResult.setCodecard(loginResult.getCodecard().add((new BigDecimal(hdxRole.getMoney() * 5))));
                        SendMessage.sendMessageByRoleName(loginResult.getRolename(), Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
                        SendMessage.sendMessageToSlef(ctx1, Agreement.getAgreement().PromptAgreement("你中#Y七星#W，共得到" + (hdxRole.getMoney() * 5 + "仙玉")));
                    } else if (b.equals("4") && rolenum.equals(b)) {
                        total = total + 1;
                        totalMoney1 = totalMoney1 + (hdxRole.getMoney() * 2 - hdxRole.getMoney());
                        AssetUpdate assetUpdate = new AssetUpdate();
                        assetUpdate.updata("X=" + hdxRole.getMoney() * 2);
                        loginResult.setCodecard(loginResult.getCodecard().add((new BigDecimal(hdxRole.getMoney() * 2))));
                        SendMessage.sendMessageByRoleName(loginResult.getRolename(), Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
                        SendMessage.sendMessageToSlef(ctx1, Agreement.getAgreement().PromptAgreement("你中#Y散星#W，共得到" + (hdxRole.getMoney() * 2 + "仙玉")));
                    } else {
                        SendMessage.sendMessageToSlef(ctx1, Agreement.getAgreement().PromptAgreement("未投中，庄家赢！#28"));
                    }
                }
            }
        }
    }

    private static void sendMessage(String num1, String num2) {
        SendMessage.sendMessageToMapRoles(1197L, Agreement.getAgreement().PromptAgreement("H-" + num1 + "-" + num2));
    }
    //发奖励判断
    public static int identical(String num1,String num2) {
        int number1 = Integer.parseInt(num1);
        int number2 = Integer.parseInt(num2);
        int num = number1+number2;
        if(num1.equals(tch)&&num2.equals(tch1)) {
            return 1;
        }else if (num1.equals(num2)){
            return 2;
        }else if (num==7){
            return 3;
        }else if (num==3 || num==5 || num==9 || num==11) {
            return 4;
        }
        return 0;
    }
    public static void delHDXRole() {
        Runnable task = () -> {
            try {
                HdxRoleList.clear();
                tch = null;
                tch1 = null;
            } catch (Exception e) {
                e.printStackTrace();
            }
        };
        executor.schedule(task, 6, TimeUnit.SECONDS);
    }

    public static String getTch() {
        return tch;
    }

    public static void setTch(String tch) {
        HDXUtil.tch = tch;
    }

    public static String getTch1() {
        return tch1;
    }

    public static void setTch1(String tch1) {
        HDXUtil.tch1 = tch1;
    }
}
