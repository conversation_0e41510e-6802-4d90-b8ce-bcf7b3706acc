webpackJsonp([21],{Lfd0:function(t,e){},Sgp7:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=i("mvHQ"),l=i.n(a),n=(i("eNfa"),i("VsUZ")),o={data:function(){return{tableData:[],showDialog:!1,showDialogNew:!1,payvip:{id:0,paynum:100,givegoods:"",grade:11,instructiontext:"",increationtext:"",getnumber:""},payvip2:{id:0,paynum:100,givegoods:"",grade:11,instructiontext:"",increationtext:"",getnumber:0}}},methods:{handleEdit:function(t,e){this.payvip.id=e.id,this.payvip.paynum=e.paynum,this.payvip.givegoods=e.givegoods,this.payvip.grade=e.grade,this.payvip.instructiontext=e.instructiontext,this.payvip.increationtext=e.increationtext,this.payvip.getnumber=e.getnumber,this.showDialog=!0},handleDelete:function(t,e){var i=this,a={id:e.id,type:"del"};Object(n.h)(a).then(function(t){200==t.status?(i.$message.success("操作成功"),i.initData()):i.$message.error("失败")})},addPayvip:function(){this.showDialogNew=!1;var t=this,e={payvip:l()(this.payvip2),type:"insert"};Object(n.h)(e).then(function(e){200==e.status?(t.$message.success("操作成功"),t.initData()):t.$message.error("失败")})},editPayvip:function(){this.showDialog=!1;var t=this,e={payvip:l()(this.payvip),type:"update"};Object(n.h)(e).then(function(e){200==e.status?(t.$message.success("操作成功"),t.initData()):t.$message.error("失败")})},initData:function(){var t=this;Object(n.o)({type:"find"}).then(function(e){400==e.status?t.$message.error(e.message):200==e.status?t.tableData=e.list:t.$message.warning("未知错误")})}},mounted:function(){this.initData()}},s={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"cm-title"},[t._v("VIP礼包维护")]),t._v(" "),i("div",{staticClass:"link-top"}),t._v(" "),i("div",{staticClass:"cm-search-div"},[i("br"),t._v(" "),i("el-tag",{attrs:{type:"danger",effect:"dark"}},[t._v("\n    注意!注意!注意!，新增数据时，确保ID每次会自动+1,但是请确保其不会重复，否则一旦删除和修改，会将重复ID的数据一起删除或一起修改。\n    ")]),t._v("\n         "),i("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-circle-plus"},on:{click:function(e){t.showDialogNew=!0}}},[t._v("新增礼包")])],1),t._v(" "),i("div",{staticClass:"cm-title"},[t._v("VIP礼包内容")]),t._v(" "),i("div",{staticClass:"link-top"}),t._v(" "),i("br"),t._v(" "),i("div",[i("el-table",{attrs:{data:t.tableData,"header-cell-style":{textAlign:"center"},"cell-style":{textAlign:"center"},border:"",stripe:""}},[i("el-table-column",{attrs:{prop:"id",label:"礼包ID",width:"100"}}),t._v(" "),i("el-table-column",{attrs:{prop:"paynum",label:"领取金额",width:"300"}}),t._v(" "),i("el-table-column",{attrs:{prop:"givegoods",label:"奖励物品"}}),t._v(" "),i("el-table-column",{attrs:{prop:"grade",label:"VIP等级"}}),t._v(" "),i("el-table-column",{attrs:{prop:"instructiontext",label:"物品描述"}}),t._v(" "),i("el-table-column",{attrs:{prop:"increationtext",label:"持续加成"}}),t._v(" "),i("el-table-column",{attrs:{prop:"getnumber",label:"领取次数"}}),t._v(" "),i("el-table-column",{attrs:{label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{attrs:{size:"mini"},on:{click:function(i){return t.handleEdit(e.$index,e.row)}}},[t._v("编辑")]),t._v(" "),i("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(i){return t.handleDelete(e.$index,e.row)}}},[t._v("删除")])]}}])})],1)],1),t._v(" "),i("el-dialog",{attrs:{title:"礼包修改",visible:t.showDialog,width:"400px"},on:{"update:visible":function(e){t.showDialog=e}}},[i("el-form",{attrs:{model:t.payvip,size:"mini"}},[i("el-form-item",{attrs:{label:"id","label-width":"150px"}},[i("el-input",{attrs:{disabled:!0},model:{value:t.payvip.id,callback:function(e){t.$set(t.payvip,"id",e)},expression:"payvip.id"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"充值金额","label-width":"150px"}},[i("el-input",{model:{value:t.payvip.paynum,callback:function(e){t.$set(t.payvip,"paynum",e)},expression:"payvip.paynum"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"奖励物品","label-width":"150px"}},[i("el-input",{model:{value:t.payvip.givegoods,callback:function(e){t.$set(t.payvip,"givegoods",e)},expression:"payvip.givegoods"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"等级","label-width":"150px"}},[i("el-input",{model:{value:t.payvip.grade,callback:function(e){t.$set(t.payvip,"grade",e)},expression:"payvip.grade"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"描述","label-width":"150px"}},[i("el-input",{model:{value:t.payvip.instructiontext,callback:function(e){t.$set(t.payvip,"instructiontext",e)},expression:"payvip.instructiontext"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"持续加成","label-width":"150px"}},[i("el-input",{model:{value:t.payvip.increationtext,callback:function(e){t.$set(t.payvip,"increationtext",e)},expression:"payvip.increationtext"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"领取次数","label-width":"150px"}},[i("el-input",{model:{value:t.payvip.getnumber,callback:function(e){t.$set(t.payvip,"getnumber",e)},expression:"payvip.getnumber"}})],1)],1),t._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.showDialog=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.editPayvip()}}},[t._v("确 定")])],1)],1),t._v(" "),i("el-dialog",{attrs:{title:"新增礼包",visible:t.showDialogNew,width:"400px"},on:{"update:visible":function(e){t.showDialogNew=e}}},[i("el-form",{attrs:{model:t.payvip2,size:"mini"}},[i("el-form-item",{attrs:{label:"充值金额","label-width":"150px"}},[i("el-input",{model:{value:t.payvip2.paynum,callback:function(e){t.$set(t.payvip2,"paynum",e)},expression:"payvip2.paynum"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"奖励物品","label-width":"150px"}},[i("el-input",{model:{value:t.payvip2.givegoods,callback:function(e){t.$set(t.payvip2,"givegoods",e)},expression:"payvip2.givegoods"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"等级","label-width":"150px"}},[i("el-input",{model:{value:t.payvip2.grade,callback:function(e){t.$set(t.payvip2,"grade",e)},expression:"payvip2.grade"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"描述","label-width":"150px"}},[i("el-input",{model:{value:t.payvip2.instructiontext,callback:function(e){t.$set(t.payvip2,"instructiontext",e)},expression:"payvip2.instructiontext"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"持续加成","label-width":"150px"}},[i("el-input",{model:{value:t.payvip2.increationtext,callback:function(e){t.$set(t.payvip2,"increationtext",e)},expression:"payvip2.increationtext"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"领取次数","label-width":"150px"}},[i("el-input",{model:{value:t.payvip2.getnumber,callback:function(e){t.$set(t.payvip2,"getnumber",e)},expression:"payvip2.getnumber"}})],1)],1),t._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.showDialogNew=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addPayvip()}}},[t._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var p=i("VU/8")(o,s,!1,function(t){i("Lfd0")},"data-v-255a1388",null);e.default=p.exports}});
//# sourceMappingURL=21.f477f04393df9536ccd6.js.map