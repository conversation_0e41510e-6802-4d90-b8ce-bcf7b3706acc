package org.come.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 三端
 * <AUTHOR>
 * @time 2019年7月15日16:41:46
 * 
 */
public class RoleTableNew {

	private BigDecimal role_id;
	private BigDecimal user_id;
	private BigDecimal species_id;
	private BigDecimal summoning_id;
	private BigDecimal gang_id;
	private Integer mount_id;
	private BigDecimal troop_id;
	private BigDecimal race_id;
	private BigDecimal skill_id;
	private BigDecimal booth_id;
	private BigDecimal task_id;
	private Double hp;
	private Double mp;
	private BigDecimal gold;
	private BigDecimal codecard;
	private BigDecimal experience;
	private Integer grade;
	private BigDecimal prestige;
	private BigDecimal pkrecord;
	private String rolename;
	private String title;
	private String path;
	private String sex;
	private String localname;
	private String skill;
	private Integer x;
	private Integer y;
	private Integer mapid;
	private String uptime;
	private String gangpost;
	private BigDecimal achievement;
	private BigDecimal contribution;
	private Integer bone;
	private Integer spir;
	private Integer power;
	private Integer speed;
	private Integer canpoint;
	private Integer captain;
	private BigDecimal savegold;
	private String password;
	private String gangname;
	private Integer havebaby;
	private Integer newrole;
	private String racename;
	private BigDecimal maxexp;
	private String marryobject;
	private String skills;
	private String timinggood;
	private Integer turnaround;
	private String taskdaily;
	private String born;
	private String resistance;
	private Integer servermestring;
	private String taskreceive;
	private String taskcomplete;
	private String taskdata;
	private Integer fighting;
	private Integer dbexp;
	private String score;
	private String kill;
	private String babyid;
	private Date drawing;
	private Integer calm;
	private Integer xiuwei;
	private String extrapoint;
	private Integer fmsld;

	public RoleTableNew() {
		// TODO Auto-generated constructor stub
	}

	public BigDecimal getRole_id() {
		return role_id;
	}

	public void setRole_id(BigDecimal role_id) {
		this.role_id = role_id;
	}

	public BigDecimal getUser_id() {
		return user_id;
	}

	public void setUser_id(BigDecimal user_id) {
		this.user_id = user_id;
	}

	public BigDecimal getSpecies_id() {
		return species_id;
	}

	public void setSpecies_id(BigDecimal species_id) {
		this.species_id = species_id;
	}

	public BigDecimal getSummoning_id() {
		return summoning_id;
	}

	public void setSummoning_id(BigDecimal summoning_id) {
		this.summoning_id = summoning_id;
	}

	public BigDecimal getGang_id() {
		return gang_id;
	}

	public void setGang_id(BigDecimal gang_id) {
		this.gang_id = gang_id;
	}

	public Integer getMount_id() {
		return mount_id;
	}

	public void setMount_id(Integer mount_id) {
		this.mount_id = mount_id;
	}

	public BigDecimal getTroop_id() {
		return troop_id;
	}

	public void setTroop_id(BigDecimal troop_id) {
		this.troop_id = troop_id;
	}

	public BigDecimal getRace_id() {
		return race_id;
	}

	public void setRace_id(BigDecimal race_id) {
		this.race_id = race_id;
	}

	public BigDecimal getSkill_id() {
		return skill_id;
	}

	public void setSkill_id(BigDecimal skill_id) {
		this.skill_id = skill_id;
	}

	public BigDecimal getBooth_id() {
		return booth_id;
	}

	public void setBooth_id(BigDecimal booth_id) {
		this.booth_id = booth_id;
	}

	public BigDecimal getTask_id() {
		return task_id;
	}

	public void setTask_id(BigDecimal task_id) {
		this.task_id = task_id;
	}

	public Double getHp() {
		return hp;
	}

	public void setHp(Double hp) {
		this.hp = hp;
	}

	public Double getMp() {
		return mp;
	}

	public void setMp(Double mp) {
		this.mp = mp;
	}

	public BigDecimal getGold() {
		return gold;
	}

	public void setGold(BigDecimal gold) {
		this.gold = gold;
	}

	public BigDecimal getCodecard() {
		return codecard;
	}

	public void setCodecard(BigDecimal codecard) {
		this.codecard = codecard;
	}

	public BigDecimal getExperience() {
		return experience;
	}

	public void setExperience(BigDecimal experience) {
		this.experience = experience;
	}

	public Integer getGrade() {
		return grade;
	}

	public void setGrade(Integer grade) {
		this.grade = grade;
	}

	public BigDecimal getPrestige() {
		return prestige;
	}

	public void setPrestige(BigDecimal prestige) {
		this.prestige = prestige;
	}

	public BigDecimal getPkrecord() {
		return pkrecord;
	}

	public void setPkrecord(BigDecimal pkrecord) {
		this.pkrecord = pkrecord;
	}

	public String getRolename() {
		return rolename;
	}

	public void setRolename(String rolename) {
		this.rolename = rolename;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getLocalname() {
		return localname;
	}

	public void setLocalname(String localname) {
		this.localname = localname;
	}

	public String getSkill() {
		return skill;
	}

	public void setSkill(String skill) {
		this.skill = skill;
	}

	public Integer getX() {
		return x;
	}

	public void setX(Integer x) {
		this.x = x;
	}

	public Integer getY() {
		return y;
	}

	public void setY(Integer y) {
		this.y = y;
	}

	public Integer getMapid() {
		return mapid;
	}

	public void setMapid(Integer mapid) {
		this.mapid = mapid;
	}

	public String getUptime() {
		return uptime;
	}

	public void setUptime(String uptime) {
		this.uptime = uptime;
	}

	public String getGangpost() {
		return gangpost;
	}

	public void setGangpost(String gangpost) {
		this.gangpost = gangpost;
	}

	public BigDecimal getAchievement() {
		return achievement;
	}

	public void setAchievement(BigDecimal achievement) {
		this.achievement = achievement;
	}

	public BigDecimal getContribution() {
		return contribution;
	}

	public void setContribution(BigDecimal contribution) {
		this.contribution = contribution;
	}

	public Integer getBone() {
		return bone;
	}

	public void setBone(Integer bone) {
		this.bone = bone;
	}

	public Integer getSpir() {
		return spir;
	}

	public void setSpir(Integer spir) {
		this.spir = spir;
	}

	public Integer getPower() {
		return power;
	}

	public void setPower(Integer power) {
		this.power = power;
	}

	public Integer getSpeed() {
		return speed;
	}

	public void setSpeed(Integer speed) {
		this.speed = speed;
	}

	public Integer getCanpoint() {
		return canpoint;
	}

	public void setCanpoint(Integer canpoint) {
		this.canpoint = canpoint;
	}

	public Integer getCaptain() {
		return captain;
	}

	public void setCaptain(Integer captain) {
		this.captain = captain;
	}

	public BigDecimal getSavegold() {
		return savegold;
	}

	public void setSavegold(BigDecimal savegold) {
		this.savegold = savegold;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getGangname() {
		return gangname;
	}

	public void setGangname(String gangname) {
		this.gangname = gangname;
	}

	public Integer getHavebaby() {
		return havebaby;
	}

	public void setHavebaby(Integer havebaby) {
		this.havebaby = havebaby;
	}

	public Integer getNewrole() {
		return newrole;
	}

	public void setNewrole(Integer newrole) {
		this.newrole = newrole;
	}

	public String getRacename() {
		return racename;
	}

	public void setRacename(String racename) {
		this.racename = racename;
	}

	public BigDecimal getMaxexp() {
		return maxexp;
	}

	public void setMaxexp(BigDecimal maxexp) {
		this.maxexp = maxexp;
	}

	public String getMarryobject() {
		return marryobject;
	}

	public void setMarryobject(String marryobject) {
		this.marryobject = marryobject;
	}

	public String getSkills() {
		return skills;
	}

	public void setSkills(String skills) {
		this.skills = skills;
	}

	public String getTiminggood() {
		return timinggood;
	}

	public void setTiminggood(String timinggood) {
		this.timinggood = timinggood;
	}

	public Integer getTurnaround() {
		return turnaround;
	}

	public void setTurnaround(Integer turnaround) {
		this.turnaround = turnaround;
	}

	public String getTaskdaily() {
		return taskdaily;
	}

	public void setTaskdaily(String taskdaily) {
		this.taskdaily = taskdaily;
	}

	public String getBorn() {
		return born;
	}

	public void setBorn(String born) {
		this.born = born;
	}

	public String getResistance() {
		return resistance;
	}

	public void setResistance(String resistance) {
		this.resistance = resistance;
	}

	public Integer getServermestring() {
		return servermestring;
	}

	public void setServermestring(Integer servermestring) {
		this.servermestring = servermestring;
	}

	public String getTaskreceive() {
		return taskreceive;
	}

	public void setTaskreceive(String taskreceive) {
		this.taskreceive = taskreceive;
	}

	public String getTaskcomplete() {
		return taskcomplete;
	}

	public void setTaskcomplete(String taskcomplete) {
		this.taskcomplete = taskcomplete;
	}

	public String getTaskdata() {
		return taskdata;
	}

	public void setTaskdata(String taskdata) {
		this.taskdata = taskdata;
	}

	public Integer getFighting() {
		return fighting;
	}

	public void setFighting(Integer fighting) {
		this.fighting = fighting;
	}

	public Integer getDbexp() {
		return dbexp;
	}

	public void setDbexp(Integer dbexp) {
		this.dbexp = dbexp;
	}

	public String getScore() {
		return score;
	}

	public void setScore(String score) {
		this.score = score;
	}

	public String getKill() {
		return kill;
	}

	public void setKill(String kill) {
		this.kill = kill;
	}

	public String getBabyid() {
		return babyid;
	}

	public void setBabyid(String babyid) {
		this.babyid = babyid;
	}

	public Date getDrawing() {
		return drawing;
	}

	public void setDrawing(Date drawing) {
		this.drawing = drawing;
	}

	public Integer getCalm() {
		return calm;
	}

	public void setCalm(Integer calm) {
		this.calm = calm;
	}

	public Integer getXiuwei() {
		return xiuwei;
	}

	public void setXiuwei(Integer xiuwei) {
		this.xiuwei = xiuwei;
	}

	public String getExtrapoint() {
		return extrapoint;
	}

	public void setExtrapoint(String extrapoint) {
		this.extrapoint = extrapoint;
	}
	public int getFmsld() {
		return fmsld;
	}

	public void setFmsld(Integer fmsld) {
		this.fmsld = fmsld;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("RoleTableNew [role_id=");
		builder.append(role_id);
		builder.append(", user_id=");
		builder.append(user_id);
		builder.append(", species_id=");
		builder.append(species_id);
		builder.append(", summoning_id=");
		builder.append(summoning_id);
		builder.append(", gang_id=");
		builder.append(gang_id);
		builder.append(", mount_id=");
		builder.append(mount_id);
		builder.append(", troop_id=");
		builder.append(troop_id);
		builder.append(", race_id=");
		builder.append(race_id);
		builder.append(", skill_id=");
		builder.append(skill_id);
		builder.append(", booth_id=");
		builder.append(booth_id);
		builder.append(", task_id=");
		builder.append(task_id);
		builder.append(", hp=");
		builder.append(hp);
		builder.append(", mp=");
		builder.append(mp);
		builder.append(", gold=");
		builder.append(gold);
		builder.append(", codecard=");
		builder.append(codecard);
		builder.append(", experience=");
		builder.append(experience);
		builder.append(", grade=");
		builder.append(grade);
		builder.append(", prestige=");
		builder.append(prestige);
		builder.append(", pkrecord=");
		builder.append(pkrecord);
		builder.append(", rolename=");
		builder.append(rolename);
		builder.append(", title=");
		builder.append(title);
		builder.append(", path=");
		builder.append(path);
		builder.append(", sex=");
		builder.append(sex);
		builder.append(", localname=");
		builder.append(localname);
		builder.append(", skill=");
		builder.append(skill);
		builder.append(", x=");
		builder.append(x);
		builder.append(", y=");
		builder.append(y);
		builder.append(", mapid=");
		builder.append(mapid);
		builder.append(", uptime=");
		builder.append(uptime);
		builder.append(", gangpost=");
		builder.append(gangpost);
		builder.append(", achievement=");
		builder.append(achievement);
		builder.append(", contribution=");
		builder.append(contribution);
		builder.append(", bone=");
		builder.append(bone);
		builder.append(", spir=");
		builder.append(spir);
		builder.append(", power=");
		builder.append(power);
		builder.append(", speed=");
		builder.append(speed);
		builder.append(", canpoint=");
		builder.append(canpoint);
		builder.append(", captain=");
		builder.append(captain);
		builder.append(", savegold=");
		builder.append(savegold);
		builder.append(", password=");
		builder.append(password);
		builder.append(", gangname=");
		builder.append(gangname);
		builder.append(", havebaby=");
		builder.append(havebaby);
		builder.append(", newrole=");
		builder.append(newrole);
		builder.append(", racename=");
		builder.append(racename);
		builder.append(", maxexp=");
		builder.append(maxexp);
		builder.append(", marryobject=");
		builder.append(marryobject);
		builder.append(", skills=");
		builder.append(skills);
		builder.append(", timinggood=");
		builder.append(timinggood);
		builder.append(", turnaround=");
		builder.append(turnaround);
		builder.append(", taskdaily=");
		builder.append(taskdaily);
		builder.append(", born=");
		builder.append(born);
		builder.append(", resistance=");
		builder.append(resistance);
		builder.append(", servermestring=");
		builder.append(servermestring);
		builder.append(", taskreceive=");
		builder.append(taskreceive);
		builder.append(", taskcomplete=");
		builder.append(taskcomplete);
		builder.append(", taskdata=");
		builder.append(taskdata);
		builder.append(", fighting=");
		builder.append(fighting);
		builder.append(", dbexp=");
		builder.append(dbexp);
		builder.append(", score=");
		builder.append(score);
		builder.append(", kill=");
		builder.append(kill);
		builder.append(", babyid=");
		builder.append(babyid);
		builder.append(", drawing=");
		builder.append(drawing);
		builder.append(", calm=");
		builder.append(calm);
		builder.append(", xiuwei=");
		builder.append(xiuwei);
		builder.append(", fmsld=");
		builder.append(fmsld);
		builder.append(", extrapoint=");
		builder.append(extrapoint);
		builder.append("]");
		return builder.toString();
	}

}
