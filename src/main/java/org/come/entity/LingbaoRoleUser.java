package org.come.entity;

import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 * @time 2019年7月4日11:29:58
 */
public class LingbaoRoleUser {

	private BigDecimal baoid;
	private String baoname;
	private String gethard;
	private String baotype;
	private Integer baoactive;
	private String baospeed;
	private String baoreply;
	private String baoshot;
	private String baoap;
	private String resistshot;
	private String assistance;
	private String goodskill;
	private BigDecimal roleid;
	private String skin;
	private Integer skillsum;
	private Integer fusum;
	private BigDecimal lingbaolvl;
	private BigDecimal lingbaoexe;
	private long lingbaoqihe;
	private String skills;
	private String kangxing;
	private int equipment;
	private String baoquality;
	private String tianfuskill;
	private String fushis;
	private String rolename;
	private BigDecimal user_id;
	private String username;

	// --
	private Integer start;
	private Integer end;
	private String orderBy;
	private Integer pageNow;

	public LingbaoRoleUser() {
		// TODO Auto-generated constructor stub
	}

	public BigDecimal getBaoid() {
		return baoid;
	}

	public void setBaoid(BigDecimal baoid) {
		this.baoid = baoid;
	}

	public String getBaoname() {
		return baoname;
	}

	public void setBaoname(String baoname) {
		this.baoname = baoname;
	}

	public String getGethard() {
		return gethard;
	}

	public void setGethard(String gethard) {
		this.gethard = gethard;
	}

	public String getBaotype() {
		return baotype;
	}

	public void setBaotype(String baotype) {
		this.baotype = baotype;
	}

	public Integer getBaoactive() {
		return baoactive;
	}

	public void setBaoactive(Integer baoactive) {
		this.baoactive = baoactive;
	}

	public String getBaospeed() {
		return baospeed;
	}

	public void setBaospeed(String baospeed) {
		this.baospeed = baospeed;
	}

	public String getBaoreply() {
		return baoreply;
	}

	public void setBaoreply(String baoreply) {
		this.baoreply = baoreply;
	}

	public String getBaoshot() {
		return baoshot;
	}

	public void setBaoshot(String baoshot) {
		this.baoshot = baoshot;
	}

	public String getBaoap() {
		return baoap;
	}

	public void setBaoap(String baoap) {
		this.baoap = baoap;
	}

	public String getResistshot() {
		return resistshot;
	}

	public void setResistshot(String resistshot) {
		this.resistshot = resistshot;
	}

	public String getAssistance() {
		return assistance;
	}

	public void setAssistance(String assistance) {
		this.assistance = assistance;
	}

	public String getGoodskill() {
		return goodskill;
	}

	public void setGoodskill(String goodskill) {
		this.goodskill = goodskill;
	}

	public BigDecimal getRoleid() {
		return roleid;
	}

	public void setRoleid(BigDecimal roleid) {
		this.roleid = roleid;
	}

	public String getSkin() {
		return skin;
	}

	public void setSkin(String skin) {
		this.skin = skin;
	}

	public Integer getSkillsum() {
		return skillsum;
	}

	public void setSkillsum(Integer skillsum) {
		this.skillsum = skillsum;
	}

	public Integer getFusum() {
		return fusum;
	}

	public void setFusum(Integer fusum) {
		this.fusum = fusum;
	}

	public BigDecimal getLingbaolvl() {
		return lingbaolvl;
	}

	public void setLingbaolvl(BigDecimal lingbaolvl) {
		this.lingbaolvl = lingbaolvl;
	}

	public BigDecimal getLingbaoexe() {
		return lingbaoexe;
	}

	public void setLingbaoexe(BigDecimal lingbaoexe) {
		this.lingbaoexe = lingbaoexe;
	}

	public long getLingbaoqihe() {
		return lingbaoqihe;
	}

	public void setLingbaoqihe(long lingbaoqihe) {
		this.lingbaoqihe = lingbaoqihe;
	}

	public String getSkills() {
		return skills;
	}

	public void setSkills(String skills) {
		this.skills = skills;
	}

	public String getKangxing() {
		return kangxing;
	}

	public void setKangxing(String kangxing) {
		this.kangxing = kangxing;
	}

	public int getEquipment() {
		return equipment;
	}

	public void setEquipment(int equipment) {
		this.equipment = equipment;
	}

	public String getBaoquality() {
		return baoquality;
	}

	public void setBaoquality(String baoquality) {
		this.baoquality = baoquality;
	}

	public String getTianfuskill() {
		return tianfuskill;
	}

	public void setTianfuskill(String tianfuskill) {
		this.tianfuskill = tianfuskill;
	}

	public String getFushis() {
		return fushis;
	}

	public void setFushis(String fushis) {
		this.fushis = fushis;
	}

	public String getRolename() {
		return rolename;
	}

	public void setRolename(String rolename) {
		this.rolename = rolename;
	}

	public BigDecimal getUser_id() {
		return user_id;
	}

	public void setUser_id(BigDecimal user_id) {
		this.user_id = user_id;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public Integer getStart() {
		return start;
	}

	public void setStart(Integer start) {
		this.start = start;
	}

	public Integer getEnd() {
		return end;
	}

	public void setEnd(Integer end) {
		this.end = end;
	}

	public String getOrderBy() {
		return orderBy;
	}

	public void setOrderBy(String orderBy) {
		this.orderBy = orderBy;
	}

	public Integer getPageNow() {
		return pageNow;
	}

	public void setPageNow(Integer pageNow) {
		this.pageNow = pageNow;
	}

}
