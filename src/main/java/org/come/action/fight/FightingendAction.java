package org.come.action.fight;


import come.tool.FightingData.ManData;
import io.netty.channel.ChannelHandlerContext;

import org.come.action.IAction;
import org.come.bean.LoginResult;
import org.come.entity.RoleSummoning;
import org.come.server.GameServer;

import come.tool.Battle.BattleData;
import come.tool.Battle.BattleThreadPool;
import org.come.until.AllServiceUtil;

import java.math.BigDecimal;

/**
 * 战斗结束,
 * <AUTHOR>
 * @date 2017年11月24日 下午11:31:12
 * 
 */ 
public class FightingendAction implements IAction {
	static int size=0;
	@Override
	public void action(ChannelHandlerContext ctx, String message) {
		LoginResult roleinfo=GameServer.getAllLoginRole().get(ctx);	
		if (roleinfo==null) {return;}
		BattleData battleData=BattleThreadPool.BattleDatas.get(roleinfo.getFighting());
		if (battleData!=null){
			battleData.getParticipantlist().remove(roleinfo.getRolename());
			BattleThreadPool.sendBattleState(0,roleinfo.getRolename());
			for (int i = battleData.getBattlefield().fightingdata.size()-1; i >=0; i--) {
				//逃跑单独计算Hp mp
				ManData data=battleData.getBattlefield().fightingdata.get(i);
				if(data.getManname().equals(roleinfo.getRolename()) && data.getStates() == 2){
					if (data.getHp()<=0) {
						roleinfo.setHp(new BigDecimal((int)(data.getHp_z()*0.25)));
						roleinfo.setMp(new BigDecimal((int)(data.getMp_z()*0.25)));
					}else {
						roleinfo.setHp(new BigDecimal(data.getHp()>0?data.getHp():1));
						roleinfo.setMp(new BigDecimal(data.getMp()));
					}

					if (battleData.getBattlefield().isFightType()) {
						RoleSummoning pet=data.getPet(true, true,true,true);
						if (pet!=null) {
							AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
						}
					}
					if(roleinfo.getRoleData().getLimit("VIP")!=null){
						RoleSummoning pet=data.vipUpdatePet();
						if (pet!=null) {
							AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
						}
					}
					break;

				}


			}

		}
		roleinfo.setFighting(0);
		
	}
}
