package org.come.action.Fly;

import come.tool.Role.RoleData;
import come.tool.Role.RolePool;
import io.netty.channel.ChannelHandlerContext;
import org.come.action.IAction;
import org.come.bean.LoginResult;
import org.come.entity.Fly;

import org.come.server.GameServer;
import org.come.until.AllServiceUtil;
import org.come.until.GsonUtil;

public class FlyUpdateAction implements IAction {
    public void action(ChannelHandlerContext ctx, String message){

    }
}
