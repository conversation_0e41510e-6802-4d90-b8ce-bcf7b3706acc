
package com.gl.controller;

import com.github.pagehelper.PageInfo;
import com.gl.model.Param;
import com.gl.model.Result;
import com.gl.service.PlayerService;
import com.gl.service.ResultFactory;
import com.gl.token.UserToken;
import org.come.entity.Goodsrecord;
import org.come.until.AllServiceUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
public class PlayerController {
   public PlayerController() {
   }

   @UserToken
   @PostMapping({"/api/role"})
   public Result roles(Param param, HttpServletRequest request) {
      // 获取用户名密码格式为 用户名|&|密码
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
      return ResultFactory.success(service.getRole(param));
   }

   /**
    * 查询所有玩家角色（新）
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/roleList")
   public Result roleList(@RequestBody Map<String, Object> params, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      Param param = new Param();
      param.setPageNum(Integer.parseInt(params.get("PageNum").toString()));
      param.setPageSize(Integer.parseInt(params.get("PageSize").toString()));
      param.setValue1(params.get("Value1").toString());
      param.setValue2(params.get("Value2").toString());
      PlayerService service = new PlayerService();
      return ResultFactory.success(service.getRole(param));
   }
   /**
    ** 修改解锁码(新)
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/updatelockpwd")
   public Result updatelockpwd(@RequestBody Map<String, Object> params, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      Param param = new Param();
      param.setValue1(params.get("value1").toString());
      param.setValue2(params.get("value2").toString());

      PlayerService service = new PlayerService();
      if (service.editLockPassword(param)) {
         return ResultFactory.success(null);
      }
      return ResultFactory.fail("操作失败");
   }
   @UserToken
   @PostMapping({"/api/lockpwd"})
   public Result lockpwd(Param param, HttpServletRequest request) {
      // 获取用户名密码格式为 用户名|&|密码
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
      return service.editLockPassword(param) ? ResultFactory.success(null) : ResultFactory.fail("操作失败");
   }

   /**
    * 设置GM特权
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/updateGMRole")
   public Result updateGMRole(Param param, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
//      if (service.updateGMRole(param)) {
//         return ResultFactory.success(null);
//      }
      return ResultFactory.fail("操作失败");
   }
   /**
    * 删除角色
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/deleterole")
   public Result deleterole(Param param, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
//      if (service.deleteRolePwdForRid(param)) {
//         return ResultFactory.success(null);
//      }
      return ResultFactory.fail("操作失败");
   }

   /**
    * 修改密码
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/updatePwdUser")
   public Result updatePwdUser(Param param, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
//      if (service.updatePwdUserForRid(param)) {
//         return ResultFactory.success(null);
//      }
      return ResultFactory.fail("操作失败");
   }

   /**
    * 修改角色坐骑
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/updateMount")
   public Result updateMount(Param param, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
//      if (service.updateMountForRid(param)) {
//         return ResultFactory.success(null);
//      }
      return ResultFactory.fail("操作失败");
   }

   /**
    * 查询交易记录
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/selectGoodsRecord")
   public Result selectGoodsRecord(Param param, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
      return ResultFactory.success(service.selectGoodsRecord(param));
   }
   /**
    * 查询交易记录
    *
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/selectGoodsRecordNew")
   public Result selectGoodsRecordNew(Param param, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PageInfo<Goodsrecord> goodsrecordPageInfo = AllServiceUtil.getGoodsrecordService().selectGoodsRecordNew(param);
      return ResultFactory.success(goodsrecordPageInfo);
   }
   @UserToken
   @PostMapping({"/api/roleoperation"})
   public Result operation(Param param, HttpServletRequest request) {
      // 获取用户名密码格式为 用户名|&|密码
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
      return service.operation(param) ? ResultFactory.success(null) : ResultFactory.fail("操作失败，请确认该玩家是否存在");
   }

   @UserToken
   @PostMapping({"/api/recharge"})
   public Result recharge(Param param, HttpServletRequest request) {
      // 获取用户名密码格式为 用户名|&|密码
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
      return service.rechargeCallBack(param) ? ResultFactory.success(null) : ResultFactory.fail("操作失败");
   }

   @UserToken
   @PostMapping({"/api/rechargeinfo"})
   public Result rechargeinfo(Param param, HttpServletRequest request) {
      // 获取用户名密码格式为 用户名|&|密码
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
      return ResultFactory.success(service.getReceipts(param));
   }
   /**
    * 授权充值
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/userAdminRecharge")
   public Result userAdminRecharge(Param param, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
//      PlayerService service = new PlayerService();
//      if (service.userAdminRechargeCallBack(param)) {
//         return ResultFactory.success(null);
//      }
      return ResultFactory.fail("操作失败");
   }

   /**
    * 发卡人员列表管理
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/adminUserList")
   public Result adminUserList(Param param, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
//      return ResultFactory.success(service.adminUserList(param));
      return ResultFactory.success(null);
   }
   /**
    * 发卡人员添加
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/insertUser")
   public Result insertUser(Param param, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
//      return ResultFactory.success(service.insertUser(param));
      return ResultFactory.success(null);
   }
   /**
    * 发卡人员删除
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/deleteUser")
   public Result deleteUser(Param param, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      Map<String, Object> params = new HashMap<>();
      params.put("ACCOUNT", param.getValue1());
      PlayerService service = new PlayerService();
//      return ResultFactory.success(service.deleteUser(params));
      return ResultFactory.success(null);
   }
   /**
    * 额度修改
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/updateUserAmount")
   public Result updateUserAmount(Param param, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
//      return ResultFactory.success(service.updateUserAmount(param));
      return ResultFactory.success(null);
   }

   /**
    * 查询配置数据库
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/selectConfigure")
   public Result selectConfigure(Param param, HttpServletRequest request) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      PlayerService service = new PlayerService();
//      return ResultFactory.success(service.selectConfigure());
      return ResultFactory.success(null);
   }

   /**
    * 查询配置数据库
    * @param param
    * @return
    */
   @UserToken
   @PostMapping(value = "/api/updateConfigure")
   public Result updateConfigure(HttpServletRequest request,Param param) {
      Result ipCheckResult = UserController.IPstop(request);
      if (ipCheckResult != null) {
         return ipCheckResult;
      }
      String fsd = request.getParameter("fsd");
      String cjlzg = request.getParameter("cjlzg");
      if(fsd!=null &&fsd!="") {
         param.setValue1(fsd);
      }
      if(cjlzg!=null &&cjlzg!="") {
         param.setValue2(cjlzg);
      }
      PlayerService service = new PlayerService();
//      return ResultFactory.success(service.updateConfigure(param));
      return ResultFactory.success(null);
   }
}
