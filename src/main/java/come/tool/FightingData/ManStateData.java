package come.tool.FightingData;

import java.util.List;

//TODO ս�����ݣ��ˡ��衢���ӡ��鱦��

/**
 * ս������������
 *
 * <AUTHOR>
 */

public class ManStateData implements Cloneable {
	private int type;// ���� 0��� 1�ٻ��� 2Ұ�� 3�鱦 4С��
	private int id;// id
	private int camp;// ս��1��Ӫ
	private int man;// ս��λ�� 1��� 6�ٻ��� 11�鱦 16С��
	private String manname;// ����
	private int States;// ��ǰ״̬ 0��ʾ���� 1��ʾ���� 2��ʾ�Ѿ�����
	private List<AddState> addStates;// ����״̬����

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getCamp() {
		return camp;
	}

	public void setCamp(int camp) {
		this.camp = camp;
	}

	public int getMan() {
		return man;
	}

	public void setMan(int man) {
		this.man = man;
	}

	public String getManname() {
		return manname;
	}

	public void setManname(String manname) {
		this.manname = manname;
	}

	public int getStates() {
		return States;
	}

	public void setStates(int states) {
		States = states;
	}

	public List<AddState> getAddStates() {
		return addStates;
	}

	public void setAddStates(List<AddState> addStates) {
		this.addStates = addStates;
	}
}
