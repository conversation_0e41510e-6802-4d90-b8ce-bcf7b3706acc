/*
*
* <PERSON><PERSON> for Bootstrap and Less
*
* Converted by @whitetruffle (http://www.twitter.com/whitetruffle)
* from @joeylomanto's SASS fork (http://chosen-sass-bootstrap.herokuapp.com/)
* Havest: @harvest
*
*/
/*!
 * Bootstrap v2.1.1
 *
 * Copyright 2012 Twitter, Inc
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Designed and built with all the love in the world @twitter by @mdo and @fat.
 */

.chzn-container {
  position: relative;
  display: inline-block;
  zoom: 1;
  *display: inline;
}
.chzn-container .chzn-drop {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15);
  box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15);
  background: #fff;
  border: 1px solid #CCC;
  position: absolute;
  top: 29px;
  left: 0;
  z-index: 1010;
  margin: 4px 0 0;
}

.chzn-container-single .chzn-single {
  margin-top: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  -webkit-box-shadow: 0 0 3px #ffffff inset, 0 1px 1px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 0 3px #ffffff inset, 0 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 3px #ffffff inset, 0 1px 1px rgba(0, 0, 0, 0.1);
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding-box;
  background-clip: padding-box;
  background-color: #f6f6f6;
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), color-stop(50%, #f6f6f6), to(#f4f4f4));
  background-image: -webkit-linear-gradient(#ffffff, #f6f6f6 50%, #f4f4f4);
  background-image: -moz-linear-gradient(top, #ffffff, #f6f6f6 50%, #f4f4f4);
  background-image: -o-linear-gradient(#ffffff, #f6f6f6 50%, #f4f4f4);
  background-image: linear-gradient(#ffffff, #f6f6f6 50%, #f4f4f4);
  background-repeat: no-repeat;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fff4f4f4', GradientType=0);
  -moz-background-clip: padding;
  background-color: #ffffff;
  border: 1px solid #CCC;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  height: 28px;
  line-height: 29px;
  padding: 0 0 0 8px;
  color: #555555;
  text-decoration: none;
}
.chzn-container-single .chzn-single span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 26px;
  display: block;
  overflow: hidden;
  white-space: nowrap;
}
.chzn-container-single .chzn-single abbr {
  display: block;
  position: absolute;
  right: 26px;
  top: 8px;
  width: 12px;
  height: 13px;
  font-size: 1px;
  background: url("../image/chosen-sprite.png") right top no-repeat;
}
.chzn-container-single .chzn-single abbr:hover {
  background-position: right -11px;
}
.chzn-container-single .chzn-single div {
  position: absolute;
  right: 0;
  top: 0;
  display: block;
  height: 100%;
  width: 18px;
}
.chzn-container-single .chzn-single div b {
  background: url("../image/chosen-sprite.png") no-repeat 0 2px;
  display: block;
  width: 100%;
  height: 100%;
}
.chzn-container-single .chzn-search {
  padding: 3px 4px;
  position: relative;
  margin: 0;
  white-space: nowrap;
  z-index: 1010;
}
.chzn-container-single .chzn-search input {
  margin: 1px 0;
  padding: 4px 20px 4px 5px;
  outline: 0;
}
.chzn-container-single .chzn-default {
  color: #999;
}
.chzn-container-single .chzn-drop {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding-box;
  background-clip: padding-box;
  -moz-background-clip: padding;
}
.chzn-container-single.chzn-disabled .chzn-single abbr:hover {
  background-position: right top;
}
.chzn-container-single-nosearch .chzn-search input {
  position: absolute;
  left: -9000px;
}
.chzn-container-multi .chzn-choices {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border linear 0.2s;
  -moz-transition: border linear 0.2s;
  -o-transition: border linear 0.2s;
  transition: border linear 0.2s;
  -webkit-transition: box-shadow linear 0.2s;
  -moz-transition: box-shadow linear 0.2s;
  -o-transition: box-shadow linear 0.2s;
  transition: box-shadow linear 0.2s;
  background-color: #ffffff !important;
  border: 1px solid #cccccc;
  cursor: text;
  overflow: hidden;
  height: auto !important;
  height: 1%;
  position: relative;
  display: block;
  padding: 0;
  margin: 0;
  font-size: 14px;
  line-height: 20px;
  color: #555555;
}
.chzn-container-multi .chzn-choices li {
  float: left;
  list-style: none;
}
.chzn-container-multi .chzn-choices .search-field {
  white-space: nowrap;
  margin: 0;
  padding: 0;
}
.chzn-container-multi .chzn-choices .search-field input {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  color: #555555;
  background: transparent !important;
  border: 0 !important;
  font-family: sans-serif;
  font-size: 100%;
  height: 15px;
  padding: 5px;
  margin: 1px 0 2px;
  outline: 0;
}
.chzn-container-multi .chzn-choices .search-field .default {
  color: #999;
}
.chzn-container-multi .chzn-choices .search-choice {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  -webkit-box-shadow: 0 0 2px #ffffff inset, 0 1px 0 rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0 0 2px #ffffff inset, 0 1px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 2px #ffffff inset, 0 1px 0 rgba(0, 0, 0, 0.05);
  background-color: #f6f6f6;
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), color-stop(50%, #f6f6f6), to(#f4f4f4));
  background-image: -webkit-linear-gradient(#ffffff, #f6f6f6 50%, #f4f4f4);
  background-image: -moz-linear-gradient(top, #ffffff, #f6f6f6 50%, #f4f4f4);
  background-image: -o-linear-gradient(#ffffff, #f6f6f6 50%, #f4f4f4);
  background-image: linear-gradient(#ffffff, #f6f6f6 50%, #f4f4f4);
  background-repeat: no-repeat;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fff4f4f4', GradientType=0);
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding-box;
  background-clip: padding-box;
  -moz-background-clip: padding;
  color: #555555;
  border: 1px solid #999999;
  line-height: 13px;
  padding: 3px 20px 3px 5px;
  margin: 3px 0 3px 5px;
  position: relative;
  cursor: default;
}
.chzn-container-multi .chzn-choices .search-choice .search-choice-close {
  display: block;
  position: absolute;
  right: 3px;
  top: 4px;
  width: 12px;
  height: 13px;
  font-size: 1px;
  background: url("../image/chosen-sprite.png") right top no-repeat;
}
.chzn-container-multi .chzn-choices .search-choice .search-choice-close:hover {
  background-position: right -11px;
}
.chzn-container-multi .chzn-choices .search-choice.search-choice-disabled {
  background-color: #eeeeee;
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f4f4f4), color-stop(50%, #f0f0f0), to(#e8e8e8));
  background-image: -webkit-linear-gradient(#f4f4f4, #f0f0f0 50%, #e8e8e8);
  background-image: -moz-linear-gradient(top, #f4f4f4, #f0f0f0 50%, #e8e8e8);
  background-image: -o-linear-gradient(#f4f4f4, #f0f0f0 50%, #e8e8e8);
  background-image: linear-gradient(#f4f4f4, #f0f0f0 50%, #e8e8e8);
  background-repeat: no-repeat;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff4f4f4', endColorstr='#ffe8e8e8', GradientType=0);
  color: #666;
  border: 1px solid #cccccc;
  padding-right: 5px;
}
.chzn-container-multi .chzn-choices .search-choice-focus {
  background: #d4d4d4;
}
.chzn-container-multi .chzn-choices .search-choice-focus .search-choice-close {
  background-position: right -11px;
}
.chzn-container .chzn-results {
  margin: 0 4px 4px 0;
  max-height: 240px;
  padding: 0 0 0 4px;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.chzn-container .chzn-results li {
  display: none;
  line-height: 15px;
  padding: 5px 6px;
  margin: 0;
  list-style: none;
}
.chzn-container .chzn-results li em {
  background: #feffde;
  font-style: normal;
}
.chzn-container .chzn-results .no-results {
  background: #f4f4f4;
  display: list-item;
}
.chzn-container .chzn-results .group-result {
  cursor: default;
  color: #999;
  font-weight: bold;
}
.chzn-container .chzn-results .group-option {
  padding-left: 15px;
}
.chzn-container .chzn-results .active-result {
  cursor: pointer;
  display: list-item;
}
.chzn-container .chzn-results .highlighted {
  background-color: #326dcc;
  background-image: -moz-linear-gradient(top, #3875d7, #2a62bc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#3875d7), to(#2a62bc));
  background-image: -webkit-linear-gradient(top, #3875d7, #2a62bc);
  background-image: -o-linear-gradient(top, #3875d7, #2a62bc);
  background-image: linear-gradient(to bottom, #3875d7, #2a62bc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff3875d7', endColorstr='#ff2a62bc', GradientType=0);
  color: #fff;
}
.chzn-container .chzn-results .highlighted em {
  background: transparent;
}
.chzn-container .chzn-results-scroll {
  background: white;
  margin: 0 4px;
  position: absolute;
  text-align: center;
  width: 321px;
  /* This should by dynamic with js */

  z-index: 1;
}
.chzn-container .chzn-results-scroll span {
  display: inline-block;
  height: 17px;
  text-indent: -5000px;
  width: 9px;
}
.chzn-container .chzn-results-scroll-down {
  bottom: 0;
}
.chzn-container .chzn-results-scroll-down span {
  background: url("../image/chosen-sprite.png") no-repeat -4px -3px;
}
.chzn-container .chzn-results-scroll-up span {
  background: url("../image/chosen-sprite.png") no-repeat -22px -3px;
}
.chzn-container-multi .chzn-results {
  margin: 4px;
  padding: 0;
}
.chzn-container-multi .chzn-drop .result-selected {
  display: none;
}
.chzn-container-active .chzn-single {
  border: 1px solid #CCC;
}
.chzn-container-active .chzn-single .chzn-drop {
  margin-top: -29px;
}
.chzn-container-active .chzn-single-with-drop {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  border-color: #CCC;
}
.chzn-container-active .chzn-single-with-drop div {
  background: transparent;
  border-left: none;
}
.chzn-container-active .chzn-single-with-drop div b {
  background-position: -18px 1px;
}
.chzn-container-active .chzn-choices {
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
  border: 1px solid rgba(82, 168, 236, 0.8);
  outline: 0;
  outline: thin dotted \9;
  /* IE6-9 */

}
.chzn-container-active .chzn-choices .search-field input {
  color: #111 !important;
}
.chzn-disabled {
  opacity: 0.005;
  filter: alpha(opacity=0.5);
  cursor: default;
}
.chzn-disabled .chzn-single {
  cursor: default;
}
.chzn-disabled .search-choice-close {
  cursor: default;
}
.chzn-rtl {
  text-align: right;
}
.chzn-rtl .chzn-single {
  padding: 0 8px 0 0;
  overflow: visible;
}
.chzn-rtl .chzn-single span {
  margin-left: 26px;
  margin-right: 0;
  direction: rtl;
}
.chzn-rtl .chzn-single div {
  left: 3px;
  right: auto;
}
.chzn-rtl .chzn-single abbr {
  left: 26px;
  right: auto;
}
.chzn-rtl .chzn-choices li {
  float: right;
}
.chzn-rtl .chzn-choices .search-field input {
  direction: rtl;
}
.chzn-rtl .chzn-choices .search-choice {
  padding: 3px 5px 3px 19px;
  margin: 3px 5px 3px 0;
}
.chzn-rtl .chzn-choices .search-choice .search-choice-close {
  left: 4px;
  right: auto;
  background-position: right top;
}
.chzn-rtl .chzn-results .group-option {
  padding-left: 0;
  padding-right: 15px;
}
.chzn-rtl .chzn-search input {
  padding: 4px 5px 4px 20px;
  direction: rtl;
}
.chzn-rtl.chzn-container-single .chzn-results {
  margin: 0 0 4px 4px;
  padding: 0 4px 0 0;
}
.chzn-rtl.chzn-container-active .chzn-single-with-drop div {
  border-right: none;
}
