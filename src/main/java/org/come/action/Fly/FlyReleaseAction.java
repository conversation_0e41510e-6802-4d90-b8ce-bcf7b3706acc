package org.come.action.Fly;

import io.netty.channel.ChannelHandlerContext;
import org.come.action.IAction;
import org.come.until.AllServiceUtil;

import java.math.BigDecimal;

public class FlyReleaseAction implements IAction {
    public void action(ChannelHandlerContext ctx, String message){

//        AllServiceUtil.getFlyService().deleteFlysByMid(new BigDecimal(message));
//        AllServiceUtil.getMountskillService().deleteMountskills(new BigDecimal(message));
//

    }
}
