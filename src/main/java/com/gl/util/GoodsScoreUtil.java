package com.gl.util;

import org.apache.commons.lang.StringUtils;
import org.come.entity.Goodstable;
import org.come.tool.Goodtype;
import org.come.until.AllServiceUtil;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class GoodsScoreUtil {
	private static final Logger logger = Logger.getLogger(GoodsScoreUtil.class.getName());

	public static void main(String[] args) {
		// Main method can be used for testing
	}

	public static int addCountGoodstableScore(Goodstable goodstable) {
		if (logger.isLoggable(Level.FINE) && goodstable.getRgid().equals(new BigDecimal(23095000))) {
			logger.fine(goodstable.getGoodsname() + "===" + goodstable.getRgid());
		}

		List<Goodstable> goodsList = AllServiceUtil.getGoodsTableService().getGoodsByRoleID(goodstable.getRole_id());
		if (goodsList == null) {
			return 0;
		}

		Map<BigDecimal, Goodstable> datagoodsDB = new HashMap<>();
		for (Goodstable good : goodsList) {
			datagoodsDB.put(good.getRgid(), good);
		}

		int goodScore = 0;

		// Calculate score based on QHT and QHV
		if (goodstable.getQht() != null && goodstable.getQht() > 0) {
			goodScore += goodstable.getQht() * SCORE_MULTIPLIER_QHT;
			if (goodstable.getQhv() != null && goodstable.getQhv() > 0) {
				goodScore += calculateQhvScore(goodstable.getQht(), goodstable.getQhv());
			}
		}

		if (StringUtils.isNotEmpty(goodstable.getValue())) {
			String[] valve = goodstable.getValue().split("\\|");
			for (String vs : valve) {
				long type = goodstable.getType();
				if (Goodtype.GodEquipment_xian(type)) {
					goodScore += parseAndCalculateScore(vs, "阶数", SCORE_MULTIPLIER_XIAN);
					goodScore += parseAndCalculateScore(vs, "等级", SCORE_MULTIPLIER_XIAN);
				} else if (Goodtype.GodEquipment_God(type)) {
					goodScore += parseAndCalculateScore(vs, "等级", SCORE_MULTIPLIER_GOD);
				} else if (Goodtype.OrdinaryEquipment(type)) {
					goodScore += parseAndCalculateScore(vs, "等级", SCORE_MULTIPLIER_ORDINARY);
				}

				if (vs.contains("炼化属性") || vs.contains("兽魂属性") || vs.contains("炼器属性") || vs.contains("神兵属性") || vs.contains("宝石镶嵌")) {
					goodScore += processSpecialAttributes(vs, datagoodsDB);
				} else if (!vs.contains("套装属性")) {
					goodScore += parseAndCalculateGeneralScore(vs);
				}
			}
		}

		return goodScore;
	}

	private static int calculateQhvScore(int qht, int qhv) {
		if (qhv < 4) {
			return qht * 2;
		} else if (qhv < 7) {
			return qht * 4;
		} else if (qhv < 10) {
			return qht * 8;
		} else if (qhv < 14) {
			return qht * 16;
		} else {
			return qht * 32;
		}
	}

	private static int parseAndCalculateScore(String vs, String key, int multiplier) {
		try {
			if (vs.split("=").length > 1 && isNumber(vs.split("=")[1]) && vs.split("=")[0].equals(key)) {
				return (Integer.parseInt(vs.split("=")[1]) * Integer.parseInt(vs.split("=")[1])) * multiplier;
			}
		} catch (NumberFormatException e) {
			logger.warning("Invalid number format: " + vs);
		}
		return 0;
	}

	private static int processSpecialAttributes(String vs, Map<BigDecimal, Goodstable> datagoodsDB) {
		int score = 0;
		String[] va = vs.split("&");
		for (String v : va) {
			score += parseSpecialAttribute(v);
			if (v.contains("宝石属性") && !v.equals("宝石属性")) {
				score += processGemstoneAttributes(v, datagoodsDB);
			}
		}
		return score;
	}

	private static int parseSpecialAttribute(String v) {
		int score = 0;
		try {
			if (v.contains("敏捷") || v.contains("附加速度") || v.contains("速度")) {
				score += (int) (v.contains("敏捷") ? Double.parseDouble(v.split("=")[1]) * 15 : (Math.abs(Double.parseDouble(v.split("=")[1])) / 2) * 15);
			}
			if (v.contains("忽视") || v.contains("忽视抗")) {
				score += (int) (v.contains("忽视抗混") || v.contains("忽视抗睡") || v.contains("忽视抗封") || v.contains("忽视遗忘") ?
                                        Double.parseDouble(v.split("=")[1]) * 20 : Double.parseDouble(v.split("=")[1]) * 10);
			}
			if (v.contains("抗") && !v.contains("三尸虫")) {
				double value = Double.parseDouble(v.split("=")[1]);
				score += (int) (value > 100 ? value * 0.5 : value * 5);
			}
			if (v.contains("抗三尸虫")) {
				score += (int) (Double.parseDouble(v.split("=")[1]) * 0.5);
			}
			if (v.contains("加强") && !v.contains("加强三尸虫") && !v.contains("回血值")) {
				score += (int) (Double.parseDouble(v.split("=")[1]) * 10);
			}
			if (v.contains("加强三尸虫")) {
				score += (int) (Double.parseDouble(v.split("=")[1]) * 0.5);
			}
			if (v.contains("强力克")) {
				score += (int) (Double.parseDouble(v.split("=")[1]) * 10);
			}
			if (v.contains("致命")) {
				score += (int) (Double.parseDouble(v.split("=")[1]) * 5);
			}
			if (v.contains("狂暴程度")) {
				score += (int) (Double.parseDouble(v.split("=")[1]) * 10);
			} else if (v.contains("狂暴")) {
				score += (int) (Double.parseDouble(v.split("=")[1]) * 25);
			}
			if (v.contains("加强攻击")) {
				score += (int) (Double.parseDouble(v.split("=")[1]) * 10);
			}
		} catch (NumberFormatException e) {
			logger.warning("Invalid number format: " + v);
		}
		return score;
	}

	private static int processGemstoneAttributes(String v, Map<BigDecimal, Goodstable> datagoodsDB) {
		int score = 0;
		try {
			Goodstable good2 = datagoodsDB.get(new BigDecimal(v));
			if (good2 != null) {
				String[] bs = good2.getValue().split("\\|");
				for (String string : bs) {
					if (string.split("=").length > 1 && isNumber(string.split("=")[1])) {
						if (string.split("=")[0].equals("等级")) {
							int level = Integer.parseInt(string.split("=")[1]);
							score += level > 8 ? level * 5 * 15 : level * 2 * 10;
						}
						if (string.split("=")[0].equals("价值")) {
							int value = Integer.parseInt(string.split("=")[1]);
							score += value > 102 ? value * 5 : value * 2;
						}
						if (string.contains("敏捷") || string.contains("附加速度") || string.contains("速度")) {
							double speedValue = Double.parseDouble(string.split("=")[1]);
							score += (int) (speedValue < 0 ? (Math.abs(speedValue) / 2) * 25 : speedValue * 25);
						}
						if (string.contains("忽视") || string.contains("忽视抗")) {
							score += (int) (Double.parseDouble(string.split("=")[1]) * 20);
						} else if (string.contains("抗") && !string.contains("三尸虫")) {
							double resistanceValue = Double.parseDouble(string.split("=")[1]);
							score += (int) (resistanceValue > 100 ? resistanceValue * 0.5 : resistanceValue * 5);
						} else if (string.contains("抗三尸虫")) {
							score += (int) (Double.parseDouble(string.split("=")[1]) * 0.5);
						}
						if (string.contains("加强") && !string.contains("加强三尸虫") && !string.contains("回血值")) {
							score += (int) (Double.parseDouble(string.split("=")[1]) * 10);
						} else if (string.contains("加强三尸虫")) {
							score += (int) (Double.parseDouble(string.split("=")[1]) * 0.5);
						}
						if (string.contains("强力克")) {
							score += (int) (Double.parseDouble(string.split("=")[1]) * 10);
						}
						if (string.contains("无属性伤害")) {
							score += (int) (Double.parseDouble(string.split("=")[1]) * 2);
						}
						if (string.contains("致命")) {
							score += (int) (Double.parseDouble(string.split("=")[1]) * 5);
						}
						if (string.contains("狂暴程度")) {
							score += (int) (Double.parseDouble(string.split("=")[1]) * 10);
						} else if (string.contains("狂暴")) {
							score += (int) (Double.parseDouble(string.split("=")[1]) * 25);
						}
						if (string.contains("加强攻击")) {
							score += (int) (Double.parseDouble(string.split("=")[1]) * 10);
						}
					}
				}
			}
		} catch (NumberFormatException e) {
			logger.warning("Invalid number format: " + v);
		}
		return score;
	}

	private static int parseAndCalculateGeneralScore(String vs) {
		int score = 0;
		try {
			if (vs.split("=").length > 1 && isNumber(vs.split("=")[1])) {
				if (vs.contains("敏捷") || vs.contains("附加速度") || vs.contains("速度")) {
					score += (int) (vs.contains("敏捷") ? Double.parseDouble(vs.split("=")[1]) * 15 : (Math.abs(Double.parseDouble(vs.split("=")[1]) / 2)) * 15);
				}
				if (vs.contains("忽视") || vs.contains("忽视抗")) {
					score += (int) (Double.parseDouble(vs.split("=")[1]) * 20);
				} else if (vs.contains("抗") && !vs.contains("三尸虫")) {
					double value = Double.parseDouble(vs.split("=")[1]);
					score += (int) (value > 100 ? value * 0.5 : value * 5);
				} else if (vs.contains("抗三尸虫")) {
					score += (int) (Double.parseDouble(vs.split("=")[1]) * 0.5);
				}
				if (vs.contains("加强") && !vs.contains("加强三尸虫") && !vs.contains("回血值")) {
					score += (int) (Double.parseDouble(vs.split("=")[1]) * 10);
				} else if (vs.contains("加强三尸虫")) {
					score += (int) (Double.parseDouble(vs.split("=")[1]) * 1);
				}
				if (vs.contains("强力克")) {
					score += (int) (Double.parseDouble(vs.split("=")[1]) * 10);
				}
				if (vs.contains("无属性伤害")) {
					score += (int) (Double.parseDouble(vs.split("=")[1]) * 2);
				}
				if (vs.contains("致命")) {
					score += (int) (Double.parseDouble(vs.split("=")[1]) * 5);
				}
				if (vs.contains("狂暴程度")) {
					score += (int) (Double.parseDouble(vs.split("=")[1]) * 10);
				} else if (vs.contains("狂暴")) {
					score += (int) (Double.parseDouble(vs.split("=")[1]) * 25);
				}
				if (vs.contains("加强攻击")) {
					score += (int) (Double.parseDouble(vs.split("=")[1]) * 10);
				}
			}
		} catch (NumberFormatException e) {
			logger.warning("Invalid number format: " + vs);
		}
		return score;

//		if (StringUtils.isNotEmpty(goodstable.getValue())) {
//			String[] vs = goodstable.getValue().split("\\|");
//			for(String s:vs) {
//				int level= Integer.parseInt(s.split("=")[1]);
//				goodScore += level * (level <= 6 ? 500 : level > 6 && level < 9 ? 1000 : level >= 9 ? 1500 : 0);
//			}
//		}
	}

	public static boolean isNumber(String str) {
		// 正则表达式匹配整数或带小数的数字
		String regex = "^[-+]?\\d+(\\.\\d+)?$";
		return str.matches(regex);
	}

	private static final int SCORE_MULTIPLIER_QHT = 10;
	private static final int SCORE_MULTIPLIER_XIAN = 15;
	private static final int SCORE_MULTIPLIER_GOD = 10;
	private static final int SCORE_MULTIPLIER_ORDINARY = 5;
}
