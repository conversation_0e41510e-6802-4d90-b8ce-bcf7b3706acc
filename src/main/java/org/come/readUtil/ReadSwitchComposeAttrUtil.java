package org.come.readUtil;

import org.come.handler.MainServerHandler;
import org.come.model.WitchComposeAttr;
import org.come.servlet.UpXlsAndTxtFile;
import org.come.tool.ReadExelTool;
import org.come.tool.SettModelMemberTool;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class ReadSwitchComposeAttrUtil {
    public static ConcurrentHashMap<String, List<WitchComposeAttr>> getAllData(String path, StringBuffer buffer){
        ConcurrentHashMap<String, List<WitchComposeAttr>> getAlldata = new ConcurrentHashMap<String,List<WitchComposeAttr>>();
        String[][] result = ReadExelTool.getResult("config/"+path+".xls");
        for (int i = 1; i < result.length; i++) {
            if (result[i][0].equals("")) {continue;}

            WitchComposeAttr witchComposeAttr=new WitchComposeAttr();
            for (int j = 0; j < result[i].length; j++) {
                try {
                    SettModelMemberTool.setReflectRelative(witchComposeAttr, result[i][j], j);
                } catch (Exception e) {
                    UpXlsAndTxtFile.addStringBufferMessage(buffer, i, j, result[i][j], MainServerHandler.getErrorMessage(e));
                    return null;
                }
            }
            try {
                if(getAlldata.containsKey(witchComposeAttr.getType())){
                    getAlldata.get(witchComposeAttr.getType()).add(witchComposeAttr);
                }else {
                    List<WitchComposeAttr> witchComposeAttrs = new ArrayList<WitchComposeAttr>();
                    witchComposeAttrs.add(witchComposeAttr);
                    getAlldata.put(witchComposeAttr.getType(),witchComposeAttrs);
                }
            } catch (Exception e) {
                // TODO: handle exception
                UpXlsAndTxtFile.addStringBufferMessage(buffer, i, 0,"解析错误",MainServerHandler.getErrorMessage(e));
                return null;
            }
        }

        return getAlldata;
    }

}
