# 一键多开交任务Bug修复说明

## 问题描述
在使用一键多开功能时，队员号的任务不会自动提交，只有队长的任务能正常完成和领取奖励。

## 问题根因
1. 在 `TaskAction.java` 的 `receive` 方法中，任务奖励领取只处理了当前操作的角色
2. 没有检测一键多开模式，导致队员的任务状态无法同步
3. 队员无法获得相应的任务奖励

## 修复方案

### 修改文件
- `src/main/java/come/tool/newTask/TaskAction.java`

### 主要修改内容

1. **添加一键多开检测**
   ```java
   // 检查是否为一键多开模式
   boolean isMultiopen = ChangeRoleAction.allMapping.get(ctx) != null;
   String[] teams = loginResult.getTeam().split("\\|");
   ```

2. **活跃奖励同步处理**
   - 在活跃奖励领取时，检查一键多开模式
   - 为所有队员同步领取状态和奖励

3. **成就奖励同步处理**
   - 在成就奖励领取时，检查队员是否满足条件
   - 为满足条件的队员自动领取奖励
   - 同步处理称谓奖励

### 修复效果
- 一键多开时，队长领取任务奖励后，队员也会自动获得相应奖励
- 确保所有队员的任务状态保持同步
- 避免队员任务卡住无法完成的问题

## 测试建议
1. 创建一个队伍，使用一键多开功能
2. 完成任务后，检查队员是否也能获得奖励
3. 验证队员的任务状态是否正确更新
4. 测试不同类型的任务奖励（活跃奖励、成就奖励等）

## 注意事项
- 修复只影响一键多开模式下的任务奖励领取
- 不影响正常单人或多人组队的任务系统
- 保持了原有的任务验证逻辑，确保安全性
