package org.come.action.lottery;

import come.tool.Stall.AssetUpdate;
import io.netty.channel.ChannelHandlerContext;
import org.come.action.IAction;
import org.come.bean.LoginResult;
import org.come.handler.SendMessage;
import org.come.model.HDXData;
import org.come.model.HDXRole;
import org.come.protocol.Agreement;
import org.come.server.GameServer;
import org.come.until.GsonUtil;
import org.come.until.HDXUtil;

import java.math.BigDecimal;

public class HDXRoleAction implements IAction {
    private static String tpye;
    @Override
    public void action(ChannelHandlerContext ctx, String message) {
        LoginResult loginResult = GameServer.getAllLoginRole().get(ctx);
//        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("活动暂时关闭"));
//        return;

        // TODO Auto-generated method stub
        if (message.startsWith("HDX")) {//暂时关闭
            setData(ctx,message);
        }
        if (message.startsWith("TCH")) {
            TCroleRecord(loginResult);
        }
    }

    /**储存数据的方法*/
    public void setData(ChannelHandlerContext ctx,String message) {
        LoginResult loginResult = GameServer.getAllLoginRole().get(ctx);
        int mathMaxInt = 0;
        if (HDXUtil.HdxDataList.size() > 0) {
            mathMaxInt = HDXUtil.HdxDataList.stream().mapToInt(HDXData::getStage).max().getAsInt(); //获取开奖期数
        }
//        LocalDateTime now = LocalDateTime.now();
//        if (now.getHour() >= 1 && now.getHour() < 19){
//            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("活动未开启，无法购买！！"));
//            return;
//        }
        if (HDXUtil.getTch()==null&&HDXUtil.getTch1()==null){
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("头奖号还没开启不能购买！！"));
            return;
        }
        for (int i = 0; i < HDXUtil.HdxRoleList.size(); i++) {
            if (HDXUtil.HdxRoleList.get(i).getRole_id().equals(loginResult.getRole_id())) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("你本轮已经下押过了，休想蒙混老夫#24"));
                return;
            }
        }
        if (message==null || message.equals("")) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("#Y请选择要下注的类型，并且输入金额！"));
            return;
        }

        HDXRole hdxRole = new HDXRole();
        String msg = message.split("=")[1];
        String[] m = msg.split("-");
        hdxRole.setPrizeNumber(m[0]);
        hdxRole.setTpye(Integer.parseInt(m[1]));
        hdxRole.setMoney(Long.parseLong(m[2]));
        hdxRole.setRole_id(loginResult.getRole_id());
        hdxRole.setStage(mathMaxInt+1);
        HDXUtil.HdxRoleList.add(hdxRole);
        hdxRole.setIfWin("未开奖");
        HDXUtil.HdxRoleRecordList.add(hdxRole);
        switch (m[0]){
            case "1":
                tpye = "头彩";
                break;
            case "2":
                tpye = "双对";
                break;
            case "3":
                tpye = "七星";
                break;
            case "4":
                tpye = "散星";
                break;
        }
        if (m[1].equals("5")){
            AssetUpdate assetUpdate = new AssetUpdate();
            assetUpdate.updata("D=" + (-Long.parseLong(m[2])));
            loginResult.setGold(loginResult.getGold().add((new BigDecimal(-Long.parseLong(m[2])))));
            SendMessage.sendMessageByRoleName(loginResult.getRolename(), Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("你投入"+ Long.parseLong(m[2]) + "两下押" + tpye + "，各位快快下押啦！"));
        }else if (m[1].equals("6")){
            AssetUpdate assetUpdate = new AssetUpdate();
            assetUpdate.updata("X=" + (-Long.parseLong(m[2])));
            loginResult.setCodecard(loginResult.getCodecard().add((new BigDecimal(-Long.parseLong(m[2])))));
            SendMessage.sendMessageByRoleName(loginResult.getRolename(), Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("你投入"+ Long.parseLong(m[2]) + "两下押" + tpye + "，各位快快下押啦！"));
        }
    }

    public static void TCroleRecord(LoginResult loginResult) {
        String tch = HDXUtil.getTch();
        String tch1 = HDXUtil.getTch1();
        SendMessage.sendMessageByRoleName(loginResult.getRolename(), Agreement.getAgreement().HDXCPAgreement("TCH=" + tch + "-" + tch1));
    }
}
