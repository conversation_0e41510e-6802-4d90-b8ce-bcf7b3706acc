package org.come.action.jilv;

import come.tool.Role.RoleData;
import come.tool.Role.RolePool;
import come.tool.Stall.AssetUpdate;
import io.netty.channel.ChannelHandlerContext;
import org.come.action.IAction;
import org.come.bean.JilvData;
import org.come.bean.LoginResult;
import org.come.entity.Goodstable;
import org.come.handler.SendMessage;
import org.come.protocol.Agreement;
import org.come.server.GameServer;
import org.come.tool.EquipTool;
import org.come.until.AllServiceUtil;
import org.come.until.GsonUtil;

import java.math.BigDecimal;
import java.util.List;


public class JiLvAction implements IAction {


    @Override
    public void action(ChannelHandlerContext ctx, String message) {
        LoginResult loginResult = GameServer.getAllLoginRole().get(ctx);
        if (loginResult == null) {
            return;
        }
        JilvData jilvData = GameServer.roleJilvData.get(loginResult.getRole_id());



        if (jilvData == null ) {
            RoleData roleData= RolePool.getRoleData(loginResult.getRole_id());
            if (roleData.getLimit("VIP")!=null) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("你已经领取过该鸡驴奖励！"));
            }else
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("月卡用户才能领取三个奖励！"));
            return;
        }
        String jilv = jilvData.getJilv();
        if( !jilv.contains(message)) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("你已经领取过该几率奖励！"));
            return;
        }

        String[] split = jilv.split("\\|");
        for (String s : split) {
            if (message.equals(s)) {
                send(new BigDecimal(s), loginResult);
                String s1 = Agreement.getAgreement().JiLvAgreement(GsonUtil.getGsonUtil().getgson().toJson("success|" + s));
                SendMessage.sendMessageToSlef(ctx, s1);
                jilv = jilv.replace(s, "");
                jilvData.setJilv(jilv);
//                GameServer.roleJilvData.remove(loginResult.getRole_id());
            }
        }
        jilvData.setCount(jilvData.getCount() - 1);
        if (jilvData.getCount() == 0) {
            GameServer.roleJilvData.remove(loginResult.getRole_id());
        }
    }


    public void send(BigDecimal goodsId, LoginResult loginResult) {

        Goodstable goodstable = GameServer.getGood(goodsId);
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setMsg("获得" + 1 + "个" + goodstable.getGoodsname());
        goodstable.setRole_id(loginResult.getRole_id());
        if (EquipTool.canSuper(goodstable.getType())) {// 可叠加
//            int sum = yid == sid ? xxgdBean.getSum() : 1;
            int sum = 1;
            // 判断该角色是否拥有这件物品
            List<Goodstable> sameGoodstable = AllServiceUtil.getGoodsTableService().selectGoodsByRoleIDAndGoodsID(loginResult.getRole_id(), goodstable.getGoodsid());
            if (sameGoodstable.size() != 0) {
                // 修改使用次数
                int uses = sameGoodstable.get(0).getUsetime() + sum;
                sameGoodstable.get(0).setUsetime(uses);
                // 修改数据库
                AllServiceUtil.getGoodsTableService().updateGoodRedis(sameGoodstable.get(0));
                assetUpdate.setGood(sameGoodstable.get(0));
                AllServiceUtil.getGoodsrecordService().insert(goodstable, null, 1, -3);
            } else {
                goodstable.setUsetime(sum);
                // 插入数据库
                AllServiceUtil.getGoodsTableService().insertGoods(goodstable);
                assetUpdate.setGood(goodstable);
                AllServiceUtil.getGoodsrecordService().insert(goodstable, null, 1, -3);
            }
        } else {
            goodstable.setUsetime(1);
            AllServiceUtil.getGoodsTableService().insertGoods(goodstable);
            assetUpdate.setGood(goodstable);
            AllServiceUtil.getGoodsrecordService().insert(goodstable, null, 1, -3);
        }

        //玩家在线
        if( GameServer.getRoleNameMap().get(loginResult.getRolename()) != null ){
            SendMessage.sendMessageToSlef(GameServer.getRoleNameMap().get(loginResult.getRolename()), Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
        }
    }


}
