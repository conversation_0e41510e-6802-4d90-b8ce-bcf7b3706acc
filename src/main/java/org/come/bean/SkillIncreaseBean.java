package org.come.bean;

  /**
   * 当使用法术的时候用来保存相应的属性增长幅度值
   * <AUTHOR>
   *
   */

public class SkillIncreaseBean {

	    //物理增益
		private Double rolekwl;

		//震慑增益
		private Double rolekzs;

		//风增益
		private Double rolekff;

		//雷增益
		private Double roleklf;

		//水增益
		private Double roleksf;

		//火增益
		private Double rolekhf;

		//混乱增益
		private Double rolekhl;

		//昏睡增益
		private Double rolekhs;

		//封印增益
		private Double rolekfy;

		//中毒增益
		private Double rolekzd;

		//遗忘增益
		private Double rolekyw;
		
		// 血量增益
		private Double hp;

		// 蓝量增益
		private Double mp;

		// 伤害增益
		private Double ap;

		// 敏捷增益
		private Double sp;


		public Double getHp() {
			return hp;
		}

		public void setHp(Double hp) {
			this.hp = hp;
		}

		public Double getMp() {
			return mp;
		}

		public void setMp(Double mp) {
			this.mp = mp;
		}

		public Double getAp() {
			return ap;
		}

		public void setAp(Double ap) {
			this.ap = ap;
		}

		public Double getSp() {
			return sp;
		}

		public void setSp(Double sp) {
			this.sp = sp;
		}

		public Double getRolekwl() {
			return rolekwl;
		}

		public void setRolekwl(Double rolekwl) {
			this.rolekwl = rolekwl;
		}

		public Double getRolekzs() {
			return rolekzs;
		}

		public void setRolekzs(Double rolekzs) {
			this.rolekzs = rolekzs;
		}

		public Double getRolekff() {
			return rolekff;
		}

		public void setRolekff(Double rolekff) {
			this.rolekff = rolekff;
		}

		public Double getRoleklf() {
			return roleklf;
		}

		public void setRoleklf(Double roleklf) {
			this.roleklf = roleklf;
		}

		public Double getRoleksf() {
			return roleksf;
		}

		public void setRoleksf(Double roleksf) {
			this.roleksf = roleksf;
		}

		public Double getRolekhf() {
			return rolekhf;
		}

		public void setRolekhf(Double rolekhf) {
			this.rolekhf = rolekhf;
		}

		public Double getRolekhl() {
			return rolekhl;
		}

		public void setRolekhl(Double rolekhl) {
			this.rolekhl = rolekhl;
		}

		public Double getRolekhs() {
			return rolekhs;
		}

		public void setRolekhs(Double rolekhs) {
			this.rolekhs = rolekhs;
		}

		public Double getRolekfy() {
			return rolekfy;
		}

		public void setRolekfy(Double rolekfy) {
			this.rolekfy = rolekfy;
		}

		public Double getRolekzd() {
			return rolekzd;
		}

		public void setRolekzd(Double rolekzd) {
			this.rolekzd = rolekzd;
		}

		public Double getRolekyw() {
			return rolekyw;
		}

		public void setRolekyw(Double rolekyw) {
			this.rolekyw = rolekyw;
		}
		
}
