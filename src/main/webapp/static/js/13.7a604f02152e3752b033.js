webpackJsonp([13],{FgY6:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=a("spLH"),s=(a("eNfa"),a("VsUZ")),n={name:"comment",components:{pagination:l.a},data:function(){return{userName:"",czType:"",options:[{value:"1",label:"周卡充值"},{value:"2",label:"月卡充值"},{value:"3",label:"小资冲级礼包充值"},{value:"4",label:"土豪冲级礼包充值"}],tableData:[],pager:{totalCount:0,currentPage:1,pageSize:20}}},methods:{addGold:function(){var e=this,t={type:"gift",userName:this.userName,czType:this.czType};Object(s.e)(t).then(function(t){400==t.status?e.$message.error(t.message):200==t.status?(e.$message.success("操作成功"),e.initData()):e.$message.warning("未知错误")})},initData:function(){var e=this;Object(s.n)({type:"7",count:40}).then(function(t){e.tableData=t.list})}},mounted:function(){this.initData()}},i={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"cm-title"},[e._v("礼包充值")]),e._v(" "),a("div",{staticClass:"link-top"}),e._v(" "),a("div",{staticClass:"cm-search-div"},[e._v("\n    账号："),a("el-input",{staticClass:"cm-search-input",attrs:{placeholder:"请输入账号"},model:{value:e.userName,callback:function(t){e.userName=t},expression:"userName"}}),e._v("\n      礼包类型："),a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.czType,callback:function(t){e.czType=t},expression:"czType"}},e._l(e.options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1),e._v("\n          "),a("span",{staticStyle:{color:"red"}},[e._v("小资 与 土豪 冲级礼包互斥")]),e._v("       \n\n        "),a("el-button",{attrs:{type:"success",size:"small",icon:"el-icon-user-solid"},on:{click:function(t){e.pager.currentPage=1,e.addGold()}}},[e._v("充值")])],1),e._v(" "),a("div",{staticClass:"cm-title"},[e._v("充值记录")]),e._v(" "),a("div",{staticClass:"link-top"}),e._v(" "),a("br"),e._v(" "),a("div",[a("el-table",{attrs:{data:e.tableData,"header-cell-style":{textAlign:"center"},"cell-style":{textAlign:"center"},border:"",stripe:""}},[a("el-table-column",{attrs:{prop:"recordId",label:"充值ID",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"text",label:"充值记录",width:"600"}}),e._v(" "),a("el-table-column",{attrs:{prop:"recordTime",label:"充值时间"}})],1)],1),e._v(" "),a("pagination",{staticClass:"cm-page",attrs:{pager:e.pager},on:{load:function(e){}}})],1)},staticRenderFns:[]};var c=a("VU/8")(n,i,!1,function(e){a("wfXi")},"data-v-5dc8c254",null);t.default=c.exports},wfXi:function(e,t){}});
//# sourceMappingURL=13.7a604f02152e3752b033.js.map