package org.come.bean;

import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 * 每个区月统计报表
 *
 */
public class OneAreaServiceMonthBean {
	private BigDecimal one=new BigDecimal(0);
	private BigDecimal two=new BigDecimal(0);
	private BigDecimal three=new BigDecimal(0);
	private BigDecimal four=new BigDecimal(0);
	private BigDecimal five=new BigDecimal(0);
	private BigDecimal six=new BigDecimal(0);
	private BigDecimal seven=new BigDecimal(0);
	private BigDecimal eight=new BigDecimal(0);
	private BigDecimal nine=new BigDecimal(0);
	private BigDecimal ten=new BigDecimal(0);
	private BigDecimal eleven=new BigDecimal(0);
	private BigDecimal tweer=new BigDecimal(0);
    /**
     * 服务区ID
     */
    private BigDecimal sid;
    
    /***
     * 年份
     * @return
     */
    private String year;
	public BigDecimal getOne() {
		return one;
	}
	public void setOne(BigDecimal one) {
		this.one = one;
	}
	public BigDecimal getTwo() {
		return two;
	}
	public void setTwo(BigDecimal two) {
		this.two = two;
	}
	public BigDecimal getThree() {
		return three;
	}
	public void setThree(BigDecimal three) {
		this.three = three;
	}
	public BigDecimal getFour() {
		return four;
	}
	public void setFour(BigDecimal four) {
		this.four = four;
	}
	public BigDecimal getFive() {
		return five;
	}
	public void setFive(BigDecimal five) {
		this.five = five;
	}
	public BigDecimal getSix() {
		return six;
	}
	public void setSix(BigDecimal six) {
		this.six = six;
	}
	public BigDecimal getSeven() {
		return seven;
	}
	public void setSeven(BigDecimal seven) {
		this.seven = seven;
	}
	public BigDecimal getEight() {
		return eight;
	}
	public void setEight(BigDecimal eight) {
		this.eight = eight;
	}
	public BigDecimal getNine() {
		return nine;
	}
	public void setNine(BigDecimal nine) {
		this.nine = nine;
	}
	public BigDecimal getTen() {
		return ten;
	}
	public void setTen(BigDecimal ten) {
		this.ten = ten;
	}
	public BigDecimal getEleven() {
		return eleven;
	}
	public void setEleven(BigDecimal eleven) {
		this.eleven = eleven;
	}
	public BigDecimal getTweer() {
		return tweer;
	}
	public void setTweer(BigDecimal tweer) {
		this.tweer = tweer;
	}
	public BigDecimal getSid() {
		return sid;
	}
	public void setSid(BigDecimal sid) {
		this.sid = sid;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}

	
	

}
