package org.come.model;
/**
 * 培养饰品表
 * <AUTHOR>
 *
 */
public class Decorate {
	
	// 物品id	
	private String decotateid;
	
	// 标签
	private String decotatetage;
	
	// 物品id [1]
	private String decotatesn;
	
	// 基本属性
	private String decotatebvalue;
	
	// 类型
	private String decotatetype;
	
	// 属性点
	private String decotatepvalue;
	
	// 附加属性
	private String decotatefvalue;
	
	// 附加抗性
	private String decotatekvalue;
	
	// 培养属性1
	private String decotatepv1;
	
	// 培养属性2
	private String decotatepv2;
	
	// 升级值
	private String decotatemv;
	
	// 培养值
	private String decotatev;
	// 神类值
	private String slsx;
	// 神类值
	private String slsx1;
	private String slsx2;
	private String slsx3;
	private String slsx4;
	private String slsx5;
	private String slsx6;
	private String slsx7;

	public String getDecotateid() {
		return decotateid;
	}

	public void setDecotateid(String decotateid) {
		this.decotateid = decotateid;
	}

	public String getDecotatetage() {
		return decotatetage;
	}

	public void setDecotatetage(String decotatetage) {
		this.decotatetage = decotatetage;
	}

	public String getDecotatesn() {
		return decotatesn;
	}

	public void setDecotatesn(String decotatesn) {
		this.decotatesn = decotatesn;
	}

	public String getDecotatebvalue() {
		return decotatebvalue;
	}

	public void setDecotatebvalue(String decotatebvalue) {
		this.decotatebvalue = decotatebvalue;
	}

	public String getDecotatetype() {
		return decotatetype;
	}

	public void setDecotatetype(String decotatetype) {
		this.decotatetype = decotatetype;
	}

	public String getDecotatepvalue() {
		return decotatepvalue;
	}

	public void setDecotatepvalue(String decotatepvalue) {
		this.decotatepvalue = decotatepvalue;
	}

	public String getDecotatefvalue() {
		return decotatefvalue;
	}

	public void setDecotatefvalue(String decotatefvalue) {
		this.decotatefvalue = decotatefvalue;
	}

	public String getDecotatekvalue() {
		return decotatekvalue;
	}

	public void setDecotatekvalue(String decotatekvalue) {
		this.decotatekvalue = decotatekvalue;
	}

	public String getDecotatepv1() {
		return decotatepv1;
	}

	public void setDecotatepv1(String decotatepv1) {
		this.decotatepv1 = decotatepv1;
	}

	public String getDecotatepv2() {
		return decotatepv2;
	}

	public void setDecotatepv2(String decotatepv2) {
		this.decotatepv2 = decotatepv2;
	}

	public String getDecotatemv() {
		return decotatemv;
	}

	public void setDecotatemv(String decotatemv) {
		this.decotatemv = decotatemv;
	}

	public String getDecotatev() {
		return decotatev;
	}

	public void setDecotatev(String decotatev) {
		this.decotatev = decotatev;
	}
	public String getSlsx() {
		return slsx;
	}

	public void setSlsx(String slsx) {
		this.slsx = slsx;
	}

	public String getSlsx1() {return slsx1;}
	public void setSlsx1(String slsx1) {this.slsx1= slsx1;}

	public String getSlsx2() {return slsx2;}
	public void setSlsx2(String slsx1) {this.slsx2= slsx2;}

	public String getSlsx3() {return slsx3;}
	public void setSlsx3(String slsx3) {this.slsx3= slsx3;}

	public String getSlsx4() {return slsx4;}
	public void setSlsx4(String slsx4) {this.slsx4= slsx4;}

	public String getSlsx5() {return slsx5;}
	public void setSlsx5(String slsx5) {this.slsx5= slsx5;}

	public String getSlsx6() {return slsx6;}
	public void setSlsx6(String slsx6) {this.slsx6= slsx6;}

	public String getSlsx7() {return slsx7;}
	public void setSlsx7(String slsx7) {this.slsx7= slsx7;}

}
