package org.come.model;

import java.math.BigDecimal;

/**
 * 角色数据
 * <AUTHOR>
 *
 */
public class HDXRole {

    //角色ID
    private BigDecimal role_id;
    // 购买的开奖类型 1头彩，2双对，3七星，4散星
    private String prizeNumber;
    // 开奖期数
    private int stage;
    // 购买类型 5大话币 6仙玉
    private int tpye;
    // 购买金额
    private long money;
    // 是否中奖
    private String ifWin;

    public BigDecimal getRole_id() {
        return role_id;
    }
    public void setRole_id(BigDecimal role_id) {
        this.role_id = role_id;
    }
    public String getPrizeNumber() {
        return prizeNumber;
    }
    public void setPrizeNumber(String prizeNumber) {
        this.prizeNumber = prizeNumber;
    }
    public int getStage() {
        return stage;
    }
    public void setStage(int stage) {
        this.stage = stage;
    }

    public int getTpye() {
        return tpye;
    }

    public void setTpye(int tpye) {
        this.tpye = tpye;
    }

    public long getMoney() {
        return money;
    }
    public void setMoney(long money) {
        this.money = money;
    }
    public String getIfWin() {
        return ifWin;
    }
    public void setIfWin(String ifWin) {
        this.ifWin = ifWin;
    }


}
