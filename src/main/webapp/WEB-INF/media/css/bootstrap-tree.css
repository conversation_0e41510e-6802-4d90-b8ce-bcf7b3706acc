/*!
 * Bootstrap Tree v0.3
 *
 * Copyright 2012 Cutters Crossing
 * Bootstrap is Copyright 2012 Twitter, Inc.
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Designed and built with all the love in the world by @cutterbl.
 */
 
.tree,
.branch {
  list-style: none outside none;
}
.branch {
  postion: relative;
  height: 0;
  margin: 0 0 0 15px;
  overflow: hidden;
}
.branch.in {
  height: auto;
}

a:link,
a:visited,
a:hover,
a:active {
  color: #000;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
  cursor: pointer;
}
/* Work in progress */
a.tree-toggle-icon-only {
  height: 16px;
  width: 20px;
  line-height: 16px;
  vertical-align: middle;
  display: inline-block;
  background: url("../image/bstree-halflings.png") no-repeat;
  background-position: 0 -22px;
}

a.tree-toggle {
  height: 16px;
  padding-left: 20px;
  line-height: 16px;
  vertical-align: middle;
  display: inline-block;
  background: url("../image/bstree-halflings.png") no-repeat;
  background-position: 0 -22px;
}
a.tree-toggle.closed, a.tree-toggle-icon-only.closed {
  background-position: 0 1px;
}