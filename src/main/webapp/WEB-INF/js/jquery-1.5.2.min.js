/*!
 * jQuery JavaScript Library v1.5.2
 * http://jquery.com/
 *
 * Copyright 2011, <PERSON>
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * Includes Sizzle.js
 * http://sizzlejs.com/
 * Copyright 2011, The Dojo Foundation
 * Released under the MIT, BSD, and GPL Licenses.
 *
 * Date: Thu Mar 31 15:28:23 2011 -0400
 */
(function(g,e){function Z0($){return c.isWindow($)?$:$.nodeType===9?$.defaultView||$.parentWindow:!1}function d0(A){if(!k0[A]){var $=c("<"+A+">").appendTo("body"),_=$.css("display");$.remove();if(_==="none"||_==="")_="block";k0[A]=_}return k0[A]}function c0(A,$){var _={};c.each(f0.concat.apply([],f0.slice(0,$)),function(){_[this]=A});return _}function h0(){try{return new g.ActiveXObject("Microsoft.XMLHTTP")}catch($){}}function i0(){try{return new g.XMLHttpRequest}catch($){}}function p0(){c(g).unload(function(){for(var $ in r0)r0[$](0,1)})}function n0(C,B){C.dataFilter&&(B=C.dataFilter(B,C.dataType));var A=C.dataTypes,$={},_,J,K=A.length,H,I=A[0],F,G,D,E,L;for(_=1;_<K;_++){if(_===1)for(J in C.converters)typeof J==="string"&&($[J.toLowerCase()]=C.converters[J]);F=I,I=A[_];if(I==="*")I=F;else if(F!=="*"&&F!==I){G=F+" "+I,D=$[G]||$["* "+I];if(!D){L=e;for(E in $){H=E.split(" ");if(H[0]===F||H[0]==="*"){L=$[H[1]+" "+I];if(L){E=$[E],E===!0?D=L:L===!0&&(D=E);break}}}}!D&&!L&&c.error("No conversion from "+G.replace(" "," to ")),D!==!0&&(B=D?D(B):L(E(B)))}}return B}function o0(D,C,A){var B=D.contents,$=D.dataTypes,_=D.responseFields,G,H,E,F;for(H in _)H in A&&(C[_[H]]=A[H]);while($[0]==="*")$.shift(),G===e&&(G=D.mimeType||C.getResponseHeader("content-type"));if(G)for(H in B)if(B[H]&&B[H].test(G)){$.unshift(H);break}if($[0]in A)E=$[0];else{for(H in A){if(!$[0]||D.converters[H+" "+$[0]]){E=H;break}F||(F=H)}E=E||F}if(E){E!==$[0]&&$.unshift(E);return A[E]}}function y0(C,A,B,_){if(c.isArray(A)&&A.length)c.each(A,function(A,$){B||F0.test(C)?_(C,$):y0(C+"["+(typeof $==="object"||c.isArray($)?A:"")+"]",$,B,_)});else if(B||A==null||typeof A!=="object")_(C,A);else if(c.isArray(A)||c.isEmptyObject(A))_(C,"");else for(var $ in A)y0(C+"["+$+"]",A[$],B,_)}function x0(D,C,A,B,$,_){$=$||C.dataTypes[0],_=_||{},_[$]=!0;var H=D[$],I=0,F=H?H.length:0,G=D===v0,E;for(;I<F&&(G||!E);I++)E=H[I](C,A,B),typeof E==="string"&&(!G||_[E]?E=e:(C.dataTypes.unshift(E),E=x0(D,C,A,B,E,_)));(G||!E)&&!_["*"]&&(E=x0(D,C,A,B,"*",_));return E}function $1($){return function(C,D){typeof C!=="string"&&(D=C,C="*");if(c.isFunction(D)){var B=C.toLowerCase().split(_1),_=0,A=B.length,F,G,E;for(;_<A;_++)F=B[_],E=/^\+/.test(F),E&&(F=F.substr(1)||"*"),G=$[F]=$[F]||[],G[E?"unshift":"push"](D)}}}function M0(C,A,B){var _=A==="width"?O0:P0,$=A==="width"?C.offsetWidth:C.offsetHeight;if(B==="border")return $;c.each(_,function(){B||($-=parseFloat(c.css(C,"padding"+this))||0),B==="margin"?$+=parseFloat(c.css(C,"margin"+this))||0:$-=parseFloat(c.css(C,"border"+this+"Width"))||0});return $}function W0(_,$){$.src?c.ajax({url:$.src,async:!1,dataType:"script"}):c.globalEval($.text||$.textContent||$.innerHTML||""),$.parentNode&&$.parentNode.removeChild($)}function V0($){return"getElementsByTagName"in $?$.getElementsByTagName("*"):"querySelectorAll"in $?$.querySelectorAll("*"):[]}function j0(A,$){if($.nodeType===1){var _=$.nodeName.toLowerCase();$.clearAttributes(),$.mergeAttributes(A);if(_==="object")$.outerHTML=A.outerHTML;else if(_!=="input"||A.type!=="checkbox"&&A.type!=="radio"){if(_==="option")$.selected=A.defaultSelected;else if(_==="input"||_==="textarea")$.defaultValue=A.defaultValue}else A.checked&&($.defaultChecked=$.checked=A.checked),$.value!==A.value&&($.value=A.value);$.removeAttribute(c.expando)}}function W(D,B){if(B.nodeType===1&&c.hasData(D)){var C=c.expando,A=c.data(D),$=c.data(B,A);if(A=A[C]){var _=A.events;$=$[C]=c.extend({},A);if(_){delete $.handle,$.events={};for(var F in _)for(var G=0,E=_[F].length;G<E;G++)c.event.add(B,F+(_[F][G].namespace?".":"")+_[F][G].namespace,_[F][G],_[F][G].data)}}}}function $(_,$){return c.nodeName(_,"table")?_.getElementsByTagName("tbody")[0]||_.appendChild(_.ownerDocument.createElement("tbody")):_}function S(B,_,A){if(c.isFunction(_))return c.grep(B,function(C,$){var B=!!_.call(C,$,C);return B===A});if(_.nodeType)return c.grep(B,function(B,$){return B===_===A});if(typeof _==="string"){var $=c.grep(B,function($){return $.nodeType===1});if(G.test(_))return c.filter(_,$,!A);_=c.filter(_,$)}return c.grep(B,function(B,$){return c.inArray(B,_)>=0===A})}function T($){return!$||!$.parentNode||$.parentNode.nodeType===11}function K(_,$){return(_&&_!=="*"?_+".":"")+$.replace(s,"`").replace(r,"&")}function C(D){var B,C,A,$,_,K,L,I,J,G,H,E,F,P=[],O=[],N=c._data(this,"events");if(D.liveFired!==this&&N&&N.live&&!D.target.disabled&&(!D.button||D.type!=="click")){D.namespace&&(E=new RegExp("(^|\\.)"+D.namespace.split(".").join("\\.(?:.*\\.)?")+"(\\.|$)")),D.liveFired=this;var M=N.live.slice(0);for(L=0;L<M.length;L++)_=M[L],_.origType.replace(u,"")===D.type?O.push(_.selector):M.splice(L--,1);$=c(D.target).closest(O,D.currentTarget);for(I=0,J=$.length;I<J;I++){H=$[I];for(L=0;L<M.length;L++){_=M[L];if(H.selector===_.selector&&(!E||E.test(_.namespace))&&!H.elem.disabled){K=H.elem,A=null;if(_.preType==="mouseenter"||_.preType==="mouseleave")D.type=_.preType,A=c(D.relatedTarget).closest(_.selector)[0];(!A||A!==K)&&P.push({elem:K,handleObj:_,level:H.level})}}}for(I=0,J=P.length;I<J;I++){$=P[I];if(C&&$.level>C)break;D.currentTarget=$.elem,D.data=$.handleObj.data,D.handleObj=$.handleObj,F=$.handleObj.origHandler.apply($.elem,arguments);if(F===!1||D.isPropagationStopped()){C=$.level,F===!1&&(B=!1);if(D.isImmediatePropagationStopped())break}}return B}}function A(B,A,_){var $=c.extend({},_[0]);$.type=B,$.originalEvent={},$.liveFired=e,c.event.handle.call(A,$),$.isDefaultPrevented()&&_[0].preventDefault()}function y(){return!0}function z(){return!1}function o(_){for(var $ in _)if($!=="toJSON")return!1;return!0}function n(B,A,_){if(_===e&&B.nodeType===1){_=B.getAttribute("data-"+A);if(typeof _==="string"){try{_=_==="true"?!0:_==="false"?!1:_==="null"?null:c.isNaN(_)?b.test(_)?c.parseJSON(_):_:parseFloat(_)}catch($){}c.data(B,A,_)}else _=e}return _}var f=g.document,c=function(){function B(){if(!H.isReady){try{f.documentElement.doScroll("left")}catch($){setTimeout(B,1);return}H.ready()}}var H=function(_,$){return new H.fn.init(_,$,G)},I=g.jQuery,F=g.$,G,P=/^(?:[^<]*(<[\w\W]+>)[^>]*$|#([\w\-]+)$)/,Q=/\S/,N=/^\s+/,O=/\s+$/,L=/\d/,M=/^<(\w+)\s*\/?>(?:<\/\1>)?$/,J=/^[\],:{}\s]*$/,K=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,Y=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,X=/(?:^|:|,)(?:\s*\[)+/g,W=/(webkit)[ \/]([\w.]+)/,V=/(opera)(?:.*version)?[ \/]([\w.]+)/,U=/(msie) ([\w.]+)/,T=/(mozilla)(?:.*? rv:([\w.]+))?/,S=navigator.userAgent,R,b,a,Z=Object.prototype.toString,C=Object.prototype.hasOwnProperty,D=Array.prototype.push,E=Array.prototype.slice,$=String.prototype.trim,_=Array.prototype.indexOf,A={};H.fn=H.prototype={constructor:H,init:function(B,A,$){var _,E,C,D;if(!B)return this;if(B.nodeType){this.context=this[0]=B,this.length=1;return this}if(B==="body"&&!A&&f.body){this.context=f,this[0]=f.body,this.selector="body",this.length=1;return this}if(typeof B==="string"){_=P.exec(B);if(!_||!_[1]&&A)return!A||A.jquery?(A||$).find(B):this.constructor(A).find(B);if(_[1]){A=A instanceof H?A[0]:A,D=A?A.ownerDocument||A:f,C=M.exec(B),C?H.isPlainObject(A)?(B=[f.createElement(C[1])],H.fn.attr.call(B,A,!0)):B=[D.createElement(C[1])]:(C=H.buildFragment([_[1]],[D]),B=(C.cacheable?H.clone(C.fragment):C.fragment).childNodes);return H.merge(this,B)}E=f.getElementById(_[2]);if(E&&E.parentNode){if(E.id!==_[2])return $.find(B);this.length=1,this[0]=E}this.context=f,this.selector=B;return this}if(H.isFunction(B))return $.ready(B);B.selector!==e&&(this.selector=B.selector,this.context=B.context);return H.makeArray(B,this)},selector:"",jquery:"1.5.2",length:0,size:function(){return this.length},toArray:function(){return E.call(this,0)},get:function($){return $==null?this.toArray():$<0?this[this.length+$]:this[$]},pushStack:function(B,_,A){var $=this.constructor();H.isArray(B)?D.apply($,B):H.merge($,B),$.prevObject=this,$.context=this.context,_==="find"?$.selector=this.selector+(this.selector?" ":"")+A:_&&($.selector=this.selector+"."+_+"("+A+")");return $},each:function(_,$){return H.each(this,_,$)},ready:function($){H.bindReady(),b.done($);return this},eq:function($){return $===-1?this.slice($):this.slice($,+$+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},slice:function(){return this.pushStack(E.apply(this,arguments),"slice",E.call(arguments).join(","))},map:function($){return this.pushStack(H.map(this,function(_,A){return $.call(_,A,_)}))},end:function(){return this.prevObject||this.constructor(null)},push:D,sort:[].sort,splice:[].splice},H.fn.init.prototype=H.fn,H.extend=H.fn.extend=function(){var C,B,A,$,_,G,I=arguments[0]||{},E=1,F=arguments.length,D=!1;typeof I==="boolean"&&(D=I,I=arguments[1]||{},E=2),typeof I!=="object"&&!H.isFunction(I)&&(I={}),F===E&&(I=this,--E);for(;E<F;E++)if((C=arguments[E])!=null)for(B in C){A=I[B],$=C[B];if(I===$)continue;D&&$&&(H.isPlainObject($)||(_=H.isArray($)))?(_?(_=!1,G=A&&H.isArray(A)?A:[]):G=A&&H.isPlainObject(A)?A:{},I[B]=H.extend(D,G,$)):$!==e&&(I[B]=$)}return I},H.extend({noConflict:function($){g.$=F,$&&(g.jQuery=I);return H},isReady:!1,readyWait:1,ready:function($){$===!0&&H.readyWait--;if(!H.readyWait||$!==!0&&!H.isReady){if(!f.body)return setTimeout(H.ready,1);H.isReady=!0;if($!==!0&&--H.readyWait>0)return;b.resolveWith(f,[H]),H.fn.trigger&&H(f).trigger("ready").unbind("ready")}},bindReady:function(){if(!b){b=H._Deferred();if(f.readyState==="complete")return setTimeout(H.ready,1);if(f.addEventListener)f.addEventListener("DOMContentLoaded",a,!1),g.addEventListener("load",H.ready,!1);else if(f.attachEvent){f.attachEvent("onreadystatechange",a),g.attachEvent("onload",H.ready);var _=!1;try{_=g.frameElement==null}catch($){}f.documentElement.doScroll&&_&&B()}}},isFunction:function($){return H.type($)==="function"},isArray:Array.isArray||function($){return H.type($)==="array"},isWindow:function($){return $&&typeof $==="object"&&"setInterval"in $},isNaN:function($){return $==null||!L.test($)||isNaN($)},type:function($){return $==null?String($):A[Z.call($)]||"object"},isPlainObject:function(_){if(!_||H.type(_)!=="object"||_.nodeType||H.isWindow(_))return!1;if(_.constructor&&!C.call(_,"constructor")&&!C.call(_.constructor.prototype,"isPrototypeOf"))return!1;var $;for($ in _);return $===e||C.call(_,$)},isEmptyObject:function(_){for(var $ in _)return!1;return!0},error:function($){throw $},parseJSON:function($){if(typeof $!=="string"||!$)return null;$=H.trim($);if(J.test($.replace(K,"@").replace(Y,"]").replace(X,"")))return g.JSON&&g.JSON.parse?g.JSON.parse($):(new Function("return "+$))();H.error("Invalid JSON: "+$)},parseXML:function(_,A,$){g.DOMParser?($=new DOMParser,A=$.parseFromString(_,"text/xml")):(A=new ActiveXObject("Microsoft.XMLDOM"),A.async="false",A.loadXML(_)),$=A.documentElement,(!$||!$.nodeName||$.nodeName==="parsererror")&&H.error("Invalid XML: "+_);return A},noop:function(){},globalEval:function(A){if(A&&Q.test(A)){var _=f.head||f.getElementsByTagName("head")[0]||f.documentElement,$=f.createElement("script");H.support.scriptEval()?$.appendChild(f.createTextNode(A)):$.text=A,_.insertBefore($,_.firstChild),_.removeChild($)}},nodeName:function(_,$){return _.nodeName&&_.nodeName.toUpperCase()===$.toUpperCase()},each:function(C,B,A){var $,_=0,E=C.length,F=E===e||H.isFunction(C);if(A){if(F){for($ in C)if(B.apply(C[$],A)===!1)break}else for(;_<E;)if(B.apply(C[_++],A)===!1)break}else if(F){for($ in C)if(B.call(C[$],$,C[$])===!1)break}else for(var D=C[0];_<E&&B.call(D,_,D)!==!1;D=C[++_]);return C},trim:$?function(_){return _==null?"":$.call(_)}:function($){return $==null?"":($+"").replace(N,"").replace(O,"")},makeArray:function(B,_){var A=_||[];if(B!=null){var $=H.type(B);B.length==null||$==="string"||$==="function"||$==="regexp"||H.isWindow(B)?D.call(A,B):H.merge(A,B)}return A},inArray:function(B,_){if(_.indexOf)return _.indexOf(B);for(var A=0,$=_.length;A<$;A++)if(_[A]===B)return A;return-1},merge:function(C,B){var _=C.length,A=0;if(typeof B.length==="number"){for(var $=B.length;A<$;A++)C[_++]=B[A]}else while(B[A]!==e)C[_++]=B[A++];C.length=_;return C},grep:function(E,C,D){var A=[],B;D=!!D;for(var $=0,_=E.length;$<_;$++)B=!!C(E[$],$),D!==B&&A.push(E[$]);return A},map:function(E,C,D){var A=[],B;for(var $=0,_=E.length;$<_;$++)B=C(E[$],$,D),B!=null&&(A[A.length]=B);return A.concat.apply([],A)},guid:1,proxy:function(A,_,$){arguments.length===2&&(typeof _==="string"?($=A,A=$[_],_=e):_&&!H.isFunction(_)&&($=_,_=e)),!_&&A&&(_=function(){return A.apply($||this,arguments)}),A&&(_.guid=A.guid=A.guid||_.guid||H.guid++);return _},access:function(C,B,A,$,_,F){var G=C.length;if(typeof B==="object"){for(var D in B)H.access(C,D,B[D],$,_,A);return C}if(A!==e){$=!F&&$&&H.isFunction(A);for(var E=0;E<G;E++)_(C[E],B,$?A.call(C[E],E,_(C[E],B)):A,F);return C}return G?_(C[0],B):e},now:function(){return(new Date).getTime()},uaMatch:function(_){_=_.toLowerCase();var $=W.exec(_)||V.exec(_)||U.exec(_)||_.indexOf("compatible")<0&&T.exec(_)||[];return{browser:$[1]||"",version:$[2]||"0"}},sub:function(){function A($,_){return new A.fn.init($,_)}H.extend(!0,A,this),A.superclass=this,A.fn=A.prototype=this(),A.fn.constructor=A,A.subclass=this.subclass,A.fn.init=function _(_,B){B&&B instanceof H&&!(B instanceof A)&&(B=A(B));return H.fn.init.call(this,_,B,$)},A.fn.init.prototype=A.fn;var $=A(f);return A},browser:{}}),H.each("Boolean Number String Function Array Date RegExp Object".split(" "),function(_,$){A["[object "+$+"]"]=$.toLowerCase()}),R=H.uaMatch(S),R.browser&&(H.browser[R.browser]=!0,H.browser.version=R.version),H.browser.webkit&&(H.browser.safari=!0),_&&(H.inArray=function(A,$){return _.call($,A)}),Q.test("\xa0")&&(N=/^[\s\xA0]+/,O=/[\s\xA0]+$/),G=H(f),f.addEventListener?a=function(){f.removeEventListener("DOMContentLoaded",a,!1),H.ready()}:f.attachEvent&&(a=function(){f.readyState==="complete"&&(f.detachEvent("onreadystatechange",a),H.ready())});return H}(),d="then done fail isResolved isRejected promise".split(" "),a=[].slice;c.extend({_Deferred:function(){var C=[],A,B,_,$={done:function(){if(!_){var D=arguments,B,G,H,E,F;A&&(F=A,A=0);for(B=0,G=D.length;B<G;B++)H=D[B],E=c.type(H),E==="array"?$.done.apply($,H):E==="function"&&C.push(H);F&&$.resolveWith(F[0],F[1])}return this},resolveWith:function(D,$){if(!_&&!A&&!B){$=$||[],B=1;try{while(C[0])C.shift().apply(D,$)}finally{A=[D,$],B=0}}return this},resolve:function(){$.resolveWith(this,arguments);return this},isResolved:function(){return B||A},cancel:function(){_=1,C=[];return this}};return $},Deferred:function(B){var _=c._Deferred(),A=c._Deferred(),$;c.extend(_,{then:function(A,$){_.done(A).fail($);return this},fail:A.done,rejectWith:A.resolveWith,reject:A.resolve,isRejected:A.isResolved,promise:function(B){if(B==null){if($)return $;$=B={}}var A=d.length;while(A--)B[d[A]]=_[d[A]];return B}}),_.done(A.cancel).fail(_.cancel),delete _.cancel,B&&B.call(_,_);return _},when:function(C){function E(_){return function(B){A[_]=arguments.length>1?a.call(arguments,0):B,--$||D.resolveWith(D,a.call(A,0))}}var A=arguments,B=0,_=A.length,$=_,D=_<=1&&C&&c.isFunction(C.promise)?C:c.Deferred();if(_>1){for(;B<_;B++)A[B]&&c.isFunction(A[B].promise)?A[B].promise().then(E(B),D.reject):--$;$||D.resolveWith(D,A)}else D!==C&&D.resolveWith(D,_?[C]:[]);return D.promise()}}),function(){c.support={};var B=f.createElement("div");B.style.display="none",B.innerHTML="   <link/><table></table><a href='/a' style='color:red;float:left;opacity:.55;'>a</a><input type='checkbox'/>";var A=B.getElementsByTagName("*"),$=B.getElementsByTagName("a")[0],_=f.createElement("select"),H=_.appendChild(f.createElement("option")),I=B.getElementsByTagName("input")[0];if(A&&A.length&&$){c.support={leadingWhitespace:B.firstChild.nodeType===3,tbody:!B.getElementsByTagName("tbody").length,htmlSerialize:!!B.getElementsByTagName("link").length,style:/red/.test($.getAttribute("style")),hrefNormalized:$.getAttribute("href")==="/a",opacity:/^0.55$/.test($.style.opacity),cssFloat:!!$.style.cssFloat,checkOn:I.value==="on",optSelected:H.selected,deleteExpando:!0,optDisabled:!1,checkClone:!1,noCloneEvent:!0,noCloneChecked:!0,boxModel:null,inlineBlockNeedsLayout:!1,shrinkWrapBlocks:!1,reliableHiddenOffsets:!0,reliableMarginRight:!0},I.checked=!0,c.support.noCloneChecked=I.cloneNode(!0).checked,_.disabled=!0,c.support.optDisabled=!H.disabled;var F=null;c.support.scriptEval=function(){if(F===null){var B=f.documentElement,A=f.createElement("script"),$="script"+c.now();try{A.appendChild(f.createTextNode("window."+$+"=1;"))}catch(_){}B.insertBefore(A,B.firstChild),g[$]?(F=!0,delete g[$]):F=!1,B.removeChild(A)}return F};try{delete B.test}catch(G){c.support.deleteExpando=!1}!B.addEventListener&&B.attachEvent&&B.fireEvent&&(B.attachEvent("onclick",function D(){c.support.noCloneEvent=!1,B.detachEvent("onclick",D)}),B.cloneNode(!0).fireEvent("onclick")),B=f.createElement("div"),B.innerHTML="<input type='radio' name='radiotest' checked='checked'/>";var E=f.createDocumentFragment();E.appendChild(B.firstChild),c.support.checkClone=E.cloneNode(!0).cloneNode(!0).lastChild.checked,c(function(){var A=f.createElement("div"),_=f.getElementsByTagName("body")[0];if(_){A.style.width=A.style.paddingLeft="1px",_.appendChild(A),c.boxModel=c.support.boxModel=A.offsetWidth===2,"zoom"in A.style&&(A.style.display="inline",A.style.zoom=1,c.support.inlineBlockNeedsLayout=A.offsetWidth===2,A.style.display="",A.innerHTML="<div style='width:4px;'></div>",c.support.shrinkWrapBlocks=A.offsetWidth!==2),A.innerHTML="<table><tr><td style='padding:0;border:0;display:none'></td><td>t</td></tr></table>";var $=A.getElementsByTagName("td");c.support.reliableHiddenOffsets=$[0].offsetHeight===0,$[0].style.display="",$[1].style.display="none",c.support.reliableHiddenOffsets=c.support.reliableHiddenOffsets&&$[0].offsetHeight===0,A.innerHTML="",f.defaultView&&f.defaultView.getComputedStyle&&(A.style.width="1px",A.style.marginRight="0",c.support.reliableMarginRight=(parseInt(f.defaultView.getComputedStyle(A,null).marginRight,10)||0)===0),_.removeChild(A).style.display="none",A=$=null}});var C=function(A){var _=f.createElement("div");A="on"+A;if(!_.attachEvent)return!0;var $=A in _;$||(_.setAttribute(A,"return;"),$=typeof _[A]==="function");return $};c.support.submitBubbles=C("submit"),c.support.changeBubbles=C("change"),B=A=$=null}}();var b=/^(?:\{.*\}|\[.*\])$/;c.extend({cache:{},uuid:0,expando:"jQuery"+(c.fn.jquery+Math.random()).replace(/\D/g,""),noData:{embed:!0,object:"clsid:D27CDB6E-AE6D-11cf-96B8-************",applet:!0},hasData:function($){$=$.nodeType?c.cache[$[c.expando]]:$[c.expando];return!!$&&!o($)},data:function(C,B,A,$){if(c.acceptData(C)){var _=c.expando,G=typeof B==="string",H,E=C.nodeType,F=E?c.cache:C,D=E?C[c.expando]:C[c.expando]&&c.expando;if((!D||$&&D&&!F[D][_])&&G&&A===e)return;D||(E?C[c.expando]=D=++c.uuid:D=c.expando),F[D]||(F[D]={},E||(F[D].toJSON=c.noop));if(typeof B==="object"||typeof B==="function")$?F[D][_]=c.extend(F[D][_],B):F[D]=c.extend(F[D],B);H=F[D],$&&(H[_]||(H[_]={}),H=H[_]),A!==e&&(H[B]=A);if(B==="events"&&!H[B])return H[_]&&H[_].events;return G?H[B]:H}},removeData:function(B,C,A){if(c.acceptData(B)){var $=c.expando,_=B.nodeType,G=_?c.cache:B,E=_?B[c.expando]:c.expando;if(!G[E])return;if(C){var F=A?G[E][$]:G[E];if(F){delete F[C];if(!o(F))return}}if(A){delete G[E][$];if(!o(G[E]))return}var D=G[E][$];c.support.deleteExpando||G!=g?delete G[E]:G[E]=null,D?(G[E]={},_||(G[E].toJSON=c.noop),G[E][$]=D):_&&(c.support.deleteExpando?delete B[c.expando]:B.removeAttribute?B.removeAttribute(c.expando):B[c.expando]=null)}},_data:function(A,$,_){return c.data(A,$,_,!0)},acceptData:function(_){if(_.nodeName){var $=c.noData[_.nodeName.toLowerCase()];if($)return $!==!0&&_.getAttribute("classid")===$}return!0}}),c.fn.extend({data:function(C,B){var A=null;if(typeof C==="undefined"){if(this.length){A=c.data(this[0]);if(this[0].nodeType===1){var $=this[0].attributes,_;for(var F=0,D=$.length;F<D;F++)_=$[F].name,_.indexOf("data-")===0&&(_=_.substr(5),n(this[0],_,A[_]))}}return A}if(typeof C==="object")return this.each(function(){c.data(this,C)});var E=C.split(".");E[1]=E[1]?"."+E[1]:"";if(B===e){A=this.triggerHandler("getData"+E[1]+"!",[E[0]]),A===e&&this.length&&(A=c.data(this[0],C),A=n(this[0],C,A));return A===e&&E[1]?this.data(E[0]):A}return this.each(function(){var _=c(this),$=[E[0],B];_.triggerHandler("setData"+E[1]+"!",$),c.data(this,C,B),_.triggerHandler("changeData"+E[1]+"!",$)})},removeData:function($){return this.each(function(){c.removeData(this,$)})}}),c.extend({queue:function(B,_,A){if(B){_=(_||"fx")+"queue";var $=c._data(B,_);if(!A)return $||[];!$||c.isArray(A)?$=c._data(B,_,c.makeArray(A)):$.push(A);return $}},dequeue:function(B,_){_=_||"fx";var A=c.queue(B,_),$=A.shift();$==="inprogress"&&($=A.shift()),$&&(_==="fx"&&A.unshift("inprogress"),$.call(B,function(){c.dequeue(B,_)})),A.length||c.removeData(B,_+"queue",!0)}}),c.fn.extend({queue:function(_,$){typeof _!=="string"&&($=_,_="fx");if($===e)return c.queue(this[0],_);return this.each(function(B){var A=c.queue(this,_,$);_==="fx"&&A[0]!=="inprogress"&&c.dequeue(this,_)})},dequeue:function($){return this.each(function(){c.dequeue(this,$)})},delay:function(_,$){_=c.fx?c.fx.speeds[_]||_:_,$=$||"fx";return this.queue($,function(){var A=this;setTimeout(function(){c.dequeue(A,$)},_)})},clearQueue:function($){return this.queue($||"fx",[])}});var l=/[\n\t\r]/g,m=/\s+/,j=/\r/g,k=/^(?:href|src|style)$/,h=/^(?:button|input)$/i,i=/^(?:button|input|object|select|textarea)$/i,w=/^a(?:rea)?$/i,v=/^(?:radio|checkbox)$/i;c.props={"for":"htmlFor","class":"className",readonly:"readOnly",maxlength:"maxLength",cellspacing:"cellSpacing",rowspan:"rowSpan",colspan:"colSpan",tabindex:"tabIndex",usemap:"useMap",frameborder:"frameBorder"},c.fn.extend({attr:function(_,$){return c.access(this,_,$,!0,c.attr)},removeAttr:function(_,$){return this.each(function(){c.attr(this,_,""),this.nodeType===1&&this.removeAttribute(_)})},addClass:function(D){if(c.isFunction(D))return this.each(function($){var _=c(this);_.addClass(D.call(this,$,_.attr("class")))});if(D&&typeof D==="string"){var B=(D||"").split(m);for(var C=0,A=this.length;C<A;C++){var $=this[C];if($.nodeType===1)if($.className){var _=" "+$.className+" ",F=$.className;for(var G=0,E=B.length;G<E;G++)_.indexOf(" "+B[G]+" ")<0&&(F+=" "+B[G]);$.className=c.trim(F)}else $.className=D}}return this},removeClass:function(C){if(c.isFunction(C))return this.each(function($){var _=c(this);_.removeClass(C.call(this,$,_.attr("class")))});if(C&&typeof C==="string"||C===e){var B=(C||"").split(m);for(var A=0,$=this.length;A<$;A++){var _=this[A];if(_.nodeType===1&&_.className)if(C){var E=(" "+_.className+" ").replace(l," ");for(var F=0,D=B.length;F<D;F++)E=E.replace(" "+B[F]+" "," ");_.className=c.trim(E)}else _.className=""}}return this},toggleClass:function(B,_){var A=typeof B,$=typeof _==="boolean";if(c.isFunction(B))return this.each(function(A){var $=c(this);$.toggleClass(B.call(this,A,$.attr("class"),_),_)});return this.each(function(){if(A==="string"){var C,D=0,F=c(this),G=_,E=B.split(m);while(C=E[D++])G=$?G:!F.hasClass(C),F[G?"addClass":"removeClass"](C)}else if(A==="undefined"||A==="boolean")this.className&&c._data(this,"__className__",this.className),this.className=this.className||B===!1?"":c._data(this,"__className__")||""})},hasClass:function(B){var _=" "+B+" ";for(var A=0,$=this.length;A<$;A++)if((" "+this[A].className+" ").replace(l," ").indexOf(_)>-1)return!0;return!1},val:function(C){if(!arguments.length){var B=this[0];if(B){if(c.nodeName(B,"option")){var A=B.attributes.value;return!A||A.specified?B.value:B.text}if(c.nodeName(B,"select")){var $=B.selectedIndex,_=[],H=B.options,I=B.type==="select-one";if($<0)return null;for(var F=I?$:0,G=I?$+1:H.length;F<G;F++){var E=H[F];if(E.selected&&(c.support.optDisabled?!E.disabled:E.getAttribute("disabled")===null)&&(!E.parentNode.disabled||!c.nodeName(E.parentNode,"optgroup"))){C=c(E).val();if(I)return C;_.push(C)}}if(I&&!_.length&&H.length)return c(H[$]).val();return _}if(v.test(B.type)&&!c.support.checkOn)return B.getAttribute("value")===null?"on":B.value;return(B.value||"").replace(j,"")}return e}var D=c.isFunction(C);return this.each(function(A){var B=c(this),_=C;if(this.nodeType===1){D&&(_=C.call(this,A,B.val())),_==null?_="":typeof _==="number"?_+="":c.isArray(_)&&(_=c.map(_,function($){return $==null?"":$+""}));if(c.isArray(_)&&v.test(this.type))this.checked=c.inArray(B.val(),_)>=0;else if(c.nodeName(this,"select")){var $=c.makeArray(_);c("option",this).each(function(){this.selected=c.inArray(c(this).val(),$)>=0}),$.length||(this.selectedIndex=-1)}else this.value=_}})}}),c.extend({attrFn:{val:!0,css:!0,html:!0,text:!0,data:!0,width:!0,height:!0,offset:!0},attr:function(C,B,A,$){if(!C||C.nodeType===3||C.nodeType===8||C.nodeType===2)return e;if($&&B in c.attrFn)return c(C)[B](A);var _=C.nodeType!==1||!c.isXMLDoc(C),G=A!==e;B=_&&c.props[B]||B;if(C.nodeType===1){var H=k.test(B);if(B==="selected"&&!c.support.optSelected){var E=C.parentNode;E&&(E.selectedIndex,E.parentNode&&E.parentNode.selectedIndex)}if((B in C||C[B]!==e)&&_&&!H){G&&(B==="type"&&h.test(C.nodeName)&&C.parentNode&&c.error("type property can't be changed"),A===null?C.nodeType===1&&C.removeAttribute(B):C[B]=A);if(c.nodeName(C,"form")&&C.getAttributeNode(B))return C.getAttributeNode(B).nodeValue;if(B==="tabIndex"){var F=C.getAttributeNode("tabIndex");return F&&F.specified?F.value:i.test(C.nodeName)||w.test(C.nodeName)&&C.href?0:e}return C[B]}if(!c.support.style&&_&&B==="style"){G&&(C.style.cssText=""+A);return C.style.cssText}G&&C.setAttribute(B,""+A);if(!C.attributes[B]&&(C.hasAttribute&&!C.hasAttribute(B)))return e;var D=!c.support.hrefNormalized&&_&&H?C.getAttribute(B,2):C.getAttribute(B);return D===null?e:D}G&&(C[B]=A);return C[B]}});var u=/\.(.*)$/,t=/^(?:textarea|input|select)$/i,s=/\./g,r=/ /g,q=/[^\w\s.|`]/g,p=function($){return $.replace(q,"\\$&")};c.event={add:function(B,A,$,_){if(B.nodeType!==3&&B.nodeType!==8){try{c.isWindow(B)&&(B!==g&&!B.frameElement)&&(B=g)}catch(I){}if($===!1)$=z;else if(!$)return;var J,G;$.handler&&(J=$,$=J.handler),$.guid||($.guid=c.guid++);var H=c._data(B);if(!H)return;var E=H.events,F=H.handle;E||(H.events=E={}),F||(H.handle=F=function($){return typeof c!=="undefined"&&c.event.triggered!==$.type?c.event.handle.apply(F.elem,arguments):e}),F.elem=B,A=A.split(" ");var C,D=0,M;while(C=A[D++]){G=J?c.extend({},J):{handler:$,data:_},C.indexOf(".")>-1?(M=C.split("."),C=M.shift(),G.namespace=M.slice(0).sort().join(".")):(M=[],G.namespace=""),G.type=C,G.guid||(G.guid=$.guid);var L=E[C],K=c.event.special[C]||{};if(!L){L=E[C]=[];if(!K.setup||K.setup.call(B,_,M,F)===!1)B.addEventListener?B.addEventListener(C,F,!1):B.attachEvent&&B.attachEvent("on"+C,F)}K.add&&(K.add.call(B,G),G.handler.guid||(G.handler.guid=$.guid)),L.push(G),c.event.global[C]=!0}B=null}},global:{},remove:function(C,B,A,$){if(C.nodeType!==3&&C.nodeType!==8){A===!1&&(A=z);var _,J,K,H,I=0,F,G,D,E,Q,P,O,N=c.hasData(C)&&c._data(C),M=N&&N.events;if(!N||!M)return;B&&B.type&&(A=B.handler,B=B.type);if(!B||typeof B==="string"&&B.charAt(0)==="."){B=B||"";for(J in M)c.event.remove(C,J+B);return}B=B.split(" ");while(J=B[I++]){O=J,P=null,F=J.indexOf(".")<0,G=[],F||(G=J.split("."),J=G.shift(),D=new RegExp("(^|\\.)"+c.map(G.slice(0).sort(),p).join("\\.(?:.*\\.)?")+"(\\.|$)")),Q=M[J];if(!Q)continue;if(!A){for(H=0;H<Q.length;H++){P=Q[H];if(F||D.test(P.namespace))c.event.remove(C,O,P.handler,H),Q.splice(H--,1)}continue}E=c.event.special[J]||{};for(H=$||0;H<Q.length;H++){P=Q[H];if(A.guid===P.guid){if(F||D.test(P.namespace))$==null&&Q.splice(H--,1),E.remove&&E.remove.call(C,P);if($!=null)break}}if(Q.length===0||$!=null&&Q.length===1)(!E.teardown||E.teardown.call(C,G)===!1)&&c.removeEvent(C,J,N.handle),_=null,delete M[J]}if(c.isEmptyObject(M)){var L=N.handle;L&&(L.elem=null),delete N.events,delete N.handle,c.isEmptyObject(N)&&c.removeData(C,e,!0)}}},trigger:function(C,B,A){var $=C.type||C,_=arguments[3];if(!_){C=typeof C==="object"?C[c.expando]?C:c.extend(c.Event($),C):c.Event($),$.indexOf("!")>=0&&(C.type=$=$.slice(0,-1),C.exclusive=!0),A||(C.stopPropagation(),c.event.global[$]&&c.each(c.cache,function(){var A=c.expando,_=this[A];_&&_.events&&_.events[$]&&c.event.trigger(C,B,_.handle.elem)}));if(!A||A.nodeType===3||A.nodeType===8)return e;C.result=e,C.target=A,B=c.makeArray(B),B.unshift(C)}C.currentTarget=A;var J=c._data(A,"handle");J&&J.apply(A,B);var K=A.parentNode||A.ownerDocument;try{A&&A.nodeName&&c.noData[A.nodeName.toLowerCase()]||A["on"+$]&&A["on"+$].apply(A,B)===!1&&(C.result=!1,C.preventDefault())}catch(H){}if(!C.isPropagationStopped()&&K)c.event.trigger(C,B,K,!0);else if(!C.isDefaultPrevented()){var I,F=C.target,G=$.replace(u,""),D=c.nodeName(F,"a")&&G==="click",E=c.event.special[G]||{};if((!E._default||E._default.call(A,C)===!1)&&!D&&!(F&&F.nodeName&&c.noData[F.nodeName.toLowerCase()])){try{F[G]&&(I=F["on"+G],I&&(F["on"+G]=null),c.event.triggered=C.type,F[G]())}catch(L){}I&&(F["on"+G]=I),c.event.triggered=e}}},handle:function(B){var A,$,_,I,J,G=[],H=c.makeArray(arguments);B=H[0]=c.event.fix(B||g.event),B.currentTarget=this,A=B.type.indexOf(".")<0&&!B.exclusive,A||(_=B.type.split("."),B.type=_.shift(),G=_.slice(0).sort(),I=new RegExp("(^|\\.)"+G.join("\\.(?:.*\\.)?")+"(\\.|$)")),B.namespace=B.namespace||G.join("."),J=c._data(this,"events"),$=(J||{})[B.type];if(J&&$){$=$.slice(0);for(var E=0,F=$.length;E<F;E++){var C=$[E];if(A||I.test(C.namespace)){B.handler=C.handler,B.data=C.data,B.handleObj=C;var D=C.handler.apply(this,H);D!==e&&(B.result=D,D===!1&&(B.preventDefault(),B.stopPropagation()));if(B.isImmediatePropagationStopped())break}}}return B.result},props:"altKey attrChange attrName bubbles button cancelable charCode clientX clientY ctrlKey currentTarget data detail eventPhase fromElement handler keyCode layerX layerY metaKey newValue offsetX offsetY pageX pageY prevValue relatedNode relatedTarget screenX screenY shiftKey srcElement target toElement view wheelDelta which".split(" "),fix:function(B){if(B[c.expando])return B;var A=B;B=c.Event(A);for(var $=this.props.length,_;$;)_=this.props[--$],B[_]=A[_];B.target||(B.target=B.srcElement||f),B.target.nodeType===3&&(B.target=B.target.parentNode),!B.relatedTarget&&B.fromElement&&(B.relatedTarget=B.fromElement===B.target?B.toElement:B.fromElement);if(B.pageX==null&&B.clientX!=null){var C=f.documentElement,D=f.body;B.pageX=B.clientX+(C&&C.scrollLeft||D&&D.scrollLeft||0)-(C&&C.clientLeft||D&&D.clientLeft||0),B.pageY=B.clientY+(C&&C.scrollTop||D&&D.scrollTop||0)-(C&&C.clientTop||D&&D.clientTop||0)}B.which==null&&(B.charCode!=null||B.keyCode!=null)&&(B.which=B.charCode!=null?B.charCode:B.keyCode),!B.metaKey&&B.ctrlKey&&(B.metaKey=B.ctrlKey),!B.which&&B.button!==e&&(B.which=B.button&1?1:B.button&2?3:B.button&4?2:0);return B},guid:100000000,proxy:c.proxy,special:{ready:{setup:c.bindReady,teardown:c.noop},live:{add:function($){c.event.add(this,K($.origType,$.selector),c.extend({},$,{handler:C,guid:$.handler.guid}))},remove:function($){c.event.remove(this,K($.origType,$.selector),$)}},beforeunload:{setup:function(A,$,_){c.isWindow(this)&&(this.onbeforeunload=_)},teardown:function(_,$){this.onbeforeunload===$&&(this.onbeforeunload=null)}}}},c.removeEvent=f.removeEventListener?function(A,$,_){A.removeEventListener&&A.removeEventListener($,_,!1)}:function(A,$,_){A.detachEvent&&A.detachEvent("on"+$,_)},c.Event=function($){if(!this.preventDefault)return new c.Event($);$&&$.type?(this.originalEvent=$,this.type=$.type,this.isDefaultPrevented=$.defaultPrevented||$.returnValue===!1||$.getPreventDefault&&$.getPreventDefault()?y:z):this.type=$,this.timeStamp=c.now(),this[c.expando]=!0},c.Event.prototype={preventDefault:function(){this.isDefaultPrevented=y;var $=this.originalEvent;$&&($.preventDefault?$.preventDefault():$.returnValue=!1)},stopPropagation:function(){this.isPropagationStopped=y;var $=this.originalEvent;$&&($.stopPropagation&&$.stopPropagation(),$.cancelBubble=!0)},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=y,this.stopPropagation()},isDefaultPrevented:z,isPropagationStopped:z,isImmediatePropagationStopped:z};var x=function(A){var _=A.relatedTarget;try{if(_&&_!==f&&!_.parentNode)return;while(_&&_!==this)_=_.parentNode;_!==this&&(A.type=A.data,c.event.handle.apply(this,arguments))}catch($){}},D=function($){$.type=$.data,c.event.handle.apply(this,arguments)};c.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(_,$){c.event.special[_]={setup:function(A){c.event.add(this,$,A&&A.selector?D:x,_)},teardown:function(_){c.event.remove(this,$,_&&_.selector?D:x)}}}),c.support.submitBubbles||(c.event.special.submit={setup:function(_,$){if(this.nodeName&&this.nodeName.toLowerCase()!=="form")c.event.add(this,"click.specialSubmit",function(B){var $=B.target,_=$.type;(_==="submit"||_==="image")&&c($).closest("form").length&&A("submit",this,arguments)}),c.event.add(this,"keypress.specialSubmit",function(B){var $=B.target,_=$.type;(_==="text"||_==="password")&&c($).closest("form").length&&B.keyCode===13&&A("submit",this,arguments)});else return!1},teardown:function($){c.event.remove(this,".specialSubmit")}});if(!c.support.changeBubbles){var E,F=function(A){var $=A.type,_=A.value;$==="radio"||$==="checkbox"?_=A.checked:$==="select-multiple"?_=A.selectedIndex>-1?c.map(A.options,function($){return $.selected}).join("-"):"":A.nodeName.toLowerCase()==="select"&&(_=A.selectedIndex);return _},_=function _(C){var B=C.target,A,$;if(t.test(B.nodeName)&&!B.readOnly){A=c._data(B,"_change_data"),$=F(B),(C.type!=="focusout"||B.type!=="radio")&&c._data(B,"_change_data",$);if(A===e||$===A)return;if(A!=null||$)C.type="change",C.liveFired=e,c.event.trigger(C,arguments[1],B)}};c.event.special.change={filters:{focusout:_,beforedeactivate:_,click:function(B){var $=B.target,A=$.type;(A==="radio"||A==="checkbox"||$.nodeName.toLowerCase()==="select")&&_.call(this,B)},keydown:function(B){var $=B.target,A=$.type;(B.keyCode===13&&$.nodeName.toLowerCase()!=="textarea"||B.keyCode===32&&(A==="checkbox"||A==="radio")||A==="select-multiple")&&_.call(this,B)},beforeactivate:function(_){var $=_.target;c._data($,"_change_data",F($))}},setup:function(A,$){if(this.type==="file")return!1;for(var _ in E)c.event.add(this,_+".specialChange",E[_]);return t.test(this.nodeName)},teardown:function($){c.event.remove(this,".specialChange");return t.test(this.nodeName)}},E=c.event.special.change.filters,E.focus=E.beforeactivate}f.addEventListener&&c.each({focus:"focusin",blur:"focusout"},function(B,A){function $(_){var $=c.event.fix(_);$.type=A,$.originalEvent={},c.event.trigger($,null,$.target),$.isDefaultPrevented()&&_.preventDefault()}var _=0;c.event.special[A]={setup:function(){_++===0&&f.addEventListener(B,$,!0)},teardown:function(){--_===0&&f.removeEventListener(B,$,!0)}}}),c.each(["bind","one"],function(_,$){c.fn[$]=function(C,B,_){if(typeof C==="object"){for(var A in C)this[$](A,B,C[A],_);return this}if(c.isFunction(B)||B===!1)_=B,B=e;var E=$==="one"?c.proxy(_,function($){c(this).unbind($,E);return _.apply(this,arguments)}):_;if(C==="unload"&&$!=="one")this.one(C,B,_);else for(var F=0,D=this.length;F<D;F++)c.event.add(this[F],C,E,B);return this}}),c.fn.extend({unbind:function(C,A){if(typeof C!=="object"||C.preventDefault){for(var _=0,$=this.length;_<$;_++)c.event.remove(this[_],C,A)}else for(var B in C)this.unbind(B,C[B]);return this},delegate:function(B,_,A,$){return this.live(_,A,$,B)},undelegate:function(A,$,_){return arguments.length===0?this.unbind("live"):this.die($,null,_,A)},trigger:function(_,$){return this.each(function(){c.event.trigger(_,$,this)})},triggerHandler:function(A,$){if(this[0]){var _=c.Event(A);_.preventDefault(),_.stopPropagation(),c.event.trigger(_,$,this[0]);return _.result}},toggle:function(A){var $=arguments,_=1;while(_<$.length)c.proxy(A,$[_++]);return this.click(c.proxy(A,function(C){var B=(c._data(this,"lastToggle"+A.guid)||0)%_;c._data(this,"lastToggle"+A.guid,B+1),C.preventDefault();return $[B].apply(this,arguments)||!1}))},hover:function(_,$){return this.mouseenter(_).mouseleave($||_)}});var B={focus:"focusin",blur:"focusout",mouseenter:"mouseover",mouseleave:"mouseout"};c.each(["live","die"],function(_,$){c.fn[$]=function(D,C,_,A){var L,M=0,I,J,G,H=A||this.selector,E=A?this:c(this.context);if(typeof D==="object"&&!D.preventDefault){for(var F in D)E[$](F,C,D[F],H);return this}c.isFunction(C)&&(_=C,C=e),D=(D||"").split(" ");while((L=D[M++])!=null){I=u.exec(L),J="",I&&(J=I[0],L=L.replace(u,""));if(L==="hover"){D.push("mouseenter"+J,"mouseleave"+J);continue}G=L,L==="focus"||L==="blur"?(D.push(B[L]+J),L=L+J):L=(B[L]||L)+J;if($==="live"){for(var O=0,N=E.length;O<N;O++)c.event.add(E[O],"live."+K(L,H),{data:C,selector:H,handler:_,origType:L,origHandler:_,preType:G})}else E.unbind("live."+K(L,H),_)}return this}}),c.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error".split(" "),function(_,$){c.fn[$]=function(A,_){_==null&&(_=A,A=null);return arguments.length>0?this.bind($,A,_):this.trigger($)},c.attrFn&&(c.attrFn[$]=!0)}),function(){function L(E,C,D,A,B,$){for(var _=0,G=A.length;_<G;_++){var I=A[_];if(I){var F=!1;I=I[E];while(I){if(I.sizcache===D){F=A[I.sizset];break}if(I.nodeType===1){$||(I.sizcache=D,I.sizset=_);if(typeof C!=="string"){if(I===C){F=!0;break}}else if(H.filter(C,[I]).length>0){F=I;break}}I=I[E]}A[_]=F}}}function M(E,C,D,A,B,$){for(var _=0,G=A.length;_<G;_++){var H=A[_];if(H){var F=!1;H=H[E];while(H){if(H.sizcache===D){F=A[H.sizset];break}H.nodeType===1&&!$&&(H.sizcache=D,H.sizset=_);if(H.nodeName.toLowerCase()===C){F=H;break}H=H[E]}A[_]=F}}}var B=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g,A=0,$=Object.prototype.toString,_=!1,I=!0,J=/\\/g,G=/\W/;[0,0].sort(function(){I=!1;return 0});var H=function(D,A,C,_){C=C||[],A=A||f;var L=A;if(A.nodeType!==1&&A.nodeType!==9)return[];if(!D||typeof D!=="string")return C;var M,J,G,I,T,S,R,P,O=!0,N=H.isXML(A),V=[],U=D;do{B.exec(""),M=B.exec(U);if(M){U=M[3],V.push(M[1]);if(M[2]){I=M[3];break}}}while(M);if(V.length>1&&F.exec(D)){if(V.length===2&&E.relative[V[0]])J=K(V[0]+V[1],A);else{J=E.relative[V[0]]?[A]:H(V.shift(),A);while(V.length)D=V.shift(),E.relative[D]&&(D+=V.shift()),J=K(D,J)}}else{!_&&V.length>1&&A.nodeType===9&&!N&&E.match.ID.test(V[0])&&!E.match.ID.test(V[V.length-1])&&(T=H.find(V.shift(),A,N),A=T.expr?H.filter(T.expr,T.set)[0]:T.set[0]);if(A){T=_?{expr:V.pop(),set:Q(_)}:H.find(V.pop(),V.length===1&&(V[0]==="~"||V[0]==="+")&&A.parentNode?A.parentNode:A,N),J=T.expr?H.filter(T.expr,T.set):T.set,V.length>0?G=Q(J):O=!1;while(V.length)S=V.pop(),R=S,E.relative[S]?R=V.pop():S="",R==null&&(R=A),E.relative[S](G,R,N)}else G=V=[]}G||(G=J),G||H.error(S||D);if($.call(G)==="[object Array]"){if(O){if(A&&A.nodeType===1){for(P=0;G[P]!=null;P++)G[P]&&(G[P]===!0||G[P].nodeType===1&&H.contains(A,G[P]))&&C.push(J[P])}else for(P=0;G[P]!=null;P++)G[P]&&G[P].nodeType===1&&C.push(J[P])}else C.push.apply(C,G)}else Q(G,C);I&&(H(I,L,C,_),H.uniqueSort(C));return C};H.uniqueSort=function(A){if(O){_=I,A.sort(O);if(_)for(var $=1;$<A.length;$++)A[$]===A[$-1]&&A.splice($--,1)}return A},H.matches=function(_,$){return H(_,null,null,$)},H.matchesSelector=function(_,$){return H($,null,null,[_]).length>0},H.find=function(F,C,D){var A;if(!F)return[];for(var B=0,$=E.order.length;B<$;B++){var _,H=E.order[B];if(_=E.leftMatch[H].exec(F)){var G=_[1];_.splice(1,1);if(G.substr(G.length-1)!=="\\"){_[1]=(_[1]||"").replace(J,""),A=E.find[H](_,C,D);if(A!=null){F=F.replace(E.match[H],"");break}}}}A||(A=typeof C.getElementsByTagName!=="undefined"?C.getElementsByTagName("*"):[]);return{set:A,expr:F}},H.filter=function(D,C,A,B){var $,_,K=D,L=[],J=C,I=C&&C[0]&&H.isXML(C[0]);while(D&&C.length){for(var F in E.filter)if(($=E.leftMatch[F].exec(D))!=null&&$[2]){var G,Q,P=E.filter[F],O=$[1];_=!1,$.splice(1,1);if(O.substr(O.length-1)==="\\")continue;J===L&&(L=[]);if(E.preFilter[F]){$=E.preFilter[F]($,J,A,L,B,I);if($){if($===!0)continue}else _=G=!0}if($)for(var N=0;(Q=J[N])!=null;N++)if(Q){G=P(Q,$,N,J);var M=B^!!G;A&&G!=null?M?_=!0:J[N]=!1:M&&(L.push(Q),_=!0)}if(G!==e){A||(J=L),D=D.replace(E.match[F],"");if(!_)return[];break}}if(D===K)if(_==null)H.error(D);else break;K=D}return J},H.error=function($){throw"Syntax error, unrecognized expression: "+$};var E=H.selectors={order:["ID","NAME","TAG"],match:{ID:/#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/,CLASS:/\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/,NAME:/\[name=['"]*((?:[\w\u00c0-\uFFFF\-]|\\.)+)['"]*\]/,ATTR:/\[\s*((?:[\w\u00c0-\uFFFF\-]|\\.)+)\s*(?:(\S?=)\s*(?:(['"])(.*?)\3|(#?(?:[\w\u00c0-\uFFFF\-]|\\.)*)|)|)\s*\]/,TAG:/^((?:[\w\u00c0-\uFFFF\*\-]|\\.)+)/,CHILD:/:(only|nth|last|first)-child(?:\(\s*(even|odd|(?:[+\-]?\d+|(?:[+\-]?\d*)?n\s*(?:[+\-]\s*\d+)?))\s*\))?/,POS:/:(nth|eq|gt|lt|first|last|even|odd)(?:\((\d*)\))?(?=[^\-]|$)/,PSEUDO:/:((?:[\w\u00c0-\uFFFF\-]|\\.)+)(?:\((['"]?)((?:\([^\)]+\)|[^\(\)]*)+)\2\))?/},leftMatch:{},attrMap:{"class":"className","for":"htmlFor"},attrHandle:{href:function($){return $.getAttribute("href")},type:function($){return $.getAttribute("type")}},relative:{"+":function(E,C){var D=typeof C==="string",A=D&&!G.test(C),B=D&&!A;A&&(C=C.toLowerCase());for(var $=0,_=E.length,F;$<_;$++)if(F=E[$]){while((F=F.previousSibling)&&F.nodeType!==1);E[$]=B||F&&F.nodeName.toLowerCase()===C?F||!1:F===C}B&&H.filter(C,E,!0)},">":function(E,C){var D,A=typeof C==="string",B=0,$=E.length;if(A&&!G.test(C)){C=C.toLowerCase();for(;B<$;B++){D=E[B];if(D){var _=D.parentNode;E[B]=_.nodeName.toLowerCase()===C?_:!1}}}else{for(;B<$;B++)D=E[B],D&&(E[B]=A?D.parentNode:D.parentNode===C);A&&H.filter(C,E,!0)}},"":function(E,C,D){var B,$=A++,_=L;typeof C==="string"&&!G.test(C)&&(C=C.toLowerCase(),B=C,_=M),_("parentNode",C,$,E,B,D)},"~":function(E,C,D){var B,$=A++,_=L;typeof C==="string"&&!G.test(C)&&(C=C.toLowerCase(),B=C,_=M),_("previousSibling",C,$,E,B,D)}},find:{ID:function(B,_,A){if(typeof _.getElementById!=="undefined"&&!A){var $=_.getElementById(B[1]);return $&&$.parentNode?[$]:[]}},NAME:function(D,B){if(typeof B.getElementsByName!=="undefined"){var C=[],_=B.getElementsByName(D[1]);for(var A=0,$=_.length;A<$;A++)_[A].getAttribute("name")===D[1]&&C.push(_[A]);return C.length===0?null:C}},TAG:function(_,$){if(typeof $.getElementsByTagName!=="undefined")return $.getElementsByTagName(_[1])}},preFilter:{CLASS:function(E,C,D,A,B,$){E=" "+E[1].replace(J,"")+" ";if($)return E;for(var _=0,F;(F=C[_])!=null;_++)F&&(B^(F.className&&(" "+F.className+" ").replace(/[\t\n\r]/g," ").indexOf(E)>=0)?D||A.push(F):D&&(C[_]=!1));return!1},ID:function($){return $[1].replace(J,"")},TAG:function(_,$){return _[1].replace(J,"").toLowerCase()},CHILD:function(_){if(_[1]==="nth"){_[2]||H.error(_[0]),_[2]=_[2].replace(/^\+|\s*/g,"");var $=/(-?)(\d*)(?:n([+\-]?\d*))?/.exec(_[2]==="even"&&"2n"||_[2]==="odd"&&"2n+1"||!/\D/.test(_[2])&&"0n+"+_[2]||_[2]);_[2]=$[1]+($[2]||1)-0,_[3]=$[3]-0}else _[2]&&H.error(_[0]);_[0]=A++;return _},ATTR:function(F,C,D,A,B,$){var _=F[1]=F[1].replace(J,"");!$&&E.attrMap[_]&&(F[1]=E.attrMap[_]),F[4]=(F[4]||F[5]||"").replace(J,""),F[2]==="~="&&(F[4]=" "+F[4]+" ");return F},PSEUDO:function(D,F,A,C,$){if(D[1]==="not"){if((B.exec(D[3])||"").length>1||/^\w/.test(D[3]))D[3]=H(D[3],null,null,F);else{var _=H.filter(D[3],F,A,!0^$);A||C.push.apply(C,_);return!1}}else if(E.match.POS.test(D[0])||E.match.CHILD.test(D[0]))return!0;return D},POS:function($){$.unshift(!0);return $}},filters:{enabled:function($){return $.disabled===!1&&$.type!=="hidden"},disabled:function($){return $.disabled===!0},checked:function($){return $.checked===!0},selected:function($){$.parentNode&&$.parentNode.selectedIndex;return $.selected===!0},parent:function($){return!!$.firstChild},empty:function($){return!$.firstChild},has:function(A,$,_){return!!H(_[3],A).length},header:function($){return/h\d/i.test($.nodeName)},text:function(A){var $=A.getAttribute("type"),_=A.type;return"text"===_&&($===_||$===null)},radio:function($){return"radio"===$.type},checkbox:function($){return"checkbox"===$.type},file:function($){return"file"===$.type},password:function($){return"password"===$.type},submit:function($){return"submit"===$.type},image:function($){return"image"===$.type},reset:function($){return"reset"===$.type},button:function($){return"button"===$.type||$.nodeName.toLowerCase()==="button"},input:function($){return/input|select|textarea|button/i.test($.nodeName)}},setFilters:{first:function(_,$){return $===0},last:function(B,_,A,$){return _===$.length-1},even:function(_,$){return $%2===0},odd:function(_,$){return $%2===1},lt:function(A,$,_){return $<_[3]-0},gt:function(A,$,_){return $>_[3]-0},nth:function(A,$,_){return _[3]-0===$},eq:function(A,$,_){return _[3]-0===$}},filter:{PSEUDO:function(F,C,D,A){var B=C[1],$=E.filters[B];if($)return $(F,D,C,A);if(B==="contains")return(F.textContent||F.innerText||H.getText([F])||"").indexOf(C[3])>=0;if(B==="not"){var _=C[3];for(var G=0,I=_.length;G<I;G++)if(_[G]===F)return!1;return!0}H.error(B)},CHILD:function(E,C){var D=C[1],A=E;switch(D){case"only":case"first":while(A=A.previousSibling)if(A.nodeType===1)return!1;if(D==="first")return!0;A=E;case"last":while(A=A.nextSibling)if(A.nodeType===1)return!1;return!0;case"nth":var B=C[2],$=C[3];if(B===1&&$===0)return!0;var _=C[0],G=E.parentNode;if(G&&(G.sizcache!==_||!E.nodeIndex)){var H=0;for(A=G.firstChild;A;A=A.nextSibling)A.nodeType===1&&(A.nodeIndex=++H);G.sizcache=_}var F=E.nodeIndex-$;return B===0?F===0:F%B===0&&F/B>=0}},ID:function(_,$){return _.nodeType===1&&_.getAttribute("id")===$},TAG:function(_,$){return $==="*"&&_.nodeType===1||_.nodeName.toLowerCase()===$},CLASS:function(_,$){return(" "+(_.className||_.getAttribute("class"))+" ").indexOf($)>-1},ATTR:function(F,C){var D=C[1],A=E.attrHandle[D]?E.attrHandle[D](F):F[D]!=null?F[D]:F.getAttribute(D),B=A+"",$=C[2],_=C[4];return A==null?$==="!=":$==="="?B===_:$==="*="?B.indexOf(_)>=0:$==="~="?(" "+B+" ").indexOf(_)>=0:_?$==="!="?B!==_:$==="^="?B.indexOf(_)===0:$==="$="?B.substr(B.length-_.length)===_:$==="|="?B===_||B.substr(0,_.length+1)===_+"-":!1:B&&A!==!1},POS:function(D,B,C,_){var A=B[2],$=E.setFilters[A];if($)return $(D,C,B,_)}}},F=E.match.POS,C=function(_,$){return"\\"+($-0+1)};for(var D in E.match)E.match[D]=new RegExp(E.match[D].source+/(?![^\[]*\])(?![^\(]*\))/.source),E.leftMatch[D]=new RegExp(/(^(?:.|\r|\n)*?)/.source+E.match[D].source.replace(/\\(\d+)/g,C));var Q=function(_,$){_=Array.prototype.slice.call(_,0);if($){$.push.apply($,_);return $}return _};try{Array.prototype.slice.call(f.documentElement.childNodes,0)[0].nodeType}catch(P){Q=function(D,B){var C=0,_=B||[];if($.call(D)==="[object Array]")Array.prototype.push.apply(_,D);else if(typeof D.length==="number"){for(var A=D.length;C<A;C++)_.push(D[C])}else for(;D[C];C++)_.push(D[C]);return _}}var O,N;f.documentElement.compareDocumentPosition?O=function(A,$){if(A===$){_=!0;return 0}if(!A.compareDocumentPosition||!$.compareDocumentPosition)return A.compareDocumentPosition?-1:1;return A.compareDocumentPosition($)&4?-1:1}:(O=function(E,C){var D,A,B=[],$=[],H=E.parentNode,I=C.parentNode,F=H;if(E===C){_=!0;return 0}if(H===I)return N(E,C);if(!H)return-1;if(!I)return 1;while(F)B.unshift(F),F=F.parentNode;F=I;while(F)$.unshift(F),F=F.parentNode;D=B.length,A=$.length;for(var G=0;G<D&&G<A;G++)if(B[G]!==$[G])return N(B[G],$[G]);return G===D?N(E,$[G],-1):N(B[G],C,1)},N=function(B,_,A){if(B===_)return A;var $=B.nextSibling;while($){if($===_)return-1;$=$.nextSibling}return 1}),H.getText=function(B){var _="",A;for(var $=0;B[$];$++)A=B[$],A.nodeType===3||A.nodeType===4?_+=A.nodeValue:A.nodeType!==8&&(_+=H.getText(A.childNodes));return _},function(){var A=f.createElement("div"),$="script"+(new Date).getTime(),_=f.documentElement;A.innerHTML="<a name='"+$+"'/>",_.insertBefore(A,_.firstChild),f.getElementById($)&&(E.find.ID=function(B,A,$){if(typeof A.getElementById!=="undefined"&&!$){var _=A.getElementById(B[1]);return _?_.id===B[1]||typeof _.getAttributeNode!=="undefined"&&_.getAttributeNode("id").nodeValue===B[1]?[_]:e:[]}},E.filter.ID=function(A,$){var _=typeof A.getAttributeNode!=="undefined"&&A.getAttributeNode("id");return A.nodeType===1&&_&&_.nodeValue===$}),_.removeChild(A),_=A=null}(),function(){var $=f.createElement("div");$.appendChild(f.createComment("")),$.getElementsByTagName("*").length>0&&(E.find.TAG=function(C,A){var B=A.getElementsByTagName(C[1]);if(C[1]==="*"){var $=[];for(var _=0;B[_];_++)B[_].nodeType===1&&$.push(B[_]);B=$}return B}),$.innerHTML="<a href='#'></a>",$.firstChild&&typeof $.firstChild.getAttribute!=="undefined"&&$.firstChild.getAttribute("href")!=="#"&&(E.attrHandle.href=function($){return $.getAttribute("href",2)}),$=null}(),f.querySelectorAll&&function(){var B=H,A=f.createElement("div"),$="__sizzle__";A.innerHTML="<p class='TEST'></p>";if(!A.querySelectorAll||A.querySelectorAll(".TEST").length!==0){H=function(D,C,_,A){C=C||f;if(!A&&!H.isXML(C)){var K=/^(\w+$)|^\.([\w\-]+$)|^#([\w\-]+$)/.exec(D);if(K&&(C.nodeType===1||C.nodeType===9)){if(K[1])return Q(C.getElementsByTagName(D),_);if(K[2]&&E.find.CLASS&&C.getElementsByClassName)return Q(C.getElementsByClassName(K[2]),_)}if(C.nodeType===9){if(D==="body"&&C.body)return Q([C.body],_);if(K&&K[3]){var L=C.getElementById(K[3]);if(!L||!L.parentNode)return Q([],_);if(L.id===K[3])return Q([L],_)}try{return Q(C.querySelectorAll(D),_)}catch(J){}}else if(C.nodeType===1&&C.nodeName.toLowerCase()!=="object"){var I=C,F=C.getAttribute("id"),G=F||$,O=C.parentNode,N=/^\s*[+~]/.test(D);F?G=G.replace(/'/g,"\\$&"):C.setAttribute("id",G),N&&O&&(C=C.parentNode);try{if(!N||O)return Q(C.querySelectorAll("[id='"+G+"'] "+D),_)}catch(M){}finally{F||I.removeAttribute("id")}}}return B(D,C,_,A)};for(var _ in B)H[_]=B[_];A=null}}(),function(){var C=f.documentElement,B=C.matchesSelector||C.mozMatchesSelector||C.webkitMatchesSelector||C.msMatchesSelector;if(B){var _=!B.call(f.createElement("div"),"div"),A=!1;try{B.call(f.documentElement,"[test!='']:sizzle")}catch($){A=!0}H.matchesSelector=function(F,D){D=D.replace(/\=\s*([^'"\]]*)\s*\]/g,"='$1']");if(!H.isXML(F)){try{if(A||!E.match.PSEUDO.test(D)&&!/!=/.test(D)){var $=B.call(F,D);if($||!_||F.document&&F.document.nodeType!==11)return $}}catch(C){}}return H(D,null,null,[F]).length>0}}}(),function(){var $=f.createElement("div");$.innerHTML="<div class='test e'></div><div class='test'></div>";if($.getElementsByClassName&&$.getElementsByClassName("e").length!==0){$.lastChild.className="e";if($.getElementsByClassName("e").length===1)return;E.order.splice(1,0,"CLASS"),E.find.CLASS=function(A,$,_){if(typeof $.getElementsByClassName!=="undefined"&&!_)return $.getElementsByClassName(A[1])},$=null}}(),f.documentElement.contains?H.contains=function(_,$){return _!==$&&(_.contains?_.contains($):!0)}:f.documentElement.compareDocumentPosition?H.contains=function(_,$){return!!(_.compareDocumentPosition($)&16)}:H.contains=function(){return!1},H.isXML=function(_){var $=(_?_.ownerDocument||_:0).documentElement;return $?$.nodeName!=="HTML":!1};var K=function(F,C){var D,A=[],B="",$=C.nodeType?[C]:C;while(D=E.match.PSEUDO.exec(F))B+=D[0],F=F.replace(E.match.PSEUDO,"");F=E.relative[F]?F+"*":F;for(var _=0,G=$.length;_<G;_++)H(F,$[_],A);return H.filter(B,A)};c.find=H,c.expr=H.selectors,c.expr[":"]=c.expr.filters,c.unique=H.uniqueSort,c.text=H.getText,c.isXMLDoc=H.isXML,c.contains=H.contains}();var L=/Until$/,M=/^(?:parents|prevUntil|prevAll)/,N=/,/,G=/^.[^:#\[\.,]*$/,H=Array.prototype.slice,I=c.expr.match.POS,J={children:!0,contents:!0,next:!0,prev:!0};c.fn.extend({find:function(D){var B=this.pushStack("","find",D),C=0;for(var A=0,$=this.length;A<$;A++){C=B.length,c.find(D,this[A],B);if(A>0)for(var _=C;_<B.length;_++)for(var E=0;E<C;E++)if(B[E]===B[_]){B.splice(_--,1);break}}return B},has:function(_){var $=c(_);return this.filter(function(){for(var A=0,_=$.length;A<_;A++)if(c.contains(this,$[A]))return!0})},not:function($){return this.pushStack(S(this,$,!1),"not",$)},filter:function($){return this.pushStack(S(this,$,!0),"filter",$)},is:function($){return!!$&&c.filter($,this).length>0},closest:function(D,B){var C=[],A,$,_=this[0];if(c.isArray(D)){var H,J,F={},G=1;if(_&&D.length){for(A=0,$=D.length;A<$;A++)J=D[A],F[J]||(F[J]=c.expr.match.POS.test(J)?c(J,B||this.context):J);while(_&&_.ownerDocument&&_!==B){for(J in F)H=F[J],(H.jquery?H.index(_)>-1:c(_).is(H))&&C.push({selector:J,elem:_,level:G});_=_.parentNode,G++}}return C}var E=I.test(D)?c(D,B||this.context):null;for(A=0,$=this.length;A<$;A++){_=this[A];while(_){if(E?E.index(_)>-1:c.find.matchesSelector(_,D)){C.push(_);break}_=_.parentNode;if(!_||!_.ownerDocument||_===B)break}}C=C.length>1?c.unique(C):C;return this.pushStack(C,"closest",D)},index:function($){if(!$||typeof $==="string")return c.inArray(this[0],$?c($):this.parent().children());return c.inArray($.jquery?$[0]:$,this)},add:function(B,_){var A=typeof B==="string"?c(B,_):c.makeArray(B),$=c.merge(this.get(),A);return this.pushStack(T(A[0])||T($[0])?$:c.unique($))},andSelf:function(){return this.add(this.prevObject)}}),c.each({parent:function(_){var $=_.parentNode;return $&&$.nodeType!==11?$:null},parents:function($){return c.dir($,"parentNode")},parentsUntil:function(A,$,_){return c.dir(A,"parentNode",_)},next:function($){return c.nth($,2,"nextSibling")},prev:function($){return c.nth($,2,"previousSibling")},nextAll:function($){return c.dir($,"nextSibling")},prevAll:function($){return c.dir($,"previousSibling")},nextUntil:function(A,$,_){return c.dir(A,"nextSibling",_)},prevUntil:function(A,$,_){return c.dir(A,"previousSibling",_)},siblings:function($){return c.sibling($.parentNode.firstChild,$)},children:function($){return c.sibling($.firstChild)},contents:function($){return c.nodeName($,"iframe")?$.contentDocument||$.contentWindow.document:c.makeArray($.childNodes)}},function(_,$){c.fn[_]=function(D,C){var A=c.map(this,$,D),B=H.call(arguments);L.test(_)||(C=D),C&&typeof C==="string"&&(A=c.filter(C,A)),A=this.length>1&&!J[_]?c.unique(A):A,(this.length>1||N.test(C))&&M.test(_)&&(A=A.reverse());return this.pushStack(A,_,B.join(","))}}),c.extend({filter:function(A,$,_){_&&(A=":not("+A+")");return $.length===1?c.find.matchesSelector($[0],A)?[$[0]]:[]:c.find.matches(A,$)},dir:function(C,B,A){var $=[],_=C[B];while(_&&_.nodeType!==9&&(A===e||_.nodeType!==1||!c(_).is(A)))_.nodeType===1&&$.push(_),_=_[B];return $},nth:function(C,A,B,$){A=A||1;var _=0;for(;C;C=C[B])if(C.nodeType===1&&++_===A)break;return C},sibling:function(A,$){var _=[];for(;A;A=A.nextSibling)A.nodeType===1&&A!==$&&_.push(A);return _}});var V=/ jQuery\d+="(?:\d+|null)"/g,U=/^\s+/,P=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/ig,O=/<([\w:]+)/,R=/<tbody/i,Q=/<|&#?\w+;/,Y=/<(?:script|object|embed|option|style)/i,X=/checked\s*(?:[^=]|=\s*.checked.)/i,Z={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],area:[1,"<map>","</map>"],_default:[0,"",""]};Z.optgroup=Z.option,Z.tbody=Z.tfoot=Z.colgroup=Z.caption=Z.thead,Z.th=Z.td,c.support.htmlSerialize||(Z._default=[1,"div<div>","</div>"]),c.fn.extend({text:function($){if(c.isFunction($))return this.each(function(_){var A=c(this);A.text($.call(this,_,A.text()))});if(typeof $!=="object"&&$!==e)return this.empty().append((this[0]&&this[0].ownerDocument||f).createTextNode($));return c.text(this)},wrapAll:function(_){if(c.isFunction(_))return this.each(function($){c(this).wrapAll(_.call(this,$))});if(this[0]){var $=c(_,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&$.insertBefore(this[0]),$.map(function(){var $=this;while($.firstChild&&$.firstChild.nodeType===1)$=$.firstChild;return $}).append(this)}return this},wrapInner:function($){if(c.isFunction($))return this.each(function(_){c(this).wrapInner($.call(this,_))});return this.each(function(){var _=c(this),A=_.contents();A.length?A.wrapAll($):_.append($)})},wrap:function($){return this.each(function(){c(this).wrapAll($)})},unwrap:function(){return this.parent().each(function(){c.nodeName(this,"body")||c(this).replaceWith(this.childNodes)}).end()},append:function(){return this.domManip(arguments,!0,function($){this.nodeType===1&&this.appendChild($)})},prepend:function(){return this.domManip(arguments,!0,function($){this.nodeType===1&&this.insertBefore($,this.firstChild)})},before:function(){if(this[0]&&this[0].parentNode)return this.domManip(arguments,!1,function($){this.parentNode.insertBefore($,this)});if(arguments.length){var $=c(arguments[0]);$.push.apply($,this.toArray());return this.pushStack($,"before",arguments)}},after:function(){if(this[0]&&this[0].parentNode)return this.domManip(arguments,!1,function($){this.parentNode.insertBefore($,this.nextSibling)});if(arguments.length){var $=this.pushStack(this,"after",arguments);$.push.apply($,c(arguments[0]).toArray());return $}},remove:function(B,_){for(var A=0,$;($=this[A])!=null;A++)if(!B||c.filter(B,[$]).length)!_&&$.nodeType===1&&(c.cleanData($.getElementsByTagName("*")),c.cleanData([$])),$.parentNode&&$.parentNode.removeChild($);return this},empty:function(){for(var _=0,$;($=this[_])!=null;_++){$.nodeType===1&&c.cleanData($.getElementsByTagName("*"));while($.firstChild)$.removeChild($.firstChild)}return this},clone:function(_,$){_=_==null?!1:_,$=$==null?_:$;return this.map(function(){return c.clone(this,_,$)})},html:function(B){if(B===e)return this[0]&&this[0].nodeType===1?this[0].innerHTML.replace(V,""):null;if(typeof B!=="string"||Y.test(B)||!c.support.leadingWhitespace&&U.test(B)||Z[(O.exec(B)||["",""])[1].toLowerCase()])c.isFunction(B)?this.each(function($){var _=c(this);_.html(B.call(this,$,_.html()))}):this.empty().append(B);else{B=B.replace(P,"<$1></$2>");try{for(var A=0,_=this.length;A<_;A++)this[A].nodeType===1&&(c.cleanData(this[A].getElementsByTagName("*")),this[A].innerHTML=B)}catch($){this.empty().append(B)}}return this},replaceWith:function($){if(this[0]&&this[0].parentNode){if(c.isFunction($))return this.each(function(A){var B=c(this),_=B.html();B.replaceWith($.call(this,A,_))});typeof $!=="string"&&($=c($).detach());return this.each(function(){var _=this.nextSibling,A=this.parentNode;c(this).remove(),_?c(_).before($):c(A).append($)})}return this.length?this.pushStack(c(c.isFunction($)?$():$),"replaceWith",$):this},detach:function($){return this.remove($,!0)},domManip:function(D,C,B){var _,A,J,K,H=D[0],I=[];if(!c.support.checkClone&&arguments.length===3&&typeof H==="string"&&X.test(H))return this.each(function(){c(this).domManip(D,C,B,!0)});if(c.isFunction(H))return this.each(function($){var _=c(this);D[0]=H.call(this,$,C?_.html():e),_.domManip(D,C,B)});if(this[0]){K=H&&H.parentNode,c.support.parentNode&&K&&K.nodeType===11&&K.childNodes.length===this.length?_={fragment:K}:_=c.buildFragment(D,this,I),J=_.fragment,J.childNodes.length===1?A=J=J.firstChild:A=J.firstChild;if(A){C=C&&c.nodeName(A,"tr");for(var F=0,G=this.length,E=G-1;F<G;F++)B.call(C?$(this[F],A):this[F],_.cacheable||G>1&&F<E?c.clone(J,!0,!0):J)}I.length&&c.each(I,W0)}return this}}),c.buildFragment=function(C,B,A){var $,_,D,E=B&&B[0]?B[0].ownerDocument||B[0]:f;C.length===1&&typeof C[0]==="string"&&C[0].length<512&&E===f&&C[0].charAt(0)==="<"&&!Y.test(C[0])&&(c.support.checkClone||!X.test(C[0]))&&(_=!0,D=c.fragments[C[0]],D&&(D!==1&&($=D))),$||($=E.createDocumentFragment(),c.clean(C,E,$,A)),_&&(c.fragments[C[0]]=D?$:1);return{fragment:$,cacheable:_}},c.fragments={},c.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(_,$){c.fn[_]=function(D){var C=[],A=c(D),B=this.length===1&&this[0].parentNode;if(B&&B.nodeType===11&&B.childNodes.length===1&&A.length===1){A[$](this[0]);return this}for(var F=0,G=A.length;F<G;F++){var E=(F>0?this.clone(!0):this).get();c(A[F])[$](E),C=C.concat(E)}return this.pushStack(C,_,A.selector)}}),c.extend({clone:function(D,B,C){var A=D.cloneNode(!0),$,_,E;if((!c.support.noCloneEvent||!c.support.noCloneChecked)&&(D.nodeType===1||D.nodeType===11)&&!c.isXMLDoc(D)){j0(D,A),$=V0(D),_=V0(A);for(E=0;$[E];++E)j0($[E],_[E])}if(B){W(D,A);if(C){$=V0(D),_=V0(A);for(E=0;$[E];++E)W($[E],_[E])}}return A},clean:function(C,B,A,$){B=B||f,typeof B.createElement==="undefined"&&(B=B.ownerDocument||B[0]&&B[0].ownerDocument||f);var _=[];for(var J=0,K;(K=C[J])!=null;J++){typeof K==="number"&&(K+="");if(!K)continue;if(typeof K!=="string"||Q.test(K)){if(typeof K==="string"){K=K.replace(P,"<$1></$2>");var H=(O.exec(K)||["",""])[1].toLowerCase(),I=Z[H]||Z._default,F=I[0],G=B.createElement("div");G.innerHTML=I[1]+K+I[2];while(F--)G=G.lastChild;if(!c.support.tbody){var D=R.test(K),E=H==="table"&&!D?G.firstChild&&G.firstChild.childNodes:I[1]==="<table>"&&!D?G.childNodes:[];for(var L=E.length-1;L>=0;--L)c.nodeName(E[L],"tbody")&&!E[L].childNodes.length&&E[L].parentNode.removeChild(E[L])}!c.support.leadingWhitespace&&U.test(K)&&G.insertBefore(B.createTextNode(U.exec(K)[0]),G.firstChild),K=G.childNodes}}else K=B.createTextNode(K);K.nodeType?_.push(K):_=c.merge(_,K)}if(A)for(J=0;_[J];J++)!$||!c.nodeName(_[J],"script")||_[J].type&&_[J].type.toLowerCase()!=="text/javascript"?(_[J].nodeType===1&&_.splice.apply(_,[J+1,0].concat(c.makeArray(_[J].getElementsByTagName("script")))),A.appendChild(_[J])):$.push(_[J].parentNode?_[J].parentNode.removeChild(_[J]):_[J]);return _},cleanData:function(D){var B,C,A=c.cache,$=c.expando,_=c.event.special,G=c.support.deleteExpando;for(var H=0,E;(E=D[H])!=null;H++){if(E.nodeName&&c.noData[E.nodeName.toLowerCase()])continue;C=E[c.expando];if(C){B=A[C]&&A[C][$];if(B&&B.events){for(var F in B.events)_[F]?c.event.remove(E,F):c.removeEvent(E,F,B.handle);B.handle&&(B.handle.elem=null)}G?delete E[c.expando]:E.removeAttribute&&E.removeAttribute(c.expando),delete A[C]}}}});var X0=/alpha\([^)]*\)/i,Y0=/opacity=([^)]*)/,R0=/-([a-z])/ig,S0=/([A-Z]|^ms)/g,T0=/^-?\d+(?:px)?$/i,U0=/^-?\d/,N0={position:"absolute",visibility:"hidden",display:"block"},O0=["Left","Right"],P0=["Top","Bottom"],Q0,J0,K0,L0=function(_,$){return $.toUpperCase()};c.fn.css=function(_,$){if(arguments.length===2&&$===e)return this;return c.access(this,_,$,!0,function(A,_,$){return $!==e?c.style(A,_,$):c.css(A,_)})},c.extend({cssHooks:{opacity:{get:function(A,$){if($){var _=Q0(A,"opacity","opacity");return _===""?"1":_}return A.style.opacity}}},cssNumber:{zIndex:!0,fontWeight:!0,opacity:!0,zoom:!0,lineHeight:!0},cssProps:{"float":c.support.cssFloat?"cssFloat":"styleFloat"},style:function(C,B,A,$){if(C&&C.nodeType!==3&&C.nodeType!==8&&C.style){var _,F=c.camelCase(B),G=C.style,D=c.cssHooks[F];B=c.cssProps[F]||F;if(A===e){if(D&&"get"in D&&(_=D.get(C,!1,$))!==e)return _;return G[B]}if(typeof A==="number"&&isNaN(A)||A==null)return;typeof A==="number"&&!c.cssNumber[F]&&(A+="px");if(!D||!("set"in D)||(A=D.set(C,A))!==e){try{G[B]=A}catch(E){}}}},css:function(C,B,A){var $,_=c.camelCase(B),D=c.cssHooks[_];B=c.cssProps[_]||_;if(D&&"get"in D&&($=D.get(C,!0,A))!==e)return $;if(Q0)return Q0(C,B,_)},swap:function(C,A,B){var $={};for(var _ in A)$[_]=C.style[_],C.style[_]=A[_];B.call(C);for(_ in A)C.style[_]=$[_]},camelCase:function($){return $.replace(R0,L0)}}),c.curCSS=c.css,c.each(["height","width"],function(_,$){c.cssHooks[$]={get:function(C,B,A){var _;if(B){C.offsetWidth!==0?_=M0(C,$,A):c.swap(C,N0,function(){_=M0(C,$,A)});if(_<=0){_=Q0(C,$,$),_==="0px"&&K0&&(_=K0(C,$,$));if(_!=null)return _===""||_==="auto"?"0px":_}if(_<0||_==null){_=C.style[$];return _===""||_==="auto"?"0px":_}return typeof _==="string"?_:_+"px"}},set:function(_,$){if(!T0.test($))return $;$=parseFloat($);if($>=0)return $+"px"}}}),c.support.opacity||(c.cssHooks.opacity={get:function(_,$){return Y0.test(($&&_.currentStyle?_.currentStyle.filter:_.style.filter)||"")?parseFloat(RegExp.$1)/100+"":$?"1":""},set:function(C,A){var B=C.style;B.zoom=1;var _=c.isNaN(A)?"":"alpha(opacity="+A*100+")",$=B.filter||"";B.filter=X0.test($)?$.replace(X0,_):B.filter+" "+_}}),c(function(){c.support.reliableMarginRight||(c.cssHooks.marginRight={get:function(A,$){var _;c.swap(A,{display:"inline-block"},function(){$?_=Q0(A,"margin-right","marginRight"):_=A.style.marginRight});return _}})}),f.defaultView&&f.defaultView.getComputedStyle&&(J0=function(C,B,A){var $,_,D;A=A.replace(S0,"-$1").toLowerCase();if(!(_=C.ownerDocument.defaultView))return e;if(D=_.getComputedStyle(C,null))$=D.getPropertyValue(A),$===""&&!c.contains(C.ownerDocument.documentElement,C)&&($=c.style(C,A));return $}),f.documentElement.currentStyle&&(K0=function(D,B){var C,_=D.currentStyle&&D.currentStyle[B],A=D.runtimeStyle&&D.runtimeStyle[B],$=D.style;!T0.test(_)&&U0.test(_)&&(C=$.left,A&&(D.runtimeStyle.left=D.currentStyle.left),$.left=B==="fontSize"?"1em":_||0,_=$.pixelLeft+"px",$.left=C,A&&(D.runtimeStyle.left=A));return _===""?"auto":_}),Q0=J0||K0,c.expr&&c.expr.filters&&(c.expr.filters.hidden=function(A){var $=A.offsetWidth,_=A.offsetHeight;return $===0&&_===0||!c.support.reliableHiddenOffsets&&(A.style.display||c.css(A,"display"))==="none"},c.expr.filters.visible=function($){return!c.expr.filters.hidden($)});var G0=/%20/g,F0=/\[\]$/,I0=/\r?\n/g,H0=/#.*$/,C0=/^(.*?):[ \t]*([^\r\n]*)\r?$/mg,B0=/^(?:color|date|datetime|email|hidden|month|number|password|range|search|tel|text|time|url|week)$/i,E0=/^(?:about|app|app\-storage|.+\-extension|file|widget):$/,D0=/^(?:GET|HEAD)$/,A0=/^\/\//,g0=/\?/,B1=/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,C1=/^(?:select|textarea)/i,_1=/\s+/,A1=/([?&])_=[^&]*/,F1=/(^|\-)([a-z])/g,G1=function(A,$,_){return $+_.toUpperCase()},D1=/^([\w\+\.\-]+:)(?:\/\/([^\/?#:]*)(?::(\d+))?)?/,E1=c.fn.load,v0={},w0={},t0,u0;try{t0=f.location.href}catch(z0){t0=f.createElement("a"),t0.href="",t0=t0.href}u0=D1.exec(t0.toLowerCase())||[],c.fn.extend({load:function(C,B,A){if(typeof C!=="string"&&E1)return E1.apply(this,arguments);if(!this.length)return this;var $=C.indexOf(" ");if($>=0){var _=C.slice($,C.length);C=C.slice(0,$)}var D="GET";B&&(c.isFunction(B)?(A=B,B=e):typeof B==="object"&&(B=c.param(B,c.ajaxSettings.traditional),D="POST"));var E=this;c.ajax({url:C,type:D,dataType:"html",data:B,complete:function(C,$,B){B=C.responseText,C.isResolved()&&(C.done(function($){B=$}),E.html(_?c("<div>").append(B.replace(B1,"")).find(_):B)),A&&E.each(A,[B,$,C])}});return this},serialize:function(){return c.param(this.serializeArray())},serializeArray:function(){return this.map(function(){return this.elements?c.makeArray(this.elements):this}).filter(function(){return this.name&&!this.disabled&&(this.checked||C1.test(this.nodeName)||B0.test(this.type))}).map(function(A,$){var _=c(this).val();return _==null?null:c.isArray(_)?c.map(_,function(A,_){return{name:$.name,value:A.replace(I0,"\r\n")}}):{name:$.name,value:_.replace(I0,"\r\n")}}).get()}}),c.each("ajaxStart ajaxStop ajaxComplete ajaxError ajaxSuccess ajaxSend".split(" "),function(_,$){c.fn[$]=function(_){return this.bind($,_)}}),c.each(["get","post"],function(_,$){c[$]=function(C,B,_,A){c.isFunction(B)&&(A=A||_,_=B,B=e);return c.ajax({type:$,url:C,data:B,success:_,dataType:A})}}),c.extend({getScript:function(_,$){return c.get(_,e,$,"script")},getJSON:function(A,$,_){return c.get(A,$,_,"json")},ajaxSetup:function(A,$){$?c.extend(!0,A,c.ajaxSettings,$):($=A,A=c.extend(!0,c.ajaxSettings,$));for(var _ in{context:1,url:1})_ in $?A[_]=$[_]:_ in c.ajaxSettings&&(A[_]=c.ajaxSettings[_]);return A},ajaxSettings:{url:t0,isLocal:E0.test(u0[1]),global:!0,type:"GET",contentType:"application/x-www-form-urlencoded",processData:!0,async:!0,accepts:{xml:"application/xml, text/xml",html:"text/html",text:"text/plain",json:"application/json, text/javascript","*":"*/*"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText"},converters:{"* text":g.String,"text html":!0,"text json":c.parseJSON,"text xml":c.parseXML}},ajaxPrefilter:$1(v0),ajaxTransport:$1(w0),ajax:function(C,B){function M(M,F,R,O){if(Q!==2){Q=2,S&&clearTimeout(S),E=e,G=O||"",N.readyState=M?4:0;var L,D,C,B=R?o0(A,N,R):e,V,U;if(M>=200&&M<300||M===304){if(A.ifModified){if(V=N.getResponseHeader("Last-Modified"))c.lastModified[I]=V;if(U=N.getResponseHeader("Etag"))c.etag[I]=U}if(M===304)F="notmodified",L=!0;else{try{D=n0(A,B),F="success",L=!0}catch(T){F="parsererror",C=T}}}else{C=F;if(!F||M)F="error",M<0&&(M=0)}N.status=M,N.statusText=F,L?J.resolveWith($,[D,F,N]):J.rejectWith($,[N,F,C]),N.statusCode(H),H=e,P&&_.trigger("ajax"+(L?"Success":"Error"),[N,A,L?D:C]),K.resolveWith($,[N,F]),P&&(_.trigger("ajaxComplete",[N,A]),--c.active||c.event.trigger("ajaxStop"))}}typeof C==="object"&&(B=C,C=e),B=B||{};var A=c.ajaxSetup({},B),$=A.context||A,_=$!==A&&($.nodeType||$ instanceof c)?c($):c.event,J=c.Deferred(),K=c._Deferred(),H=A.statusCode||{},I,F={},G,D,E,S,R,Q=0,P,O,N={readyState:0,setRequestHeader:function(_,$){Q||(F[_.toLowerCase().replace(F1,G1)]=$);return this},getAllResponseHeaders:function(){return Q===2?G:null},getResponseHeader:function(_){var $;if(Q===2){if(!D){D={};while($=C0.exec(G))D[$[1].toLowerCase()]=$[2]}$=D[_.toLowerCase()]}return $===e?null:$},overrideMimeType:function($){Q||(A.mimeType=$);return this},abort:function($){$=$||"abort",E&&E.abort($),M(0,$);return this}};J.promise(N),N.success=N.done,N.error=N.fail,N.complete=K.done,N.statusCode=function(_){if(_){var $;if(Q<2){for($ in _)H[$]=[H[$],_[$]]}else $=_[N.status],N.then($,$)}return this},A.url=((C||A.url)+"").replace(H0,"").replace(A0,u0[1]+"//"),A.dataTypes=c.trim(A.dataType||"*").toLowerCase().split(_1),A.crossDomain==null&&(R=D1.exec(A.url.toLowerCase()),A.crossDomain=R&&(R[1]!=u0[1]||R[2]!=u0[2]||(R[3]||(R[1]==="http:"?80:443))!=(u0[3]||(u0[1]==="http:"?80:443)))),A.data&&A.processData&&typeof A.data!=="string"&&(A.data=c.param(A.data,A.traditional)),x0(v0,A,B,N);if(Q===2)return!1;P=A.global,A.type=A.type.toUpperCase(),A.hasContent=!D0.test(A.type),P&&c.active++===0&&c.event.trigger("ajaxStart");if(!A.hasContent){A.data&&(A.url+=(g0.test(A.url)?"&":"?")+A.data),I=A.url;if(A.cache===!1){var L=c.now(),U=A.url.replace(A1,"$1_="+L);A.url=U+(U===A.url?(g0.test(A.url)?"&":"?")+"_="+L:"")}}if(A.data&&A.hasContent&&A.contentType!==!1||B.contentType)F["Content-Type"]=A.contentType;A.ifModified&&(I=I||A.url,c.lastModified[I]&&(F["If-Modified-Since"]=c.lastModified[I]),c.etag[I]&&(F["If-None-Match"]=c.etag[I])),F.Accept=A.dataTypes[0]&&A.accepts[A.dataTypes[0]]?A.accepts[A.dataTypes[0]]+(A.dataTypes[0]!=="*"?", */*; q=0.01":""):A.accepts["*"];for(O in A.headers)N.setRequestHeader(O,A.headers[O]);if(A.beforeSend&&(A.beforeSend.call($,N,A)===!1||Q===2)){N.abort();return!1}for(O in{success:1,error:1,complete:1})N[O](A[O]);E=x0(w0,A,B,N);if(E){N.readyState=1,P&&_.trigger("ajaxSend",[N,A]),A.async&&A.timeout>0&&(S=setTimeout(function(){N.abort("timeout")},A.timeout));try{Q=1,E.send(F,M)}catch(T){status<2?M(-1,T):c.error(T)}}else M(-1,"No Transport");return N},param:function(C,B){var A=[],$=function(_,$){$=c.isFunction($)?$():$,A[A.length]=encodeURIComponent(_)+"="+encodeURIComponent($)};B===e&&(B=c.ajaxSettings.traditional);if(c.isArray(C)||C.jquery&&!c.isPlainObject(C))c.each(C,function(){$(this.name,this.value)});else for(var _ in C)y0(_,C[_],B,$);return A.join("&").replace(G0,"+")}}),c.extend({active:0,lastModified:{},etag:{}});var m0=c.now(),l0=/(\=)\?(&|$)|\?\?/i;c.ajaxSetup({jsonp:"callback",jsonpCallback:function(){return c.expando+"_"+m0++}}),c.ajaxPrefilter("json jsonp",function(B,C,A){var $=typeof B.data==="string";if(B.dataTypes[0]==="jsonp"||C.jsonpCallback||C.jsonp!=null||B.jsonp!==!1&&(l0.test(B.url)||$&&l0.test(B.data))){var _,H=B.jsonpCallback=c.isFunction(B.jsonpCallback)?B.jsonpCallback():B.jsonpCallback,I=g[H],F=B.url,G=B.data,D="$1"+H+"$2",E=function(){g[H]=I,_&&c.isFunction(I)&&g[H](_[0])};B.jsonp!==!1&&(F=F.replace(l0,D),B.url===F&&($&&(G=G.replace(l0,D)),B.data===G&&(F+=(/\?/.test(F)?"&":"?")+B.jsonp+"="+H))),B.url=F,B.data=G,g[H]=function($){_=[$]},A.then(E,E),B.converters["script json"]=function(){_||c.error(H+" was not called");return _[0]},B.dataTypes[0]="json";return"script"}}),c.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/javascript|ecmascript/},converters:{"text script":function($){c.globalEval($);return $}}}),c.ajaxPrefilter("script",function($){$.cache===e&&($.cache=!1),$.crossDomain&&($.type="GET",$.global=!1)}),c.ajaxTransport("script",function(A){if(A.crossDomain){var $,_=f.head||f.getElementsByTagName("head")[0]||f.documentElement;return{send:function(B,C){$=f.createElement("script"),$.async="async",A.scriptCharset&&($.charset=A.scriptCharset),$.src=A.url,$.onload=$.onreadystatechange=function(B,A){if(!$.readyState||/loaded|complete/.test($.readyState))$.onload=$.onreadystatechange=null,_&&$.parentNode&&_.removeChild($),$=e,A||C(200,"success")},_.insertBefore($,_.firstChild)},abort:function(){$&&$.onload(0,1)}}}});var s0=c.now(),r0,q0;c.ajaxSettings.xhr=g.ActiveXObject?function(){return!this.isLocal&&i0()||h0()}:i0,q0=c.ajaxSettings.xhr(),c.support.ajax=!!q0,c.support.cors=q0&&"withCredentials"in q0,q0=e,c.support.ajax&&c.ajaxTransport(function(_){if(!_.crossDomain||c.support.cors){var $;return{send:function(C,A){var B=_.xhr(),E,F;_.username?B.open(_.type,_.url,_.async,_.username,_.password):B.open(_.type,_.url,_.async);if(_.xhrFields)for(F in _.xhrFields)B[F]=_.xhrFields[F];_.mimeType&&B.overrideMimeType&&B.overrideMimeType(_.mimeType),!_.crossDomain&&!C["X-Requested-With"]&&(C["X-Requested-With"]="XMLHttpRequest");try{for(F in C)B.setRequestHeader(F,C[F])}catch(D){}B.send(_.hasContent&&_.data||null),$=function(C,L){var J,K,H,I,F;try{if($&&(L||B.readyState===4)){$=e,E&&(B.onreadystatechange=c.noop,delete r0[E]);if(L)B.readyState!==4&&B.abort();else{J=B.status,H=B.getAllResponseHeaders(),I={},F=B.responseXML,F&&F.documentElement&&(I.xml=F),I.text=B.responseText;try{K=B.statusText}catch(G){K=""}J||!_.isLocal||_.crossDomain?J===1223&&(J=204):J=I.text?200:404}}}catch(D){L||A(-1,D)}I&&A(J,K,I,H)},_.async&&B.readyState!==4?(r0||(r0={},p0()),E=s0++,B.onreadystatechange=r0[E]=$):$()},abort:function(){$&&$(0,1)}}}});var k0={},_0=/^(?:toggle|show|hide)$/,$0=/^([+\-]=)?([\d+.\-]+)([a-z%]*)$/i,e0,f0=[["height","marginTop","marginBottom","paddingTop","paddingBottom"],["width","marginLeft","marginRight","paddingLeft","paddingRight"],["opacity"]];c.fn.extend({show:function(D,B,C){var A,$;if(D||D===0)return this.animate(c0("show",3),D,B,C);for(var _=0,E=this.length;_<E;_++)A=this[_],$=A.style.display,!c._data(A,"olddisplay")&&$==="none"&&($=A.style.display=""),$===""&&c.css(A,"display")==="none"&&c._data(A,"olddisplay",d0(A.nodeName));for(_=0;_<E;_++){A=this[_],$=A.style.display;if($===""||$==="none")A.style.display=c._data(A,"olddisplay")||""}return this},hide:function(D,B,C){if(D||D===0)return this.animate(c0("hide",3),D,B,C);for(var A=0,$=this.length;A<$;A++){var _=c.css(this[A],"display");_!=="none"&&!c._data(this[A],"olddisplay")&&c._data(this[A],"olddisplay",_)}for(A=0;A<$;A++)this[A].style.display="none";return this},_toggle:c.fn.toggle,toggle:function(B,_,A){var $=typeof B==="boolean";c.isFunction(B)&&c.isFunction(_)?this._toggle.apply(this,arguments):B==null||$?this.each(function(){var _=$?B:c(this).is(":hidden");c(this)[_?"show":"hide"]()}):this.animate(c0("toggle",3),B,_,A);return this},fadeTo:function(B,_,A,$){return this.filter(":hidden").css("opacity",0).show().end().animate({opacity:_},B,A,$)},animate:function(C,A,B,_){var $=c.speed(A,B,_);if(c.isEmptyObject(C))return this.each($.complete);return this[$.queue===!1?"each":"queue"](function(){var B=c.extend({},$),D,A=this.nodeType===1,_=A&&c(this).is(":hidden"),F=this;for(D in C){var G=c.camelCase(D);D!==G&&(C[G]=C[D],delete C[D],D=G);if(C[D]==="hide"&&_||C[D]==="show"&&!_)return B.complete.call(this);if(A&&(D==="height"||D==="width")){B.overflow=[this.style.overflow,this.style.overflowX,this.style.overflowY];if(c.css(this,"display")==="inline"&&c.css(this,"float")==="none")if(c.support.inlineBlockNeedsLayout){var E=d0(this.nodeName);E==="inline"?this.style.display="inline-block":(this.style.display="inline",this.style.zoom=1)}else this.style.display="inline-block"}c.isArray(C[D])&&((B.specialEasing=B.specialEasing||{})[D]=C[D][1],C[D]=C[D][0])}B.overflow!=null&&(this.style.overflow="hidden"),B.curAnim=c.extend({},C),c.each(C,function(D,A){var $=new c.fx(F,B,D);if(_0.test(A))$[A==="toggle"?_?"show":"hide":A](C);else{var I=$0.exec(A),G=$.cur();if(I){var H=parseFloat(I[2]),E=I[3]||(c.cssNumber[D]?"":"px");E!=="px"&&(c.style(F,D,(H||1)+E),G=(H||1)/$.cur()*G,c.style(F,D,G+E)),I[1]&&(H=(I[1]==="-="?-1:1)*H+G),$.custom(G,H,E)}else $.custom(G,A,"")}});return!0})},stop:function(A,$){var _=c.timers;A&&this.queue([]),this.each(function(){for(var A=_.length-1;A>=0;A--)_[A].elem===this&&($&&_[A](!0),_.splice(A,1))}),$||this.dequeue();return this}}),c.each({slideDown:c0("show",1),slideUp:c0("hide",1),slideToggle:c0("toggle",1),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(_,$){c.fn[_]=function(B,A,_){return this.animate($,B,A,_)}}),c.extend({speed:function(B,_,A){var $=B&&typeof B==="object"?c.extend({},B):{complete:A||!A&&_||c.isFunction(B)&&B,duration:B,easing:A&&_||_&&!c.isFunction(_)&&_};$.duration=c.fx.off?0:typeof $.duration==="number"?$.duration:$.duration in c.fx.speeds?c.fx.speeds[$.duration]:c.fx.speeds._default,$.old=$.complete,$.complete=function(){$.queue!==!1&&c(this).dequeue(),c.isFunction($.old)&&$.old.call(this)};return $},easing:{linear:function(B,_,A,$){return A+$*B},swing:function(B,_,A,$){return(-Math.cos(B*Math.PI)/2+0.5)*$+A}},timers:[],fx:function(A,$,_){this.options=$,this.elem=A,this.prop=_,$.orig||($.orig={})}}),c.fx.prototype={update:function(){this.options.step&&this.options.step.call(this.elem,this.now,this),(c.fx.step[this.prop]||c.fx.step._default)(this)},cur:function(){if(this.elem[this.prop]!=null&&(!this.elem.style||this.elem.style[this.prop]==null))return this.elem[this.prop];var _,$=c.css(this.elem,this.prop);return isNaN(_=parseFloat($))?!$||$==="auto"?0:$:_},custom:function(D,B,C){function _($){return A.step($)}var A=this,$=c.fx;this.startTime=c.now(),this.start=D,this.end=B,this.unit=C||this.unit||(c.cssNumber[this.prop]?"":"px"),this.now=this.start,this.pos=this.state=0,_.elem=this.elem,_()&&c.timers.push(_)&&!e0&&(e0=setInterval($.tick,$.interval))},show:function(){this.options.orig[this.prop]=c.style(this.elem,this.prop),this.options.show=!0,this.custom(this.prop==="width"||this.prop==="height"?1:0,this.cur()),c(this.elem).show()},hide:function(){this.options.orig[this.prop]=c.style(this.elem,this.prop),this.options.hide=!0,this.custom(this.cur(),0)},step:function(D){var B=c.now(),C=!0;if(D||B>=this.options.duration+this.startTime){this.now=this.end,this.pos=this.state=1,this.update(),this.options.curAnim[this.prop]=!0;for(var A in this.options.curAnim)this.options.curAnim[A]!==!0&&(C=!1);if(C){if(this.options.overflow!=null&&!c.support.shrinkWrapBlocks){var $=this.elem,_=this.options;c.each(["","X","Y"],function(B,A){$.style["overflow"+A]=_.overflow[B]})}this.options.hide&&c(this.elem).hide();if(this.options.hide||this.options.show)for(var G in this.options.curAnim)c.style(this.elem,G,this.options.orig[G]);this.options.complete.call(this.elem)}return!1}var H=B-this.startTime;this.state=H/this.options.duration;var E=this.options.specialEasing&&this.options.specialEasing[this.prop],F=this.options.easing||(c.easing.swing?"swing":"linear");this.pos=c.easing[E||F](this.state,H,0,1,this.options.duration),this.now=this.start+(this.end-this.start)*this.pos,this.update();return!0}},c.extend(c.fx,{tick:function(){var _=c.timers;for(var $=0;$<_.length;$++)_[$]()||_.splice($--,1);_.length||c.fx.stop()},interval:13,stop:function(){clearInterval(e0),e0=null},speeds:{slow:600,fast:200,_default:400},step:{opacity:function($){c.style($.elem,"opacity",$.now)},_default:function($){$.elem.style&&$.elem.style[$.prop]!=null?$.elem.style[$.prop]=($.prop==="width"||$.prop==="height"?Math.max(0,$.now):$.now)+$.unit:$.elem[$.prop]=$.now}}}),c.expr&&c.expr.filters&&(c.expr.filters.animated=function($){return c.grep(c.timers,function(_){return $===_.elem}).length});var a0=/^t(?:able|d|h)$/i,b0=/^(?:body|html)$/i;"getBoundingClientRect"in f.documentElement?c.fn.offset=function(D){var B=this[0],C;if(D)return this.each(function($){c.offset.setOffset(this,D,$)});if(!B||!B.ownerDocument)return null;if(B===B.ownerDocument.body)return c.offset.bodyOffset(B);try{C=B.getBoundingClientRect()}catch(A){}var $=B.ownerDocument,_=$.documentElement;if(!C||!c.contains(_,B))return C?{top:C.top,left:C.left}:{top:0,left:0};var K=$.body,L=Z0($),I=_.clientTop||K.clientTop||0,J=_.clientLeft||K.clientLeft||0,G=L.pageYOffset||c.support.boxModel&&_.scrollTop||K.scrollTop,H=L.pageXOffset||c.support.boxModel&&_.scrollLeft||K.scrollLeft,E=C.top+G-I,F=C.left+H-J;return{top:E,left:F}}:c.fn.offset=function(D){var B=this[0];if(D)return this.each(function($){c.offset.setOffset(this,D,$)});if(!B||!B.ownerDocument)return null;if(B===B.ownerDocument.body)return c.offset.bodyOffset(B);c.offset.initialize();var C,A=B.offsetParent,$=B,_=B.ownerDocument,I=_.documentElement,J=_.body,G=_.defaultView,H=G?G.getComputedStyle(B,null):B.currentStyle,E=B.offsetTop,F=B.offsetLeft;while((B=B.parentNode)&&B!==J&&B!==I){if(c.offset.supportsFixedPosition&&H.position==="fixed")break;C=G?G.getComputedStyle(B,null):B.currentStyle,E-=B.scrollTop,F-=B.scrollLeft,B===A&&(E+=B.offsetTop,F+=B.offsetLeft,c.offset.doesNotAddBorder&&(!c.offset.doesAddBorderForTableAndCells||!a0.test(B.nodeName))&&(E+=parseFloat(C.borderTopWidth)||0,F+=parseFloat(C.borderLeftWidth)||0),$=A,A=B.offsetParent),c.offset.subtractsBorderForOverflowNotVisible&&C.overflow!=="visible"&&(E+=parseFloat(C.borderTopWidth)||0,F+=parseFloat(C.borderLeftWidth)||0),H=C}if(H.position==="relative"||H.position==="static")E+=J.offsetTop,F+=J.offsetLeft;c.offset.supportsFixedPosition&&H.position==="fixed"&&(E+=Math.max(I.scrollTop,J.scrollTop),F+=Math.max(I.scrollLeft,J.scrollLeft));return{top:E,left:F}},c.offset={initialize:function(){var C=f.body,B=f.createElement("div"),A,$,_,E,F=parseFloat(c.css(C,"marginTop"))||0,D="<div style='position:absolute;top:0;left:0;margin:0;border:5px solid #000;padding:0;width:1px;height:1px;'><div></div></div><table style='position:absolute;top:0;left:0;margin:0;border:5px solid #000;padding:0;width:1px;height:1px;' cellpadding='0' cellspacing='0'><tr><td></td></tr></table>";c.extend(B.style,{position:"absolute",top:0,left:0,margin:0,border:0,width:"1px",height:"1px",visibility:"hidden"}),B.innerHTML=D,C.insertBefore(B,C.firstChild),A=B.firstChild,$=A.firstChild,E=A.nextSibling.firstChild.firstChild,this.doesNotAddBorder=$.offsetTop!==5,this.doesAddBorderForTableAndCells=E.offsetTop===5,$.style.position="fixed",$.style.top="20px",this.supportsFixedPosition=$.offsetTop===20||$.offsetTop===15,$.style.position=$.style.top="",A.style.overflow="hidden",A.style.position="relative",this.subtractsBorderForOverflowNotVisible=$.offsetTop===-5,this.doesNotIncludeMarginInBodyOffset=C.offsetTop!==F,C.removeChild(B),c.offset.initialize=c.noop},bodyOffset:function(A){var $=A.offsetTop,_=A.offsetLeft;c.offset.initialize(),c.offset.doesNotIncludeMarginInBodyOffset&&($+=parseFloat(c.css(A,"marginTop"))||0,_+=parseFloat(c.css(A,"marginLeft"))||0);return{top:$,left:_}},setOffset:function(D,B,C){var A=c.css(D,"position");A==="static"&&(D.style.position="relative");var $=c(D),_=$.offset(),J=c.css(D,"top"),K=c.css(D,"left"),H=(A==="absolute"||A==="fixed")&&c.inArray("auto",[J,K])>-1,I={},F={},G,E;H&&(F=$.position()),G=H?F.top:parseInt(J,10)||0,E=H?F.left:parseInt(K,10)||0,c.isFunction(B)&&(B=B.call(D,C,_)),B.top!=null&&(I.top=B.top-_.top+G),B.left!=null&&(I.left=B.left-_.left+E),"using"in B?B.using.call(D,I):$.css(I)}},c.fn.extend({position:function(){if(!this[0])return null;var B=this[0],_=this.offsetParent(),A=this.offset(),$=b0.test(_[0].nodeName)?{top:0,left:0}:_.offset();A.top-=parseFloat(c.css(B,"marginTop"))||0,A.left-=parseFloat(c.css(B,"marginLeft"))||0,$.top+=parseFloat(c.css(_[0],"borderTopWidth"))||0,$.left+=parseFloat(c.css(_[0],"borderLeftWidth"))||0;return{top:A.top-$.top,left:A.left-$.left}},offsetParent:function(){return this.map(function(){var $=this.offsetParent||f.body;while($&&(!b0.test($.nodeName)&&c.css($,"position")==="static"))$=$.offsetParent;return $})}}),c.each(["Left","Top"],function(A,_){var $="scroll"+_;c.fn[$]=function(C){var _=this[0],B;if(!_)return null;if(C!==e)return this.each(function(){B=Z0(this),B?B.scrollTo(A?c(B).scrollLeft():C,A?C:c(B).scrollTop()):this[$]=C});B=Z0(_);return B?"pageXOffset"in B?B[A?"pageYOffset":"pageXOffset"]:c.support.boxModel&&B.document.documentElement[$]||B.document.body[$]:_[$]}}),c.each(["Height","Width"],function(A,_){var $=_.toLowerCase();c.fn["inner"+_]=function(){return this[0]?parseFloat(c.css(this[0],$,"padding")):null},c.fn["outer"+_]=function(_){return this[0]?parseFloat(c.css(this[0],$,_?"margin":"border")):null},c.fn[$]=function(C){var A=this[0];if(!A)return C==null?null:this;if(c.isFunction(C))return this.each(function(_){var A=c(this);A[$](C.call(this,_,A[$]()))});if(c.isWindow(A)){var B=A.document.documentElement["client"+_];return A.document.compatMode==="CSS1Compat"&&B||A.document.body["client"+_]||B}if(A.nodeType===9)return Math.max(A.documentElement["client"+_],A.body["scroll"+_],A.documentElement["scroll"+_],A.body["offset"+_],A.documentElement["offset"+_]);if(C===e){var D=c.css(A,$),E=parseFloat(D);return c.isNaN(E)?D:E}return this.css($,typeof C==="string"?C:C+"px")}}),g.jQuery=g.$=c})(window)