package org.come.action.role;

import io.netty.channel.ChannelHandlerContext;

import java.math.BigDecimal;

import org.come.action.IAction;
import org.come.action.monitor.MonitorUtil;
import org.come.bean.LoginResult;
import org.come.server.GameServer;
import org.come.tool.WriteOut;
import org.come.until.GsonUtil;
/**
 * 修改银两
 * <AUTHOR>
 * @date 2017年12月27日 上午11:48:19
 * 
 */ 
public class DeductiontaelAction implements IAction {

	@Override
	public void action(ChannelHandlerContext ctx, String message) {
		String[] vals = message.split("\\|");
		if (vals.length != 2) {
			return;
		}
		long money = Long.parseLong(vals[1]);
		if (money <= 0) {
			return;
		}
		LoginResult loginResult = GameServer.getAllLoginRole().get(ctx);
		if (vals[0].equals("0")) {
			// 扣除角色银两
			long gold = loginResult.getGold().longValue() - money;
			if (gold < 0) {
				String v = "扣除银量异常:" + message + ":" + GsonUtil.getGsonUtil().getgson().toJson(loginResult);
				WriteOut.addtxt(v, 9999);
				gold = 0;
			}
			loginResult.setGold(new BigDecimal(gold));
			MonitorUtil.getMoney().useD(money);
		} else if (vals[0].equals("1")) {
			// 扣除角色师贡
			long saveGold = loginResult.getSavegold().longValue() - money;
			if (saveGold < 0) {
				String v = "扣除师贡异常:" + message + ":" + GsonUtil.getGsonUtil().getgson().toJson(loginResult);
				WriteOut.addtxt(v, 9999);
				saveGold = 0;
			}
			loginResult.setSavegold(new BigDecimal(saveGold));
		} else if (vals[0].equals("2")) {
			// 扣除角色经验
			long exp = loginResult.getExperience().longValue() - money;
			if (exp < 0) {
				String v = "扣除经验异常:" + message + ":" + GsonUtil.getGsonUtil().getgson().toJson(loginResult);
				WriteOut.addtxt(v, 9999);
				exp = 0;
			}
			loginResult.setExperience(new BigDecimal(exp));
		} else if (vals[0].equals("3")) {
			// 优先扣除角色师贡
			long saveGold = loginResult.getSavegold().longValue() - money;
			if (saveGold < 0) {
				long gold = loginResult.getGold().longValue() - Math.abs(saveGold);
				if (gold < 0) {
					String v = "扣除银量异常:" + message + ":" + GsonUtil.getGsonUtil().getgson().toJson(loginResult);
					WriteOut.addtxt(v, 9999);
					gold = 0;
				}
				loginResult.setGold(new BigDecimal(gold));
				MonitorUtil.getMoney().useD(money);
				saveGold = 0;
			}
			loginResult.setSavegold(new BigDecimal(saveGold));
		}


	}

}
