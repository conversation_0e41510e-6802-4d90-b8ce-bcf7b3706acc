import {E as ge, u as be} from "./vendor.45bae780.js";

var T = {exports: {}}, z = function (e, t) {
    return function () {
        for (var a = new Array(arguments.length), i = 0; i < a.length; i++) a[i] = arguments[i];
        return e.apply(t, a)
    }
}, we = z, g = Object.prototype.toString;

function U(r) {
    return g.call(r) === "[object Array]"
}

function B(r) {
    return typeof r == "undefined"
}

function Ee(r) {
    return r !== null && !B(r) && r.constructor !== null && !B(r.constructor) && typeof r.constructor.isBuffer == "function" && r.constructor.isBuffer(r)
}

function xe(r) {
    return g.call(r) === "[object ArrayBuffer]"
}

function Se(r) {
    return typeof FormData != "undefined" && r instanceof FormData
}

function Ce(r) {
    var e;
    return typeof ArrayBuffer != "undefined" && ArrayBuffer.isView ? e = ArrayBuffer.isView(r) : e = r && r.buffer && r.buffer instanceof ArrayBuffer, e
}

function Oe(r) {
    return typeof r == "string"
}

function Re(r) {
    return typeof r == "number"
}

function V(r) {
    return r !== null && typeof r == "object"
}

function O(r) {
    if (g.call(r) !== "[object Object]") return !1;
    var e = Object.getPrototypeOf(r);
    return e === null || e === Object.prototype
}

function Ae(r) {
    return g.call(r) === "[object Date]"
}

function je(r) {
    return g.call(r) === "[object File]"
}

function Ne(r) {
    return g.call(r) === "[object Blob]"
}

function K(r) {
    return g.call(r) === "[object Function]"
}

function Pe(r) {
    return V(r) && K(r.pipe)
}

function ke(r) {
    return typeof URLSearchParams != "undefined" && r instanceof URLSearchParams
}

function $e(r) {
    return r.trim ? r.trim() : r.replace(/^\s+|\s+$/g, "")
}

function Te() {
    return typeof navigator != "undefined" && (navigator.product === "ReactNative" || navigator.product === "NativeScript" || navigator.product === "NS") ? !1 : typeof window != "undefined" && typeof document != "undefined"
}

function D(r, e) {
    if (!(r === null || typeof r == "undefined")) if (typeof r != "object" && (r = [r]), U(r)) for (var t = 0, n = r.length; t < n; t++) e.call(null, r[t], t, r); else for (var a in r) Object.prototype.hasOwnProperty.call(r, a) && e.call(null, r[a], a, r)
}

function L() {
    var r = {};

    function e(a, i) {
        O(r[i]) && O(a) ? r[i] = L(r[i], a) : O(a) ? r[i] = L({}, a) : U(a) ? r[i] = a.slice() : r[i] = a
    }

    for (var t = 0, n = arguments.length; t < n; t++) D(arguments[t], e);
    return r
}

function Ue(r, e, t) {
    return D(e, function (a, i) {
        t && typeof a == "function" ? r[i] = we(a, t) : r[i] = a
    }), r
}

function Be(r) {
    return r.charCodeAt(0) === 65279 && (r = r.slice(1)), r
}

var p = {
    isArray: U,
    isArrayBuffer: xe,
    isBuffer: Ee,
    isFormData: Se,
    isArrayBufferView: Ce,
    isString: Oe,
    isNumber: Re,
    isObject: V,
    isPlainObject: O,
    isUndefined: B,
    isDate: Ae,
    isFile: je,
    isBlob: Ne,
    isFunction: K,
    isStream: Pe,
    isURLSearchParams: ke,
    isStandardBrowserEnv: Te,
    forEach: D,
    merge: L,
    extend: Ue,
    trim: $e,
    stripBOM: Be
}, E = p;

function W(r) {
    return encodeURIComponent(r).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]")
}

var X = function (e, t, n) {
    if (!t) return e;
    var a;
    if (n) a = n(t); else if (E.isURLSearchParams(t)) a = t.toString(); else {
        var i = [];
        E.forEach(t, function (s, v) {
            s === null || typeof s == "undefined" || (E.isArray(s) ? v = v + "[]" : s = [s], E.forEach(s, function (f) {
                E.isDate(f) ? f = f.toISOString() : E.isObject(f) && (f = JSON.stringify(f)), i.push(W(v) + "=" + W(f))
            }))
        }), a = i.join("&")
    }
    if (a) {
        var o = e.indexOf("#");
        o !== -1 && (e = e.slice(0, o)), e += (e.indexOf("?") === -1 ? "?" : "&") + a
    }
    return e
}, De = p;

function R() {
    this.handlers = []
}

R.prototype.use = function (e, t, n) {
    return this.handlers.push({
        fulfilled: e,
        rejected: t,
        synchronous: n ? n.synchronous : !1,
        runWhen: n ? n.runWhen : null
    }), this.handlers.length - 1
};
R.prototype.eject = function (e) {
    this.handlers[e] && (this.handlers[e] = null)
};
R.prototype.forEach = function (e) {
    De.forEach(this.handlers, function (n) {
        n !== null && e(n)
    })
};
var Le = R, qe = p, Ie = function (e, t) {
        qe.forEach(e, function (a, i) {
            i !== t && i.toUpperCase() === t.toUpperCase() && (e[t] = a, delete e[i])
        })
    }, G = function (e, t, n, a, i) {
        return e.config = t, n && (e.code = n), e.request = a, e.response = i, e.isAxiosError = !0, e.toJSON = function () {
            return {
                message: this.message,
                name: this.name,
                description: this.description,
                number: this.number,
                fileName: this.fileName,
                lineNumber: this.lineNumber,
                columnNumber: this.columnNumber,
                stack: this.stack,
                config: this.config,
                code: this.code
            }
        }, e
    }, Fe = G, Y = function (e, t, n, a, i) {
        var o = new Error(e);
        return Fe(o, t, n, a, i)
    }, He = Y, Me = function (e, t, n) {
        var a = n.config.validateStatus;
        !n.status || !a || a(n.status) ? e(n) : t(He("Request failed with status code " + n.status, n.config, null, n.request, n))
    }, A = p, _e = A.isStandardBrowserEnv() ? function () {
        return {
            write: function (t, n, a, i, o, u) {
                var s = [];
                s.push(t + "=" + encodeURIComponent(n)), A.isNumber(a) && s.push("expires=" + new Date(a).toGMTString()), A.isString(i) && s.push("path=" + i), A.isString(o) && s.push("domain=" + o), u === !0 && s.push("secure"), document.cookie = s.join("; ")
            }, read: function (t) {
                var n = document.cookie.match(new RegExp("(^|;\\s*)(" + t + ")=([^;]*)"));
                return n ? decodeURIComponent(n[3]) : null
            }, remove: function (t) {
                this.write(t, "", Date.now() - 864e5)
            }
        }
    }() : function () {
        return {
            write: function () {
            }, read: function () {
                return null
            }, remove: function () {
            }
        }
    }(), Je = function (e) {
        return /^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)
    }, ze = function (e, t) {
        return t ? e.replace(/\/+$/, "") + "/" + t.replace(/^\/+/, "") : e
    }, Ve = Je, Ke = ze, We = function (e, t) {
        return e && !Ve(t) ? Ke(e, t) : t
    }, q = p,
    Xe = ["age", "authorization", "content-length", "content-type", "etag", "expires", "from", "host", "if-modified-since", "if-unmodified-since", "last-modified", "location", "max-forwards", "proxy-authorization", "referer", "retry-after", "user-agent"],
    Ge = function (e) {
        var t = {}, n, a, i;
        return e && q.forEach(e.split(`
`), function (u) {
            if (i = u.indexOf(":"), n = q.trim(u.substr(0, i)).toLowerCase(), a = q.trim(u.substr(i + 1)), n) {
                if (t[n] && Xe.indexOf(n) >= 0) return;
                n === "set-cookie" ? t[n] = (t[n] ? t[n] : []).concat([a]) : t[n] = t[n] ? t[n] + ", " + a : a
            }
        }), t
    }, Z = p, Ye = Z.isStandardBrowserEnv() ? function () {
        var e = /(msie|trident)/i.test(navigator.userAgent), t = document.createElement("a"), n;

        function a(i) {
            var o = i;
            return e && (t.setAttribute("href", o), o = t.href), t.setAttribute("href", o), {
                href: t.href,
                protocol: t.protocol ? t.protocol.replace(/:$/, "") : "",
                host: t.host,
                search: t.search ? t.search.replace(/^\?/, "") : "",
                hash: t.hash ? t.hash.replace(/^#/, "") : "",
                hostname: t.hostname,
                port: t.port,
                pathname: t.pathname.charAt(0) === "/" ? t.pathname : "/" + t.pathname
            }
        }

        return n = a(window.location.href), function (o) {
            var u = Z.isString(o) ? a(o) : o;
            return u.protocol === n.protocol && u.host === n.host
        }
    }() : function () {
        return function () {
            return !0
        }
    }(), j = p, Ze = Me, Qe = _e, er = X, rr = We, tr = Ge, nr = Ye, I = Y, Q = function (e) {
        return new Promise(function (n, a) {
            var i = e.data, o = e.headers, u = e.responseType;
            j.isFormData(i) && delete o["Content-Type"];
            var s = new XMLHttpRequest;
            if (e.auth) {
                var v = e.auth.username || "", w = e.auth.password ? unescape(encodeURIComponent(e.auth.password)) : "";
                o.Authorization = "Basic " + btoa(v + ":" + w)
            }
            var f = rr(e.baseURL, e.url);
            s.open(e.method.toUpperCase(), er(f, e.params, e.paramsSerializer), !0), s.timeout = e.timeout;

            function l() {
                if (!!s) {
                    var b = "getAllResponseHeaders" in s ? tr(s.getAllResponseHeaders()) : null,
                        y = !u || u === "text" || u === "json" ? s.responseText : s.response,
                        S = {data: y, status: s.status, statusText: s.statusText, headers: b, config: e, request: s};
                    Ze(n, a, S), s = null
                }
            }

            if ("onloadend" in s ? s.onloadend = l : s.onreadystatechange = function () {
                !s || s.readyState !== 4 || s.status === 0 && !(s.responseURL && s.responseURL.indexOf("file:") === 0) || setTimeout(l)
            }, s.onabort = function () {
                !s || (a(I("Request aborted", e, "ECONNABORTED", s)), s = null)
            }, s.onerror = function () {
                a(I("Network Error", e, null, s)), s = null
            }, s.ontimeout = function () {
                var y = "timeout of " + e.timeout + "ms exceeded";
                e.timeoutErrorMessage && (y = e.timeoutErrorMessage), a(I(y, e, e.transitional && e.transitional.clarifyTimeoutError ? "ETIMEDOUT" : "ECONNABORTED", s)), s = null
            }, j.isStandardBrowserEnv()) {
                var c = (e.withCredentials || nr(f)) && e.xsrfCookieName ? Qe.read(e.xsrfCookieName) : void 0;
                c && (o[e.xsrfHeaderName] = c)
            }
            "setRequestHeader" in s && j.forEach(o, function (y, S) {
                typeof i == "undefined" && S.toLowerCase() === "content-type" ? delete o[S] : s.setRequestHeader(S, y)
            }), j.isUndefined(e.withCredentials) || (s.withCredentials = !!e.withCredentials), u && u !== "json" && (s.responseType = e.responseType), typeof e.onDownloadProgress == "function" && s.addEventListener("progress", e.onDownloadProgress), typeof e.onUploadProgress == "function" && s.upload && s.upload.addEventListener("progress", e.onUploadProgress), e.cancelToken && e.cancelToken.promise.then(function (y) {
                !s || (s.abort(), a(y), s = null)
            }), i || (i = null), s.send(i)
        })
    }, d = p, ee = Ie, ar = G, sr = {"Content-Type": "application/x-www-form-urlencoded"};

function re(r, e) {
    !d.isUndefined(r) && d.isUndefined(r["Content-Type"]) && (r["Content-Type"] = e)
}

function ir() {
    var r;
    return (typeof XMLHttpRequest != "undefined" || typeof process != "undefined" && Object.prototype.toString.call(process) === "[object process]") && (r = Q), r
}

function or(r, e, t) {
    if (d.isString(r)) try {
        return (e || JSON.parse)(r), d.trim(r)
    } catch (n) {
        if (n.name !== "SyntaxError") throw n
    }
    return (t || JSON.stringify)(r)
}

var N = {
    transitional: {silentJSONParsing: !0, forcedJSONParsing: !0, clarifyTimeoutError: !1},
    adapter: ir(),
    transformRequest: [function (e, t) {
        return ee(t, "Accept"), ee(t, "Content-Type"), d.isFormData(e) || d.isArrayBuffer(e) || d.isBuffer(e) || d.isStream(e) || d.isFile(e) || d.isBlob(e) ? e : d.isArrayBufferView(e) ? e.buffer : d.isURLSearchParams(e) ? (re(t, "application/x-www-form-urlencoded;charset=utf-8"), e.toString()) : d.isObject(e) || t && t["Content-Type"] === "application/json" ? (re(t, "application/json"), or(e)) : e
    }],
    transformResponse: [function (e) {
        var t = this.transitional, n = t && t.silentJSONParsing, a = t && t.forcedJSONParsing,
            i = !n && this.responseType === "json";
        if (i || a && d.isString(e) && e.length) try {
            return JSON.parse(e)
        } catch (o) {
            if (i) throw o.name === "SyntaxError" ? ar(o, this, "E_JSON_PARSE") : o
        }
        return e
    }],
    timeout: 0,
    xsrfCookieName: "XSRF-TOKEN",
    xsrfHeaderName: "X-XSRF-TOKEN",
    maxContentLength: -1,
    maxBodyLength: -1,
    validateStatus: function (e) {
        return e >= 200 && e < 300
    }
};
N.headers = {common: {Accept: "application/json, text/plain, */*"}};
d.forEach(["delete", "get", "head"], function (e) {
    N.headers[e] = {}
});
d.forEach(["post", "put", "patch"], function (e) {
    N.headers[e] = d.merge(sr)
});
var F = N, ur = p, cr = F, lr = function (e, t, n) {
    var a = this || cr;
    return ur.forEach(n, function (o) {
        e = o.call(a, e, t)
    }), e
}, te = function (e) {
    return !!(e && e.__CANCEL__)
}, ne = p, H = lr, fr = te, dr = F;

function M(r) {
    r.cancelToken && r.cancelToken.throwIfRequested()
}

var hr = function (e) {
    M(e), e.headers = e.headers || {}, e.data = H.call(e, e.data, e.headers, e.transformRequest), e.headers = ne.merge(e.headers.common || {}, e.headers[e.method] || {}, e.headers), ne.forEach(["delete", "get", "head", "post", "put", "patch", "common"], function (a) {
        delete e.headers[a]
    });
    var t = e.adapter || dr.adapter;
    return t(e).then(function (a) {
        return M(e), a.data = H.call(e, a.data, a.headers, e.transformResponse), a
    }, function (a) {
        return fr(a) || (M(e), a && a.response && (a.response.data = H.call(e, a.response.data, a.response.headers, e.transformResponse))), Promise.reject(a)
    })
}, h = p, ae = function (e, t) {
    t = t || {};
    var n = {}, a = ["url", "method", "data"], i = ["headers", "auth", "proxy", "params"],
        o = ["baseURL", "transformRequest", "transformResponse", "paramsSerializer", "timeout", "timeoutMessage", "withCredentials", "adapter", "responseType", "xsrfCookieName", "xsrfHeaderName", "onUploadProgress", "onDownloadProgress", "decompress", "maxContentLength", "maxBodyLength", "maxRedirects", "transport", "httpAgent", "httpsAgent", "cancelToken", "socketPath", "responseEncoding"],
        u = ["validateStatus"];

    function s(l, c) {
        return h.isPlainObject(l) && h.isPlainObject(c) ? h.merge(l, c) : h.isPlainObject(c) ? h.merge({}, c) : h.isArray(c) ? c.slice() : c
    }

    function v(l) {
        h.isUndefined(t[l]) ? h.isUndefined(e[l]) || (n[l] = s(void 0, e[l])) : n[l] = s(e[l], t[l])
    }

    h.forEach(a, function (c) {
        h.isUndefined(t[c]) || (n[c] = s(void 0, t[c]))
    }), h.forEach(i, v), h.forEach(o, function (c) {
        h.isUndefined(t[c]) ? h.isUndefined(e[c]) || (n[c] = s(void 0, e[c])) : n[c] = s(void 0, t[c])
    }), h.forEach(u, function (c) {
        c in t ? n[c] = s(e[c], t[c]) : c in e && (n[c] = s(void 0, e[c]))
    });
    var w = a.concat(i).concat(o).concat(u), f = Object.keys(e).concat(Object.keys(t)).filter(function (c) {
        return w.indexOf(c) === -1
    });
    return h.forEach(f, v), n
};
const pr = "axios", mr = "0.21.4", vr = "Promise based HTTP client for the browser and node.js", yr = "index.js", br = {
        test: "grunt test",
        start: "node ./sandbox/server.js",
        build: "NODE_ENV=production grunt build",
        preversion: "npm test",
        version: "npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json",
        postversion: "git push && git push --tags",
        examples: "node ./examples/server.js",
        coveralls: "cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js",
        fix: "eslint --fix lib/**/*.js"
    }, gr = {type: "git", url: "https://github.com/axios/axios.git"}, wr = ["xhr", "http", "ajax", "promise", "node"],
    Er = "Matt Zabriskie", xr = "MIT", Sr = {url: "https://github.com/axios/axios/issues"},
    Cr = "https://axios-http.com", Or = {
        coveralls: "^3.0.0",
        "es6-promise": "^4.2.4",
        grunt: "^1.3.0",
        "grunt-banner": "^0.6.0",
        "grunt-cli": "^1.2.0",
        "grunt-contrib-clean": "^1.1.0",
        "grunt-contrib-watch": "^1.0.0",
        "grunt-eslint": "^23.0.0",
        "grunt-karma": "^4.0.0",
        "grunt-mocha-test": "^0.13.3",
        "grunt-ts": "^6.0.0-beta.19",
        "grunt-webpack": "^4.0.2",
        "istanbul-instrumenter-loader": "^1.0.0",
        "jasmine-core": "^2.4.1",
        karma: "^6.3.2",
        "karma-chrome-launcher": "^3.1.0",
        "karma-firefox-launcher": "^2.1.0",
        "karma-jasmine": "^1.1.1",
        "karma-jasmine-ajax": "^0.1.13",
        "karma-safari-launcher": "^1.0.0",
        "karma-sauce-launcher": "^4.3.6",
        "karma-sinon": "^1.0.5",
        "karma-sourcemap-loader": "^0.3.8",
        "karma-webpack": "^4.0.2",
        "load-grunt-tasks": "^3.5.2",
        minimist: "^1.2.0",
        mocha: "^8.2.1",
        sinon: "^4.5.0",
        "terser-webpack-plugin": "^4.2.3",
        typescript: "^4.0.5",
        "url-search-params": "^0.10.0",
        webpack: "^4.44.2",
        "webpack-dev-server": "^3.11.0"
    }, Rr = {"./lib/adapters/http.js": "./lib/adapters/xhr.js"}, Ar = "dist/axios.min.js", jr = "dist/axios.min.js",
    Nr = "./index.d.ts", Pr = {"follow-redirects": "^1.14.0"}, kr = [{path: "./dist/axios.min.js", threshold: "5kB"}];
var $r = {
    name: pr,
    version: mr,
    description: vr,
    main: yr,
    scripts: br,
    repository: gr,
    keywords: wr,
    author: Er,
    license: xr,
    bugs: Sr,
    homepage: Cr,
    devDependencies: Or,
    browser: Rr,
    jsdelivr: Ar,
    unpkg: jr,
    typings: Nr,
    dependencies: Pr,
    bundlesize: kr
}, se = $r, _ = {};
["object", "boolean", "number", "function", "string", "symbol"].forEach(function (r, e) {
    _[r] = function (n) {
        return typeof n === r || "a" + (e < 1 ? "n " : " ") + r
    }
});
var ie = {}, Tr = se.version.split(".");

function oe(r, e) {
    for (var t = e ? e.split(".") : Tr, n = r.split("."), a = 0; a < 3; a++) {
        if (t[a] > n[a]) return !0;
        if (t[a] < n[a]) return !1
    }
    return !1
}

_.transitional = function (e, t, n) {
    var a = t && oe(t);

    function i(o, u) {
        return "[Axios v" + se.version + "] Transitional option '" + o + "'" + u + (n ? ". " + n : "")
    }

    return function (o, u, s) {
        if (e === !1) throw new Error(i(u, " has been removed in " + t));
        return a && !ie[u] && (ie[u] = !0, console.warn(i(u, " has been deprecated since v" + t + " and will be removed in the near future"))), e ? e(o, u, s) : !0
    }
};

function Ur(r, e, t) {
    if (typeof r != "object") throw new TypeError("options must be an object");
    for (var n = Object.keys(r), a = n.length; a-- > 0;) {
        var i = n[a], o = e[i];
        if (o) {
            var u = r[i], s = u === void 0 || o(u, i, r);
            if (s !== !0) throw new TypeError("option " + i + " must be " + s);
            continue
        }
        if (t !== !0) throw Error("Unknown option " + i)
    }
}

var Br = {isOlderVersion: oe, assertOptions: Ur, validators: _}, ue = p, Dr = X, ce = Le, le = hr, P = ae, fe = Br,
    x = fe.validators;

function C(r) {
    this.defaults = r, this.interceptors = {request: new ce, response: new ce}
}

C.prototype.request = function (e) {
    typeof e == "string" ? (e = arguments[1] || {}, e.url = arguments[0]) : e = e || {}, e = P(this.defaults, e), e.method ? e.method = e.method.toLowerCase() : this.defaults.method ? e.method = this.defaults.method.toLowerCase() : e.method = "get";
    var t = e.transitional;
    t !== void 0 && fe.assertOptions(t, {
        silentJSONParsing: x.transitional(x.boolean, "1.0.0"),
        forcedJSONParsing: x.transitional(x.boolean, "1.0.0"),
        clarifyTimeoutError: x.transitional(x.boolean, "1.0.0")
    }, !1);
    var n = [], a = !0;
    this.interceptors.request.forEach(function (l) {
        typeof l.runWhen == "function" && l.runWhen(e) === !1 || (a = a && l.synchronous, n.unshift(l.fulfilled, l.rejected))
    });
    var i = [];
    this.interceptors.response.forEach(function (l) {
        i.push(l.fulfilled, l.rejected)
    });
    var o;
    if (!a) {
        var u = [le, void 0];
        for (Array.prototype.unshift.apply(u, n), u = u.concat(i), o = Promise.resolve(e); u.length;) o = o.then(u.shift(), u.shift());
        return o
    }
    for (var s = e; n.length;) {
        var v = n.shift(), w = n.shift();
        try {
            s = v(s)
        } catch (f) {
            w(f);
            break
        }
    }
    try {
        o = le(s)
    } catch (f) {
        return Promise.reject(f)
    }
    for (; i.length;) o = o.then(i.shift(), i.shift());
    return o
};
C.prototype.getUri = function (e) {
    return e = P(this.defaults, e), Dr(e.url, e.params, e.paramsSerializer).replace(/^\?/, "")
};
ue.forEach(["delete", "get", "head", "options"], function (e) {
    C.prototype[e] = function (t, n) {
        return this.request(P(n || {}, {method: e, url: t, data: (n || {}).data}))
    }
});
ue.forEach(["post", "put", "patch"], function (e) {
    C.prototype[e] = function (t, n, a) {
        return this.request(P(a || {}, {method: e, url: t, data: n}))
    }
});
var Lr = C;

function J(r) {
    this.message = r
}

J.prototype.toString = function () {
    return "Cancel" + (this.message ? ": " + this.message : "")
};
J.prototype.__CANCEL__ = !0;
var de = J, qr = de;

function k(r) {
    if (typeof r != "function") throw new TypeError("executor must be a function.");
    var e;
    this.promise = new Promise(function (a) {
        e = a
    });
    var t = this;
    r(function (a) {
        t.reason || (t.reason = new qr(a), e(t.reason))
    })
}

k.prototype.throwIfRequested = function () {
    if (this.reason) throw this.reason
};
k.source = function () {
    var e, t = new k(function (a) {
        e = a
    });
    return {token: t, cancel: e}
};
var Ir = k, Fr = function (e) {
    return function (n) {
        return e.apply(null, n)
    }
}, Hr = function (e) {
    return typeof e == "object" && e.isAxiosError === !0
}, he = p, Mr = z, $ = Lr, _r = ae, Jr = F;

function pe(r) {
    var e = new $(r), t = Mr($.prototype.request, e);
    return he.extend(t, $.prototype, e), he.extend(t, e), t
}

var m = pe(Jr);
m.Axios = $;
m.create = function (e) {
    return pe(_r(m.defaults, e))
};
m.Cancel = de;
m.CancelToken = Ir;
m.isCancel = te;
m.all = function (e) {
    return Promise.all(e)
};
m.spread = Fr;
m.isAxiosError = Hr;
T.exports = m;
T.exports.default = m;
var me = T.exports;
me.defaults.withCredentials = !0;
const ve = me.create({timeout: 5e3, baseURL: "./"}), ye = be();
ve.interceptors.request.use(r => (localStorage.getItem("token") && (r.headers.token = localStorage.getItem("token")), r), r => (console.log(r), Promise.reject()));
ve.interceptors.response.use(r => {
    if (r.status === 200) return r.data.code === 200 ? r.data : (ge.info(r.data.message), Promise.reject());
    r.status === 401 && (localStorage.removeItem("ms_username"), localStorage.removeItem("token"), localStorage.removeItem("userName"), ye.push("/login")), Promise.reject()
}, r => (r.response && r.response.status === 401 ? (localStorage.removeItem("ms_username"), localStorage.removeItem("token"), localStorage.removeItem("userName"), ye.push("/login")) : console.log(r), Promise.reject()));
export {ve as s};
