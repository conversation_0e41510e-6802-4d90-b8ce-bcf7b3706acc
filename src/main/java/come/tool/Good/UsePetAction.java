package come.tool.Good;

import cn.hutool.core.util.ArrayUtil;
import come.tool.FightingData.*;
import come.tool.Mixdeal.AnalysisString;
import io.netty.channel.ChannelHandlerContext;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang.StringUtils;
import org.come.action.IAction;
import org.come.action.monitor.MonitorUtil;
import org.come.action.reward.DrawnitemsAction;
import org.come.action.suit.SuitMixdeal;
import org.come.action.summoning.SummonPetAction;
import org.come.bean.LoginResult;
import org.come.entity.Goodstable;
import org.come.entity.RoleSummoning;
import org.come.entity.XyConditionDto;
import org.come.handler.SendMessage;
import org.come.model.ColorScheme;
import org.come.model.Configure;
import org.come.model.Skill;
import org.come.protocol.Agreement;
import org.come.server.GameServer;
import org.come.tool.Arith;
import org.come.until.AllServiceUtil;
import org.come.until.GsonUtil;

import come.tool.Battle.BattleMixDeal;
import come.tool.Stall.AssetUpdate;

import static come.tool.Good.UseRoleAction.random;

public class UsePetAction implements IAction {
    public static String[] xy;
    static Map<Integer, Integer> magicSkinMap;
    static int[] highSkill = new int[]{1600, 1601, 1602, 1603, 1604, 1605, 1611, 1612, 1811,
            1815, 1816, 1817, 1818, 1819, 1815, 1816, 1817, 1818, 1819,
            1820, 1821, 1822, 1823, 1824, 1825, 1826, 1827, 1831, 1833,
            1834, 1835, 1836, 1837, 1838, 1839, 1848, 1850, 1852, 1854, 1858, 1859, 1860, 1862, 1864, 1865,
            1871, 1872, 1873, 1874, 1875, 1876, 1877, 1878, 1879, 1880, 1882, 1883, 1884, 1885, 1886, 1887};
    //筛子随机highSkillSSJN=高级，normalSkillSJJN=低级
    static int[] highSkillSSJN = new int[]{1600, 1601, 1602, 1603, 1604, 1605, 1611, 1612, 1811,
            1815, 1816, 1817, 1818, 1819, 1815, 1816, 1817, 1818, 1819,
            1820, 1821, 1822, 1823, 1824, 1825, 1826, 1827, 1831, 1833,
            1834, 1835, 1836, 1837, 1838, 1839, 1848, 1850, 1852, 1854, 1858, 1859, 1860, 1862, 1864, 1865,
            1871, 1872, 1873, 1874, 1875, 1876, 1877, 1878, 1879, 1880, 1882, 1883, 1884, 1885, 1886, 1887};
    static int[] normalSkillSJJN = new int[]{1800, 1801, 1802, 1803, 1804, 1805, 1806, 1807, 1808, 1810, 1811, 1812, 1832, 1843, 1844, 1845, 1846, 1847, 1849, 1851, 1853, 1855, 1856, 1857, 1861, 1863};
    //筛子随机highSkillSSJN=高级，normalSkillSJJN=低级
    static int[] normalSkill = new int[]{1800, 1801, 1802, 1803, 1804, 1805, 1806, 1807, 1808, 1810, 1811, 1812, 1832, 1843, 1844, 1845, 1846, 1847, 1849, 1851, 1853, 1855, 1856, 1857, 1861, 1863};
    private final Integer[] zhongjiskill = {1606, 1607, 1608, 1828, 1829, 1840, 1841, 1842, 3034, 1830, 1881};
    //颜色表
    //private static ConcurrentHashMap<Long, NPCDialogBean> maps;
    private static ConcurrentHashMap<Long, NPCDialogBean> maps = new ConcurrentHashMap<>();

    static {
        magicSkinMap = new HashMap<>();
        magicSkinMap.put(Integer.valueOf(400120), Integer.valueOf(500222));
        magicSkinMap.put(Integer.valueOf(400523), Integer.valueOf(500223));
        magicSkinMap.put(Integer.valueOf(400111), Integer.valueOf(500218));
        magicSkinMap.put(Integer.valueOf(400525), Integer.valueOf(500219));
        magicSkinMap.put(Integer.valueOf(400107), Integer.valueOf(500220));
        magicSkinMap.put(Integer.valueOf(400519), Integer.valueOf(500221));
        magicSkinMap.put(Integer.valueOf(400109), Integer.valueOf(500214));
        magicSkinMap.put(Integer.valueOf(400522), Integer.valueOf(500215));
        magicSkinMap.put(Integer.valueOf(400108), Integer.valueOf(500212));
        magicSkinMap.put(Integer.valueOf(400520), Integer.valueOf(500213));
        magicSkinMap.put(Integer.valueOf(400110), Integer.valueOf(500216));
        magicSkinMap.put(Integer.valueOf(400524), Integer.valueOf(500217));
    }

    static {
        maps = new ConcurrentHashMap<>();
    }

    @Override
    public void action(ChannelHandlerContext ctx, String message) {
        // TODO Auto-generated method stub
        LoginResult loginResult = GameServer.getAllLoginRole().get(ctx);
        if (loginResult == null) {
            return;
        }
        String[] vs = message.split("\\|");
        if (vs[0].equals("N")) {
            XXPet(ctx, loginResult, vs);
            return;
        }
        RoleSummoning pet = AllServiceUtil.getRoleSummoningService().selectRoleSummoningsByRgID(new BigDecimal(vs[1]));
        if (pet == null) {
            return;
        }
        if (pet.getRoleid().compareTo(loginResult.getRole_id()) != 0) {
            return;
        }
        switch (vs[0]) {
            case "DH":
                DHPet(pet, ctx, loginResult);
                return;
            case "FS":
                FSPet(pet, ctx, loginResult);
                return;
            case "HF":
                HFPet(pet, ctx, loginResult);
                return;
            case "SS":
                SSPet(pet, ctx, loginResult);
                return;
            case "PS": {
                int type = Integer.parseInt(vs[2]);
                if (type == 0 || type == 1 || type == 2) {
                    return;
                }
                PetSkill(pet, ctx, loginResult, type);
                return;
            }
            case "SJJN": {
//            int type = Integer.parseInt(vs[2]);
//            if (type == 0 || type == 1 || type == 2) {
//                return;
//            }
                int type = random.nextInt(2) + 1;
                PetsjSkill(pet, ctx, loginResult, type);
                return;
            }
            case "CX": {
                int i = Integer.parseInt(vs[2]);
                if (i == 0 || i == 1 || i == 2)
                    return;
                Practiced(pet, ctx, loginResult, i);
                return;
            }
            case "XY":
                SXPet(pet, ctx, loginResult, vs);
                return;
            case "XXY":
                XyClearPoint(pet, ctx, loginResult, vs);
                return;
            case "DXY":
                XyAddPoint(pet, ctx, loginResult, vs);
                return;
            case "ND":
                Goodstable good = AllServiceUtil.getGoodsTableService().getGoodsByRgID(new BigDecimal(vs[2]));
                if (good == null) {
                    return;
                }
                if (good.getRole_id().compareTo(loginResult.getRole_id()) != 0) {
                    return;
                }
                if (good.getUsetime() <= 0) {
                    return;
                }
                NDPet(pet, good, ctx, loginResult);
                return;
        }
        Goodstable good = AllServiceUtil.getGoodsTableService().getGoodsByRgID(new BigDecimal(vs[0]));
        if (good == null) {
            return;
        }
        if (good.getRole_id().compareTo(loginResult.getRole_id()) != 0) {
            return;
        }
        if (good.getUsetime() <= 0) {
            return;
        }
        long type = good.getType();
        if (type == 715) {//715    亲密丹
            useGood(good, 1);
            long addQM = Long.parseLong(good.getValue().split("\\=")[1]);
            long value = pet.getFriendliness() + addQM;
            if (value >= 50000000) {
                value = 50000000;
            }
            pet.setFriendliness(value);
            AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
            AssetUpdate assetUpdate = new AssetUpdate();
            assetUpdate.setType(AssetUpdate.USEGOOD);
            assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
            assetUpdate.updata("P" + pet.getSid() + "=" + pet.getGrade() + "=" + pet.getExp() + "=" + pet.getFriendliness() + "=" + pet.getBasishp() + "=" + pet.getBasismp());
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
        } else if (type == 503) {//503    召唤兽技能书 openSkillSealSS
            addPetSkill(pet, good, ctx, loginResult);
        } else if (type == 939) {//503    召唤兽技能书 openSkillSealSS
            addpetqiling(pet, good, ctx, loginResult);
        } else if (type == 504) {//504    聚魄丹
            openSkillSeal(pet, good, ctx, loginResult);
        } else if (type == 5055) {//504    聚魄丹
            openqhSeal(pet, good, ctx, loginResult);
        } else if (type == 938) {//504    启灵丹
            oepnqldan(pet, good, ctx, loginResult);
        } else if (type == 10086) {//10086    高级聚魄丹
            openSkillSealSS(pet, good, ctx, loginResult);
        } else if (type == 2040) {//2040   召唤兽经验丹
            useGood(good, 1);
            long addexp = Long.parseLong(good.getValue().split("\\=")[1]);
            ExpUtil.PetExp(pet, addexp);
            AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
            AssetUpdate assetUpdate = new AssetUpdate();
            assetUpdate.setType(AssetUpdate.USEGOOD);
            assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
            assetUpdate.updata("P" + pet.getSid() + "=" + pet.getGrade() + "=" + pet.getExp() + "=" + pet.getFriendliness() + "=" + pet.getBasishp() + "=" + pet.getBasismp());
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
        } else if (type == 2043) {//2043   九转
            useNgauWanPills(pet, good, ctx, loginResult);
        } else if (type == 2113) {//2113   龙之骨
            useKeel(pet, good, ctx, loginResult);
        } else if (type == 918) {//  918   超级龙之骨
            useKeelsp(pet, good, ctx, loginResult);
        } else if (type == 716) {//716    超级元气丹
            GrowUpDan(pet, good, ctx, loginResult);
        } else if (type == 192) {//192    龙涎丸
            dragonSaliva(pet, good, ctx, loginResult);
        } else if (type == 667) {//667    伐骨洗髓丹
            useBoneElution(pet, good, ctx, loginResult);
        } else if (type == 668) {//668    伐魄洗髓丹
            useSoulElution(pet, good, ctx, loginResult);
        } else if (type == 919) {//667    超级伐骨洗髓丹
            useBoneElutionsp(pet, good, ctx, loginResult);
        } else if (type == 2323) {//2323   终极培养丹
            train(pet, good, ctx, loginResult);
        } else if (type == 2325) {//2325   超级聚魄丹
            useDraw(pet, good, ctx, loginResult);
        } else if (type == 2326) {//2326   使用聚魄丹的技能
            addPetSkill(pet, good, ctx, loginResult);
        } else if (type == 726) {//726     幻肤
            changeMagicSkin(pet, good, ctx);
        } else if (type == 727) {//727     化形丹
            changeDan(pet, good, ctx, loginResult);
        } else if (type == 2116) {// 神兽飞升丹
            petFlyUpDan(pet, good, ctx, loginResult);
        } else if (type == 8002) {//8002   灵藤
            lingteng(pet, good, ctx, loginResult);
        } else if (type == 1005) {
            useGood(good, 1);
            UseMixdeal.gld(good, pet, ctx, loginResult);
        } else if (type == 2234) {

            usexuepo(pet, good, ctx, loginResult);

        }

    }

    /**
     * 灵藤
     */
    public void lingteng(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        //获取value 物品信息
        String value = good.getValue();
        int ltlvl = -1;
        int ltlvl1 = -1;
        int ltlvl2 = -1;
        //获取物品等级
        if (good.getGoodsname().contains("低")) {
            ltlvl = 3;
        } else if (good.getGoodsname().contains("中")) {
            ltlvl1 = 6;
        } else if (good.getGoodsname().contains("高")) {
            ltlvl2 = 9;
        } else {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("获取物品错误"));
            return;
        }

        String[] value1 = value.split("=");
        String value2 = "0";
        String level1 = "0";
        if (value1[1].equals("高级分裂攻击")) {
            value2 = "1833";
        } else if (value1[1].equals("春风佛面")) {
            value2 = "1871";
        } else if (value1[1].equals("春意盎然")) {
            value2 = "1612";
        } else if (value1[1].equals("分花拂柳")) {
            value2 = "1831";
        } else if (value1[1].equals("悬刃")) {
            value2 = "1834";
        } else if (value1[1].equals("遗患")) {
            value2 = "1836";
        } else if (value1[1].equals("报复")) {
            value2 = "1835";
        } else if (value1[1].equals("吉人天相")) {
            value2 = "1838";
        } else if (value1[1].equals("妙手回春")) {
            value2 = "1611";
        } else if (value1[1].equals("视死如归")) {
            value2 = "1872";
        } else if (value1[1].equals("天地同寿")) {
            value2 = "1880";
        } else if (value1[1].equals("扶伤")) {
            value2 = "1858";
        } else if (value1[1].equals("福禄双全")) {
            value2 = "1873";
        } else if (value1[1].equals("炊金馔玉")) {
            value2 = "1600";
        } else if (value1[1].equals("枯木逢春")) {
            value2 = "1601";
        } else if (value1[1].equals("西天净土")) {
            value2 = "1602";
        } else if (value1[1].equals("如人饮水")) {
            value2 = "1603";
        } else if (value1[1].equals("风火燎原")) {
            value2 = "1604";
        } else if (value1[1].equals("高级清明术")) {
            value2 = "1850";
        } else if (value1[1].equals("高级脱困术")) {
            value2 = "1852";
        } else if (value1[1].equals("高级强心术")) {
            value2 = "1854";
        } else if (value1[1].equals("舍身取义")) {
            value2 = "1839";
        } else if (value1[1].equals(TypeUtil.BB_WYJK)) {
            value2 = "1876";
        } else if (value1[1].equals(TypeUtil.BB_TLFY)) {
            value2 = "1887";
        } else {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("获取技能错误"));
            return;
        }

        //if (pet.getPetSkillswl().indexOf(value1[0]) == -1) {
        //	SendMessage.sendMessageToSlef(ctx,Agreement.getAgreement().PromptAgreement("该召唤兽没有"+value1[0]));
        //		return;
        //	}else
        if (pet.getPetSkills() == null || pet.getPetSkills().equals("")) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽没有技能"));
            return;
        }
        if (!pet.getPetSkills().contains(value2)) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("该召唤兽没有" + value2));
            return;
        }

        List<String> skillswl = new ArrayList<>();
        //	if (pet.getPetSkillswl()==null|| pet.getPetSkillswl().equals("")) {
        //	skillswl.add(value);
        //}else

        if (pet.getPetSkillswl() != null && !pet.getPetSkillswl().equals("")) {
            String[] vs = pet.getPetSkillswl().split("\\|");
            for (int i = 0; i < vs.length; i++) {
                if (!vs[i].equals("") && !vs[i].contains(value2)) {
                    skillswl.add(vs[i]);
                } else if (vs[i].contains(value2)) {
                    String[] level = vs[i].split("=");
                    level1 = level[1];
                }
            }
        }
        if (skillswl.size() > 3) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("最多悟灵3个技能"));
            return;
        }

        if (Integer.parseInt(level1) >= 10) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("高级技能已满阶"));
            return;
        }


        //判断技能等级并提升1级
        if (Integer.parseInt(level1) <= ltlvl) {
            if (GameServer.random.nextInt(100) < 20) {
                int level2 = Integer.parseInt(level1) + 1;
                skillswl.add(value2 + "=" + String.valueOf(level2));
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement(value1[1] + "的技能等级提升到了" + level2 + "阶"));
            } else {
                skillswl.add(value2 + "=" + String.valueOf(level1));
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("很遗憾，升级失败"));
            }
        } else if (Integer.parseInt(level1) <= ltlvl1 && Integer.parseInt(level1) > 3) {
            if (GameServer.random.nextInt(100) < 15) {
                int level2 = Integer.parseInt(level1) + 1;
                skillswl.add(value2 + "=" + String.valueOf(level2));
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement(value1[1] + "的技能等级提升到了" + level2 + "阶"));
            } else {
                skillswl.add(value2 + "=" + String.valueOf(level1));
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("很遗憾，升级失败"));
            }
        } else if (Integer.parseInt(level1) <= ltlvl2 && Integer.parseInt(level1) > 6) {
            if (GameServer.random.nextInt(100) < 10) {
                int level2 = Integer.parseInt(level1) + 1;
                skillswl.add(value2 + "=" + String.valueOf(level2));
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement(value1[1] + "的技能等级提升到了" + level2 + "阶"));
            } else {
                skillswl.add(value2 + "=" + String.valueOf(level1));
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("很遗憾，升级失败"));
            }
        } else {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("请使用等级对应的灵藤"));
            return;
        }


        //拼接悟灵技能
        StringBuilder buffer1 = new StringBuilder();
        for (int i = 0; i < skillswl.size(); i++) {
            if (buffer1.length() != 0) {
                buffer1.append("|");
            }
            buffer1.append(skillswl.get(i));
        }
        //保存悟灵技能
        pet.setPetSkillswl(buffer1.toString());
        //扣除物品
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);

        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        assetUpdate.setPet(pet);

        //喊话 SuitMixdeal.PYJN(login.getRolename(),pet.getSummoningname(),skill.getSkillname());
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);

        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 内丹经验转换
     */
    public void NDPet(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        if (pet.getExp().longValue() <= 0) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("你召唤兽没有经验可以转换"));
            return;
        }
        String[] vs = good.getValue().split("\\|");
        String[] stringLevel = vs[2].split("=")[1].split("转");
        int zs = Integer.parseInt(stringLevel[0]);
        long addExp = (long) (pet.getExp().longValue() * 0.2);
        int lvl = Integer.parseInt(stringLevel[1]);
        long exp = Long.parseLong(vs[3].split("=")[1]) + addExp;
        int petlvl = BattleMixDeal.petLvlint(pet.getGrade());
        int maxlvl = ExpUtil.getNedanMostLevel(zs);
        if (zs >= pet.getTurnRount() && lvl >= petlvl) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽等级不够哦，快去修炼吧！！！"));
            return;
        }
        if (lvl >= 300) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("当前内丹已达最大等级！！！"));
            return;
        }
        long maxexp = ExpUtil.getBBNeiExp(zs, lvl + 1);
        xx:
        while (exp >= maxexp && exp > 0) {//判断是否最高级最高转
            if (lvl + 1 > maxlvl) {
                if (zs >= 4) {
                    break xx;
                } else if (zs + 1 > pet.getTurnRount()) {
                    break xx;
                } else {
                    zs++;
                    lvl = 0;
                    maxexp = ExpUtil.getBBNeiExp(zs, lvl + 1);
                    exp = 0;
                }
            } else if (zs >= pet.getTurnRount() && lvl + 1 > petlvl) {
                break xx;
            } else {
                exp = exp - maxexp;
                lvl++;
                maxexp = ExpUtil.getBBNeiExp(zs, lvl + 1);
            }
        }

        pet.setExp(new BigDecimal(0));//把召唤兽的经验清空为0；
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        StringBuffer buffer = new StringBuffer();
        buffer.append(vs[0]);
        buffer.append("|");
        buffer.append(vs[1]);
        buffer.append("|内丹等级=");
        buffer.append(zs);
        buffer.append("转");
        buffer.append(lvl);
        buffer.append("|经验=");
        buffer.append(exp);
        good.setValue(buffer.toString());

        AllServiceUtil.getGoodsTableService().updateGoodRedis(good);
        AssetUpdate assetUpdate = new AssetUpdate(AssetUpdate.USEGOOD);
        assetUpdate.setPet(pet);
        assetUpdate.updata("G" + good.getRgid() + "=" + zs + "=" + lvl + "=" + exp);
        assetUpdate.updata("P" + pet.getSid() + "=" + pet.getGrade() + "=" + pet.getExp() + "=" + pet.getFriendliness() + "=" + pet.getBasishp() + "=" + pet.getBasismp());
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 召唤兽技能替换
     */
    public static boolean PetSkill(RoleSummoning pet, ChannelHandlerContext ctx, LoginResult login, int type) {
//		0 1 2领取技能 其他的是删除指定技能
        if (type == 0 || type == 1 || type == 2) {
            boolean getNormal, getHigh, getTrain;
            if (type == 1) {
                getNormal = DropUtil.isV(100);
                getHigh = DropUtil.isV(1);
                getTrain = DropUtil.isV(0.05);
            } else if (type == 2) {
                getNormal = DropUtil.isV(100);
                getHigh = DropUtil.isV(5);
                getTrain = DropUtil.isV(0.01);
            } else {
                getNormal = DropUtil.isV(1.0);
                getHigh = DropUtil.isV(0.2);
                getTrain = DropUtil.isV(0.01);
            }
            if (!getNormal && !getHigh && !getTrain) {
                return false;
            }
            // 终极技能
            int skillId = 0;
            String grade = null;
            if (getTrain) {// 随机获得培养终级技能
                skillId = 3034;
                grade = "终极";
            } else if (getHigh) {// 高级技能
                int skillIndex = GameServer.random.nextInt(highSkill.length);
                skillId = highSkill[skillIndex];
                grade = "高级";
            } else if (getNormal) {// 普通技能
                int skillIndex = GameServer.random.nextInt(normalSkill.length);
                skillId = normalSkill[skillIndex];
                grade = "普通";
            }
            if (skillId == 0) {
                return false;
            }

            Skill skill = GameServer.getSkill(skillId + "");
            if (skill == null) {
                return false;
            }
            List<String> skills = new ArrayList<>();
            if (pet.getPetSkills() != null && !pet.getPetSkills().equals("")) {
                String[] vs = pet.getPetSkills().split("\\|");
                for (int i = 0; i < vs.length; i++) {
                    if (!vs[i].equals("")) {
                        skills.add(vs[i]);
                    }
                }
            }
//			if (pet.getPetQlSkills() != null && !pet.getPetQlSkills().equals("")) {
//				String[] vss = pet.getPetQlSkills().split("\\|");
//				for (int i = 0; i < vss.length; i++) {
//					if (!vss[i].equals("")) {
//						skills.add(vss[i]);
//					}
//				}
//			}
            if (pet.getOpenSeal() <= skills.size() || skills.size() >= 8) {//召唤兽技能7-8格自动领悟
                return false;
            }
//			if (pet.getOpenql() <= skills.size() || skills.size() >= 6) {//召唤兽技能7-8格自动领悟
//				return false;
//			}
            String s = chongfu(skill, pet, skills, false);
            if (s != null) {
                if (Objects.equals(s, "不能拥有同类型的技能")) {
                    SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("不能拥有同类型的技能"));
                }
                return false;
            }
            skills.add(skill.getSkillid() + "");
            StringBuilder buffer = new StringBuilder();
            for (int i = 0; i < skills.size(); i++) {
                if (buffer.length() != 0) {
                    buffer.append("|");
                }
                buffer.append(skills.get(i));
            }
            pet.setPetSkills(buffer.toString());
//			pet.setPetQlSkills(buffer.toString());
            getskills(skills, pet.getSkill());
            getskills(skills, pet.getBeastSkills());
            pet.setSkillData(skillData(skills));
            AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
            SuitMixdeal.JN2(login.getRolename(), pet.getSummoningname(), skill.getSkillname(), grade);
            AssetUpdate assetUpdate = new AssetUpdate();
            assetUpdate.setType(AssetUpdate.USEGOOD);
            assetUpdate.setPet(pet);
            assetUpdate.setMsg("你的召唤兽学会了" + skill.getSkillname());
            if (!grade.equals("普通")) {
                assetUpdate.updata("T悟技");
            }
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
        } else {
            if (login.getGold().compareTo(new BigDecimal(300000)) < 0) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("金币不足!!!"));
            } else {
                boolean is = true;
                boolean iss = true;
                String[] vs = null;
                String[] vs1 = null;

                List<String> skills = new ArrayList<>();
                List<String> skills1 = new ArrayList<>();
                if (type == 1509 || type == 1609 || type == 1814 || type == 1866) { //如果是神兽技能就不领悟
                    is = false;
                    pet.setBeastSkills(null);
                } else {
                    StringBuilder buffer = new StringBuilder();
                    StringBuilder buffer1 = new StringBuilder();
                    if (pet.getPetSkills() != null && !pet.getPetSkills().equals("")) {
                        String types = type + "";
                        vs = pet.getPetSkills().split("\\|");
                        for (int i = 0; i < vs.length; i++) {
                            if (vs[i].equals("")) {
                            } else if (vs[i].equals(types)) {
                                is = false;
                                if (pet.getPetSkillswl() != null && !pet.getPetSkillswl().equals("")) {
                                    vs1 = pet.getPetSkillswl().split("\\|");
                                    for (int i1 = 0; i1 < vs1.length; i1++) {
                                        if (vs1[i1].contains(types)) {

                                        } else {
                                            if (buffer1.length() != 0) {
                                                buffer1.append("|");
                                            }
                                            buffer1.append(vs1[i1]);
                                            skills1.add(vs1[i1]);
                                        }
                                    }
                                }
                            } else {
                                if (buffer.length() != 0) {
                                    buffer.append("|");
                                }
                                buffer.append(vs[i]);
                                skills.add(vs[i]);
                            }
                        }
                    }
                    pet.setPetSkills(buffer.length() == 0 ? null : buffer.toString());
                    pet.setPetSkillswl(buffer1.length() == 0 ? null : buffer1.toString());
                }
                if (type == 1509 || type == 1609 || type == 1814 || type == 1866) { //如果是神兽技能就不领悟
                    is = false;
                    pet.setBeastSkills(null);
                } else {
                    StringBuilder buffer2 = new StringBuilder();
                    if (pet.getPetQlSkills() != null && !pet.getPetQlSkills().equals("")) {
                        String types = type + "";
                        vs = pet.getPetQlSkills().split("\\|");
                        for (int i = 0; i < vs.length; i++) {
                            if (vs[i].equals("")) {
                            } else if (vs[i].equals(types)) {
                                is = false;
                            } else {
                                if (buffer2.length() != 0) {
                                    buffer2.append("|");
                                }
                                buffer2.append(vs[i]);
                                skills.add(vs[i]);
                            }
                        }
                    }
                    pet.setPetQlSkills(buffer2.length() == 0 ? null : buffer2.toString());
                }
                if (is) {
                    SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽未携带技能"));
                    return false;
                }
//			if (iss) {
//				SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽未携带技能"));
//				return false;
//			}
                getskills(skills, pet.getSkill());
                getskills(skills, pet.getBeastSkills());
                pet.setSkillData(skillData(skills));
                AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
                AssetUpdate assetUpdate = new AssetUpdate();
                assetUpdate.setType(AssetUpdate.USEGOOD);
                assetUpdate.setPet(pet);

                assetUpdate.updata("D=-300000");
                login.setGold(login.getGold().subtract(new BigDecimal(300000)));
                MonitorUtil.getMoney().useD(300000L);

                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
            }
        }
        return true;
    }

    /**
     * 召唤兽技能替换
     */
    public static boolean PetsjSkill(RoleSummoning pet, ChannelHandlerContext ctx, LoginResult login, int type) {
        if (login.getTurnAround() < 3) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("你没有3转啊#35"));
            return false;
        }
        long currentTime = System.currentTimeMillis();
        long cooldownInterval = TimeUnit.HOURS.toMillis(2);
        if (currentTime < login.getLastSkillTime() + cooldownInterval) {
            long remainingTime = (login.getLastSkillTime() + cooldownInterval - currentTime) / 1000;
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement(
                    "技能冷却中，剩余时间：" + remainingTime / 60 + " 分钟"));
            return false;
        }

//		0 1 2领取技能 其他的是删除指定技能
        if (type == 0 || type == 1 || type == 2) {
            boolean getNormal, getHigh, getTrain;
            if (type == 1) {
                getNormal = DropUtil.isV(100);
                getHigh = DropUtil.isV(1);
            } else if (type == 2) {
                getNormal = DropUtil.isV(100);
                getHigh = DropUtil.isV(5);
            } else {
                getNormal = DropUtil.isV(50);
                getHigh = DropUtil.isV(50);
            }
            if (!getNormal && !getHigh) {
                return false;
            }
            // 终极技能
            int skillId = 0;
            String grade = null;
            if (login.getSkillAttemptCount() >= 50) {
                getHigh = true;
                login.resetSkillAttemptCount();
            }
            if (getHigh) {// 高级技能
                int skillIndex = GameServer.random.nextInt(highSkill.length);
                skillId = highSkillSSJN[skillIndex];
                grade = "高级";
            } else if (getNormal) {// 普通技能
                int skillIndex = GameServer.random.nextInt(normalSkill.length);
                skillId = normalSkillSJJN[skillIndex];
                grade = "普通";
            }
            if (skillId == 0) {
                return false;
            }

            Skill skill = GameServer.getSkill(skillId + "");
            if (skill == null) {
                return false;
            }
            List<String> skills = new ArrayList<>();
            if (pet.getPetSkills() != null && !pet.getPetSkills().equals("")) {
                String[] vs = pet.getPetSkills().split("\\|");
                for (int i = 0; i < vs.length; i++) {
                    if (!vs[i].equals("")) {
                        skills.add(vs[i]);
                    }
                }
            }

            if (pet.getOpenSeal() <= skills.size() || skills.size() >= 8) {//召唤兽技能7-8格自动领悟
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽技能格子已满"));
                return false;
            }

            String s = chongfu(skill, pet, skills, false);
            if (s != null) {
                if (Objects.equals(s, "不能拥有同类型的技能")) {
                    SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("不能拥有同类型的技能"));
                }
                return false;
            }
            skills.add(skill.getSkillid() + "");
            StringBuilder buffer = new StringBuilder();
            for (int i = 0; i < skills.size(); i++) {
                if (buffer.length() != 0) {
                    buffer.append("|");
                }
                buffer.append(skills.get(i));
            }
            pet.setPetSkills(buffer.toString());
            getskills(skills, pet.getSkill());
            getskills(skills, pet.getBeastSkills());
            pet.setSkillData(skillData(skills));
            AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
            SuitMixdeal.JN2(login.getRolename(), pet.getSummoningname(), skill.getSkillname(), grade);
            AssetUpdate assetUpdate = new AssetUpdate();
            assetUpdate.setType(AssetUpdate.USEGOOD);
            assetUpdate.setPet(pet);
            assetUpdate.setMsg("你的召唤兽学会了" + skill.getSkillname());
            if (!grade.equals("普通")) {
                assetUpdate.updata("T悟技");
            }

            login.setLastSkillTime(currentTime);
            login.incrementSkillAttemptCount();
            AssetUpdate assetUpdate1 = new AssetUpdate();
            assetUpdate1.updata("冷却=|" + login.getLastSkillTime() + "|" + login.getSkillAttemptCount());
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate1)));
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
        }
        return true;
    }


    public static void Practiced(RoleSummoning pet, ChannelHandlerContext ctx, LoginResult login, int type) {
        List<String> skills = new ArrayList<>();
        int i;
        if (pet.getPetSkills() != null && !pet.getPetSkills().equals("")) {
            String[] vs = pet.getPetSkills().split("\\|");

            for (i = 0; i < vs.length; ++i) {
                if (!vs[i].equals("") && !vs[i].equals("3034")) {
                    skills.add(vs[i]);
                }
            }
        }

        List<String> ids = new ArrayList<>();
        ids.add("1606");
        ids.add("1607");
        ids.add("1608");
        ids.add("1828");
        ids.add("1829");
        ids.add("1830");
        ids.add("1840");
        ids.add("1841");
        ids.add("1842");
        ids.add("1867");
        ids.add("1868");
        ids.add("1869");

        for (i = ids.size() - 1; i >= 0; --i) {
            if (skills.contains(ids.get(i))) {
                skills.remove(ids.get(i));
                skills.add("3034");
                break;
            }
        }

        StringBuilder buffer = new StringBuilder();

        for (int j = 0; j < skills.size(); ++j) {
            if (buffer.length() != 0) {
                buffer.append("|");
            }

            buffer.append((String) skills.get(j));
        }

        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        pet.setPetSkills(buffer.toString());
        getskills(skills, pet.getSkill());
        getskills(skills, pet.getBeastSkills());
        pet.setSkillData(skillData(skills));
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("您的神兽#G " + pet.getSummoningname() + "#Y技能重修成功!!!");
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

//    private static boolean isSkillOK(ChannelHandlerContext ctx, RoleSummoning pet, String skillId, int type) {
//        int sum = type == 0 ? getZJSum(pet, skillId) : getXHSum(pet, skillId);
//        if (sum > 0) {
//            List<Goodstable> goodsList = AllServiceUtil.getGoodsTableService().selectGoodsByRoleIDAndGoodsID(pet.getRoleid(), BigDecimal.valueOf(92429));
//            if (goodsList.size() == 0 || goodsList.get(0).getUsetime() < sum) {
//                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement((type == 0 ? "删除" : "重修") + "该技能需要" + sum + "个终极重修丹！！！"));
//                return false;
//            }
//            goodsList.get(0).setUsetime(goodsList.get(0).getUsetime() - sum);
//            AllServiceUtil.getGoodsTableService().updateGoodRedis(goodsList.get(0));
//            AssetUpdate assetUpdate = new AssetUpdate(AssetUpdate.USEGOOD);
//            assetUpdate.updata("G" + goodsList.get(0).getRgid() + "=" + goodsList.get(0).getUsetime());
//            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
//        }
//        return true;
//    }

    public static int getXHSum(RoleSummoning pet, String skillId) {
        int sum = 1;
        int zj = isZJ(pet, skillId);
        switch (zj) {
            case 2:
                sum = 1000;
                break;
            case 3:
                sum = 2000;
                break;
            case 4:
                sum = 3000;
                break;
            case 5:
                sum = 4000;
                break;
        }
        return sum;
    }

    public static int getZJSum(RoleSummoning pet, String skillId) {
        int sum = 0;
        int zj = isZJ(pet, skillId);
        switch (zj) {
            case 2:
                sum = 1000;
                break;
            case 3:
                sum = 2000;
                break;
            case 4:
                sum = 3000;
                break;
            case 5:
                sum = 4000;
                break;
        }
        return sum;
    }

    private static int isZJ(RoleSummoning pet, String skillId) {
        int num = 0;
        List<String> ids = Arrays.asList("1606", "1607", "1608", "1828", "1829", "1830", "1840", "1841", "1842", "1867", "1868", "1869");
        if (ids.contains(skillId) && StringUtils.isNotBlank(pet.getPetSkills())) {
            String[] skills = pet.getPetSkills().split("\\|");
            for (int j = 0; j < skills.length; j++) {
                if (ids.contains(skills[j])) {
                    num++;
                }
            }
        }
        return num;
    }

    private void SXPet(RoleSummoning pet, ChannelHandlerContext ctx, LoginResult loginResult, String[] vs) {
        if (!pet.getSummoningid().equals("210007")) {
            return;
        }
        //进阶
        if ("1".startsWith(vs[2])) {
            //第一次启程
            if (StringUtils.isBlank(pet.getXl())) {
                String[] split = xy[0].split("&");
                for (String s : split) {
                    XyConditionDto xyConditionDto = parseCondition(s, pet);
                    if (!xyConditionDto.getB()) {
                        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("#Y" + xyConditionDto.getText() + "#R  未完成!"));
                        return;
                    }
                }
                pet.setXl("0|1|1|12001=0_12002=0_12003=0_12004=0_12005=0_12006=0_12007=0_12008=0_12009=0_12010=0_12011=0_12012=0_12013=0_12014=0_12015=0_12016=0");
            } //新路
            else if (pet.getXl().startsWith("0")) {
                String[] xyData = pet.getXl().split("\\|");
                int i = Integer.parseInt(xyData[1]);
                if (StringUtils.isBlank(pet.getXl())) {
                    String[] split = xy[i].split("&");
                    for (String s : split) {
                        XyConditionDto xyConditionDto = parseCondition(s, pet);
                        if (!xyConditionDto.getB()) {
                            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("#Y" + xyConditionDto.getText() + "#R  未完成!"));
                            return;
                        }
                    }
                }
                xyData[1] = i + 1 + "";
                String join = ArrayUtil.join(xyData, "|");
                if (Integer.parseInt(xyData[1]) > 13) {
                    SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("当前召唤兽异常！"));
                    return;
                }
                pet.setXl(join);
            }
        }
        //一念成圣
        else if ("3".startsWith(vs[2])) {
            if (!pet.getSummoningskin().equals("10015")) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("小弟兄,当前召唤兽无法成圣#76"));
                return;
            }
            if (pet.getXl() == null) {
                pet.setXl("0|1|1|12001=0_12002=0_12003=0_12004=0_12005=0_12006=0_12007=0_12008=0_12009=0_12010=0_12011=0_12012=0_12013=0_12014=0_12015=0_12016=0");
            }
            if (pet.getXl().startsWith("0")) {
                String[] xyData = pet.getXl().split("\\|");
                int i = Integer.parseInt(xyData[1]);
                if (StringUtils.isNotBlank(pet.getXl())) {
                    String[] split = xy[12].split("&");
                    for (String s : split) {
                        XyConditionDto xyConditionDto = parseCondition(s, pet);
                        if (!xyConditionDto.getB()) {
                            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("#Y" + xyConditionDto.getText() + "#R  未完成!"));
                            return;
                        }
                    }
                }
                if (i == 12) {
                    xyData[0] = 1 + "";
                    xyData[1] = 11 + "";
                    xyData[2] = 11 + "";
                    xyData[3] = "12001=1_12002=1_12003=1_12004=1_12005=1_12006=1_12007=1_12008=1_12009=1_12010=1_12011=1_12012=1_12013=1_12014=1_12015=1_12016=1";
                    String join = ArrayUtil.join(xyData, "|");
                    pet.setXl(join);
                }
                if (pet == null) {
                    return;
                }
                RoleSummoning roleSummoning = GameServer.getAllPet().get(new BigDecimal("210008"));
                pet.setBasishp(pet.getHp());
                pet.setBasismp(pet.getMp());
                // 设置忠诚
                pet.setSummoningname(roleSummoning.getSummoningname());
                pet.setFaithful(100);
                pet.setGrade(0);
                pet.setTurnRount(0);
                pet.setBone(0);
                pet.setSpir(0);
                pet.setPower(0);
                pet.setSpeed(0);
                pet.setCalm(0);
                pet.setDragon(0);
                pet.setSpdragon(0);
                pet.setAlchemynum(0);
                pet.setExp(new BigDecimal(0));
                pet.setOpenSeal(1);
                pet.setSummoningskin("10016");
                pet.setSkill("12017");
                pet.setPetSkills("");
                pet.setSummoningid("210008");
//                if (pet.getSsn() != null && pet.getSsn().equals("0")) {f
                pet.setHp(roleSummoning.getHp());
                pet.setMp(roleSummoning.getMp());
                pet.setAp(roleSummoning.getAp());
                pet.setSp(roleSummoning.getSp());
                pet.setGrowlevel(roleSummoning.getGrowlevel());
                pet.setFourattributes("");
//                pet.setCzjjd(0);
                pet.setLongjing(0);
                pet.setOpenqh(0);
                pet.setOpenSSskill(0);
                pet.setDragonSpirit(0);
                pet.setDragonSoul(0);
                pet.setLongpo(0);
                pet.setSpdragon(0);
                pet.setDragon(0);
                pet.setRevealNum(0);
                pet.setGrowUpDanNum(0);
//                }
//				String yb = pet.getResistance();
//				if (yb == null || yb.equals("")) {
                int p = random.nextInt(SummonPetAction.kxs.length);
                int p2 = random.nextInt(SummonPetAction.kxs.length);
                while (p2 == p) {
                    p2 = random.nextInt(SummonPetAction.kxs.length);
                }
                pet.setResistance(SummonPetAction.kxs[p] + "|" + SummonPetAction.kxs[p2]);
//				}
                pet.setLyk("");
                pet.setGlyk("");
            }

            AssetUpdate assetUpdate = new AssetUpdate();
            AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
            assetUpdate.setData("XYCS");
            assetUpdate.setPet(pet);
            assetUpdate.setMsg("一阵金光闪过,你的心猿发生了质的变化#24");
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
            return;
        }


        AssetUpdate assetUpdate = new AssetUpdate();

        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setData("XY=");
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("修炼成功#50");
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }


    private void XyClearPoint(RoleSummoning pet, ChannelHandlerContext ctx, LoginResult loginResult, String[] vs) {
        //获取当前可使用点数
        if (pet.getXl() == null) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的心猿还没有学习技能！"));
            return;
        }
        String[] split = pet.getXl().split("\\|");

        StringBuffer buffer = new StringBuffer();
        buffer.append(split[0]);
        buffer.append("|");
        buffer.append(split[1]);
        buffer.append("|");
        buffer.append(split[1]);
        buffer.append("|");
        buffer.append("12001=0_12002=0_12003=0_12004=0_12005=0_12006=0_12007=0_12008=0_12009=0_12010=0_12011=0_12012=0_12013=0_12014=0_12015=0_12016=0");
        AssetUpdate assetUpdate = new AssetUpdate();
        pet.setXl(buffer.toString());
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("洗点成功#50");
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    private void XyAddPoint(RoleSummoning pet, ChannelHandlerContext ctx, LoginResult loginResult, String[] vs) {
        //获取当前可使用点数
        if (pet.getXl() == null) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的心猿还没有学习技能呢！"));
            return;
        }
        //获取当前可使用点数
        String[] split = pet.getXl().split("\\|");
        //可使用点数
        int availablePoints = Integer.parseInt(split[1]);
        String[] split1 = vs[5].split("_");
        List<Skill> skills = new ArrayList<>();
        //已添加点数
        for (String s : split1) {
            String[] split2 = s.split("=");
            if (split1.equals("1")) {
                Skill skill = GameServer.getSkill(split2[0]);
                skills.add(skill);
            }
        }
        if (skills.size() > availablePoints) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("心意点不足无法保存当前方案！"));
            return;
        }
        for (Skill skill : skills) {
            if (new BigDecimal(skill.getValue()).intValue() < skills.size()) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("修炼#R" + skill.getSkillname() + " #Y 需要" + new BigDecimal(skill.getValue()).intValue() + "点总心意点#51"));
                return;
            }

            String skillralation = skill.getSkillralation();
            if (StringUtils.isNotBlank(skillralation)) {
                Skill skill1 = GameServer.getSkill(skillralation);
                if (!skills.contains(skill1)) {
                    SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("修炼#R" + skill.getSkillname() + " #Y 需要先修炼#R " + skill1.getSkillname() + "#17"));
                    return;
                }
            }
        }
        StringBuffer buffer = new StringBuffer();
        buffer.append(split[0]);
        buffer.append("|");
        buffer.append(split[1]);
        buffer.append("|");
        buffer.append(skills.size());
        buffer.append("|");
        buffer.append(vs[5]);
        AssetUpdate assetUpdate = new AssetUpdate();
        pet.setXl(buffer.toString());
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("加点成功#50");
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 召唤兽神兽技能
     */
    public void SSPet(RoleSummoning pet, ChannelHandlerContext ctx, LoginResult login) {
        int ssn = Integer.parseInt(pet.getSsn());
        if (ssn != 2 && ssn != 3 && ssn != 4) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您携带的召唤兽不是神兽!!!"));
            return;
        }
        if (login.getGold().compareTo(new BigDecimal(50000000)) < 0) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("金币不足!!!"));
            return;
        }
        if (pet.getFriendliness() < 200000) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的神兽亲密值不足200000!!!"));
            return;
        }
        if (pet.getOpenSSskill() < 1) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的神兽技能格子还未解封!!!"));
            return;
        }
//      //已打开
        String yb = pet.getBeastSkills();
        String skillid = null;//随机一个神兽技能
        int Chances = GameServer.random.nextInt(4);
        if (Chances == 0) {
            skillid = "1509";
        }//涅槃
        else if (Chances == 1) {
            skillid = "1609";
        }//兵临城下
        else if (Chances == 2) {
            skillid = "1814";
        }//潮鸣电掣
        else if (Chances == 3) {
            skillid = "1866";
        }//如虎添翼
        Skill skill = GameServer.getSkill(skillid);
        if (skill == null) {
            return;
        }
        pet.setBeastSkills(skillid);
        if (yb == null || !yb.equals(skillid)) {
            List<String> skills = new ArrayList<>();
            getskills(skills, pet.getPetSkills());
            getskills(skills, pet.getSkill());
            getskills(skills, pet.getBeastSkills());
            pet.setSkillData(skillData(skills));
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        assetUpdate.updata("D=-50000000");
        login.setGold(login.getGold().subtract(new BigDecimal(50000000)));
        MonitorUtil.getMoney().useD(50000000L);
        pet.setFriendliness(pet.getFriendliness() - 200000);
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("召唤兽学会了" + skill.getSkillname());
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 召唤兽飞升
     */
    public void FSPet(RoleSummoning pet, ChannelHandlerContext ctx, LoginResult login) {
        int ssn = Integer.parseInt(pet.getSsn());
        if (ssn != 3 && ssn != 4) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您携带的召唤兽不是可飞升的神兽!!!"));
            return;
        }
        boolean bool = false;
        //判断条件是否够
        if (pet.getRevealNum() == 0) {//第1次飞升
            if (pet.getGrade() >= 50) {
                bool = true;
            }
        } else if (pet.getRevealNum() == 1) {//第2次飞升
            if (pet.getGrade() >= 188) {
                bool = true;
            }
        } else if (pet.getRevealNum() == 2) {//第3次飞升
            if (pet.getGrade() >= 316) {
                bool = true;
            }
        }
        if (!bool) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的神兽" + pet.getSummoningname() + "不符合飞升的条件!"));
            return;
        }
        if (login.getGold().longValue() < 5000000) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的银两不足500W"));
            return;
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        assetUpdate.updata("D=-5000000");
        login.setGold(login.getGold().subtract(new BigDecimal(5000000)));
        MonitorUtil.getMoney().useD(5000000L);
        if (pet.getRevealNum() == 0) {
            if (ssn == 3) {
                otherPetId(pet, 0);
            }
            BigDecimal grow = mathDouble(Double.parseDouble(pet.getGrowlevel()), 0.1);
            pet.setGrowlevel(Arith.xiaoshu3(grow.doubleValue()));
        } else if (pet.getRevealNum() == 1) {
            if (ssn == 3) {
                otherPetId(pet, 1);
                assetUpdate.updata("NSKIN" + pet.getSid());
            }
            BigDecimal grow = mathDouble(Double.parseDouble(pet.getGrowlevel()), 0.05);
            pet.setGrowlevel(Arith.xiaoshu3(grow.doubleValue()));
        } else if (pet.getRevealNum() == 2) {
            NPCDialogBean bean = new NPCDialogBean(assetUpdate.getI(), 0, pet.getSid(), 60);
            maps.put(bean.getId(), bean);
            assetUpdate.updata("NBASE" + assetUpdate.getI() + "=60");
        }
        pet.setRevealNum(pet.getRevealNum() + 1);
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("您的神兽 " + pet.getSummoningname() + "飞升成功!!!");
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 召唤兽幻肤
     */
    public void HFPet(RoleSummoning pet, ChannelHandlerContext ctx, LoginResult login) {
        try {
            // 检查是否已经幻肤过
            if (pet.getRevealNum() == 1) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的召唤兽#R【" + pet.getSummoningname() + "】#Y已经幻肤过了!"));
                return;
            }

            // 获取当前登录角色
            LoginResult loginResult = GameServer.getAllLoginRole().get(ctx);
            if (loginResult == null) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("无法获取当前登录角色信息!"));
                return;
            }

            // 检查是否有幻肤至尊卡
            List<Goodstable> goods = AllServiceUtil.getGoodsTableService().selectGoodsByRoleIDAndGoodsID(loginResult.getRole_id(), new BigDecimal(92447));
            if (goods.isEmpty()) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("#R没有幻肤至尊卡！"));
                return;
            }

            Goodstable good = goods.get(0);

            // 扣除物品和仙玉
            AssetUpdate assetUpdate = new AssetUpdate();
            assetUpdate.setType(AssetUpdate.USEGOOD);
            useGood(good, 0); // requiredItems 为 0，直接传入 0
            assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());

            // 更新飞升次数
            pet.setRevealNum(pet.getRevealNum() + 1);

            // 根据召唤兽ID执行相应操作
            String summoningId = pet.getSummoningid();
            if (summoningId == null) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽ID为空，无法执行幻肤操作!"));
                return;
            }

            // 处理不同召唤兽的幻肤逻辑
            handlePetSkinChange(pet, assetUpdate, summoningId);

            // 更新召唤兽信息并发送消息
            AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
            assetUpdate.setPet(pet);
            assetUpdate.setMsg("您的召唤兽 " + pet.getSummoningname() + "幻肤成功!");
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));

        } catch (Exception e) {
            // 捕获并处理异常
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("幻肤过程中发生错误：" + e.getMessage()));
        }
    }

    private void handlePetSkinChange(RoleSummoning pet, AssetUpdate assetUpdate, String summoningId) {
        // 定义需要处理的召唤兽ID列表
        Set<String> handledPetIds = new HashSet<>(Arrays.asList(
                "200116", "200097", "200101", "200099", "200098", "200100", "200140", "528", "200158", "200142", "200124"
        ));

        if (handledPetIds.contains(summoningId)) {
            otherPet1Id(pet, 1);
            NPCDialogBean bean = new NPCDialogBean(assetUpdate.getI(), 0, pet.getSid(), 5);
            maps.put(bean.getId(), bean);
            assetUpdate.updata("NBASE" + assetUpdate.getI() + "=5");
        } else {
            otherPet1Id(pet, 1);
            otherPet1Id(pet, pet.getRevealNum() - 1);
        }
    }



    public static void otherPet1Id(RoleSummoning pet, int flag) {
        if (flag != 1) {
            return; // 如果flag不为1，直接返回，减少不必要的计算
        }

        random.nextInt(SummonPetAction.kxs.length);
        random.nextInt(SummonPetAction.kxs.length);

        String summoningId = pet.getSummoningid();
        if (summoningId == null) {
            return; // 如果召唤兽ID为空，直接返回，避免空指针异常
        }

        switch (summoningId) {
            case "500206": //射莲生
                setPetAttributes(pet, "516", 140, 0, "100", "80", "0", "60", "0");
                break;
            case "200148": //吉祥果
                setPetAttributes(pet, "740", 0, 40, "60", "0", "80", "0", "0");
                break;
            case "200140": //孟极
                setPetAttributes(pet, "517", 0, 5, "80", "0", "0", "0", "100");
                break;
            case "200124": //龙马
                setPetAttributes(pet, "70016", 30, 0, "0", "90", "90", "0", "0");
                break;
            case "200158": //妙音鸾女
                setPetAttributes(pet, "555", 0, 0, "0", "60", "0", "95", "0");
                break;
            case "528": //去疾
                setPetAttributes(pet, "6033", 0, 10, "0", "0", "100", "100", "0");
                break;
            case "200097": //垂云叟
                setPetAttributes(pet, "500151", 0, 0, "30", "30", "50", "90", "30");
                break;
            case "200098": //范式之魂
                setPetAttributes(pet, "500152", 10, 5, "30", "50", "90", "30", "30");
                break;
            case "200099": //浪淘沙
                setPetAttributes(pet, "500150", 10, 0, "90", "30", "30", "30", "50");
                break;
            case "200100": //五叶
                setPetAttributes(pet, "500153", 0, 0, "30", "90", "50", "30", "30");
                break;
            case "200101": //颜如玉
                setPetAttributes(pet, "500154", 0, 20, "50", "30", "30", "30", "90");
                break;
            case "200123": //白泽
                setPetAttributes(pet, "400518", 0, 120, "20", "90", "60", "20", "60");
                break;
            case "200116": //年
                setPetAttributes(pet, "500155", 0, 5, "50", "30", "30", "30", "100");
                break;
            case "200117": //画中仙
                setPetAttributes(pet, "400521", 0, 0, "30", "90", "30", "90", "30");
                break;
            case "520": //礼·灵听
                setPetAttributes(pet, "500210", 0, 0, "50", "30", "30", "30", "90");
                break;
            case "519": //御·飞轩
                setPetAttributes(pet, "500209", 0, 160, "30", "50", "30", "30", "90");
                break;
            case "518": //乐·大吕
                setPetAttributes(pet, "500208", 0, 0, "100", "30", "30", "30", "30");
                break;
            case "517": //书·兰亭
                setPetAttributes(pet, "500207", 0, 100, "90", "50", "30", "30", "30");
                break;
            case "527": //数·幻方
                setPetAttributes(pet, "6024", 120, 120, "90", "50", "30", "50", "30");
                break;
            case "200065": //蝴蝶仙子
                setPetAttributes(pet, "633", 120, 120, "30", "100", "30", "70", "30");
                break;
            case "200147": //精卫
                setPetAttributes(pet, "6010", 0, 70, "65", "30", "30", "30", "95");
                break;
            case "200135": //冥灵妃子
                setPetAttributes(pet, "6012", 0, 140, "50", "50", "50", "50", "50");
                break;
            case "200093": //剑精灵
                setPetAttributes(pet, "6014", 0, 100, "50", "50", "50", "50", "50");
                break;
            case "200076": //凤凰
                setPetAttributes(pet, "6004", 0, 0, "30", "30", "35", "30", "100");
                break;
            case "200077": //猴精
                setPetAttributes(pet, "6006", 140, 30, "30", "70", "90", "30", "30");
                break;
            case "200085": //黄金兽
                setPetAttributes(pet, "6008", 60, 0, "100", "30", "30", "30", "30");
                break;
            case "200090": //冰雪魔
                setPetAttributes(pet, "6002", 0, 0, "30", "30", "30", "100", "30");
                break;
            case "200087": //泥石怪
                setPetAttributes(pet, "773", 98, 0, "30", "30", "100", "30", "30");
                break;
            case "200073": //火神女娲
                setPetAttributes(pet, "6029", 0, 150, "30", "45", "30", "100", "30");
                break;
            case "200046": //复活女娲
                setPetAttributes(pet, "6028", 0, 180, "100", "30", "30", "30", "55");
                break;
            case "200055": //符咒女娲
                setPetAttributes(pet, "6027", 0, 130, "30", "55", "100", "30", "30");
                break;
            case "200030": //战神女娲
                setPetAttributes(pet, "6031", 160, 100, "40", "100", "30", "30", "30");
                break;
            case "200079": //舍生女娲
                setPetAttributes(pet, "6030", 0, 0, "35", "30", "30", "100", "30");
                break;
            case "200078": //神兵
                setPetAttributes(pet, "6020", 0, 120, "95", "30", "65", "30", "30");
                break;
            case "200142": //北冥龙君
                setPetAttributes(pet, "70019", 25, 0, "80", "30", "80", "30", "30");
                break;
            case "525": //梦鹿
                setPetAttributes(pet, "775", 0, 160, "0", "100", "40", "50", "0");
                break;
            case "200075": //蛟龙
                setPetAttributes(pet, "70020", 0, 160, "100", "0", "50", "0", "0");
                pet.setSkill("1209|1211|2017");
                break;
            default:
                // 处理未知的召唤兽ID，避免遗漏
                break;
        }
    }

    private static void setPetAttributes(RoleSummoning pet, String skin, int ap, int sp, String gold, String wood, String soil, String water, String fire) {
        pet.setSummoningskin(skin);
        pet.setAp(pet.getAp() + ap);
        pet.setSp(pet.getSp() + sp);
        pet.setGold(gold);
        pet.setWood(wood);
        pet.setSoil(soil);
        pet.setWater(water);
        pet.setFire(fire);
    }


    /**
     * 召唤兽转生 点化
     */
    public void DHPet(RoleSummoning pet, ChannelHandlerContext ctx, LoginResult login) {
        int roleTurn = login.getTurnAround();
        int petTurn = pet.getTurnRount();
        if (petTurn >= roleTurn || petTurn >= 4) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("快去升级吧!你快驾驭不你的召唤兽了"));
            return;
        }
        int lvl = pet.getGrade();
        if (petTurn == 0) {
            if (lvl != 100) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("你的召唤兽" + pet.getSummoningname() + "等级不够,还需多加历练!"));
                return;
            }
            if (pet.getFriendliness() < 100000) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的召唤兽与您的亲密值不足10万"));
                return;
            }
        } else if (petTurn == 1) {
            if (lvl != 221) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("你的召唤兽" + pet.getSummoningname() + "等级不够,还需多加历练!"));
                return;
            }
            if (pet.getFriendliness() < 200000) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的召唤兽与您的亲密值不足20万"));
                return;
            }
        } else if (petTurn == 2) {
            if (lvl != 362) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("你的召唤兽" + pet.getSummoningname() + "等级不够,还需多加历练!"));
                return;
            }
            if (pet.getFriendliness() < 500000) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的召唤兽与您的亲密值不足50万"));
                return;
            }
        } else if (petTurn == 3) {
            if (lvl != 543) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("你的召唤兽" + pet.getSummoningname() + "等级不够,还需多加历练!"));
                return;
            }
            if (pet.getFriendliness() < 2000000) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的召唤兽与您的亲密值不足200万"));
                return;
            }
        }
        lvl++;
        petTurn++;
        if (petTurn <= 3) {
            if (login.getGold().longValue() < (2000000L * petTurn)) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的银两不足" + (200 * petTurn) + "万"));
                return;
            }
        } else {
            if (login.getGold().longValue() < 20000000) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的银两不足2000万"));
                return;
            }
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        if (petTurn <= 3) {
            assetUpdate.updata("D=-" + 2000000 * petTurn);
            login.setGold(login.getGold().subtract(new BigDecimal(2000000 * petTurn)));
            MonitorUtil.getMoney().useD(petTurn * 2000000L);
        } else {
            assetUpdate.updata("D=-20000000");
            login.setGold(login.getGold().subtract(new BigDecimal(20000000)));
            MonitorUtil.getMoney().useD(20000000L);
        }
        //设置这只召唤兽的根骨、灵性、力量、敏捷、经验为0
        pet.setBone(0);
        pet.setSpir(0);
        pet.setPower(0);
        pet.setSpeed(0);
        pet.setCalm(0);
        pet.setExp(new BigDecimal(0));
        //等级
        pet.setGrade(lvl);
        pet.setTurnRount(petTurn);
        if (petTurn <= 3) {
            pet.setFriendliness(pet.getFriendliness() - (petTurn == 1 ? 100000 : petTurn == 2 ? 200000 : 500000));
        } else {
            pet.setFriendliness(pet.getFriendliness() - 2000000);
        }
        //设置忠诚度为100
        pet.setFaithful(100);
        //成长率加0.1
        BigDecimal grow = mathDouble(Double.parseDouble(pet.getGrowlevel()), 0.1);
        pet.setGrowlevel(Arith.xiaoshu3(grow.doubleValue()));

        pet.setBasishp(0);
        pet.setBasismp(0);
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        if (petTurn <= 3) {
            assetUpdate.setMsg("#G召唤兽转生成功");
        } else {
            assetUpdate.setMsg("#G召唤兽点化成功");
            if (pet.getSsn().equals("2")) {
                NPCDialogBean bean = new NPCDialogBean(assetUpdate.getI(), 0, pet.getSid(), 60);
                maps.put(bean.getId(), bean);
                assetUpdate.updata("NBASE" + assetUpdate.getI() + "=60");
            } else if (pet.getSsn().equals("6")) {
                NPCDialogBean bean = new NPCDialogBean(assetUpdate.getI(), 0, pet.getSid(), 200);
                maps.put(bean.getId(), bean);
                assetUpdate.updata("NBASE" + assetUpdate.getI() + "=200");
            }
        }
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 神兽使用神兽飞升丹的方法
     */
    public void petFlyUpDan(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        if (!pet.getSsn().equals("2")) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("这只召唤兽不能使用神兽飞升丹!!!"));
            return;
        }
        if (pet.getFlyupNum() >= 3) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("神兽 " + pet.getSummoningname() + "的飞升次数已达到上限！"));
            return;
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        pet.setFlyupNum(pet.getFlyupNum() + 1);
        BigDecimal grow = mathDouble(Double.parseDouble(pet.getGrowlevel()), 0.1);
        pet.setGrowlevel(Arith.xiaoshu3(grow.doubleValue()));
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("神兽 " + pet.getSummoningname() + "飞升成功!!！");
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 化形丹
     */
    public static void changeDan(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
//		200123 白泽 200116 年 200117画中仙 200097 垂云叟 200098 范式之魂 200099 浪淘沙 200100 五叶 200101 颜如玉
        int petid = Integer.parseInt(pet.getSummoningid());
        if (petid != 200123 && petid != 200116 && petid != 200117 && petid != 210008 && petid != 200097 &&
                petid != 200098 && petid != 200099 && petid != 200100 && petid != 200101 && petid != 200196 && petid != 200197 && petid != 200198 && petid != 200199 && petid != 200200 && petid != 200201 && petid != 200202
                && petid != 516 && petid != 517 && petid != 518 && petid != 519 && petid != 520) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("白泽 年 画中仙 颜如玉 垂云叟 五叶 范式之魂 莲生 飞轩 聆听 大吕 幻方 圣猿 兰亭才能使用化形丹"));
            return;
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        String skin = null;
        if (petid == 200123) {
            skin = "400518";
        } else if (petid == 200116) {
            skin = "400523";
        } else if (petid == 200117) {
            skin = "400521";
        } else if (petid == 200097) {
            skin = "400519";
        } else if (petid == 200098) {
            skin = "400520";
        } else if (petid == 200099) {
            skin = "400522";
        } else if (petid == 200100) {
            skin = "400524";
        } else if (petid == 200101) {
            skin = "400525";
        } else if (petid == 200196) {
            skin = "500176";
        } else if (petid == 200197) {
            skin = "500175";
        } else if (petid == 200198) {
            skin = "500183";
        } else if (petid == 200199) {
            skin = "500192";
        } else if (petid == 200200) {
            skin = "500200";
        } else if (petid == 200201) {
            skin = "500026";
        } else if (petid == 200202) {
            skin = "500199";
        } else if (petid == 516) {
            skin = "900087";
        } else if (petid == 517) {
            skin = "900059";
        } else if (petid == 518) {
            skin = "900086";
        } else if (petid == 519) {
            skin = "900088";
        } else if (petid == 520) {
            skin = "900089";
        } else if (petid == 210008) {
            skin = "10017";
        }
        pet.setSummoningskin(skin);
        pet.setColorScheme(null);
        String four = pet.getFourattributes();
        int ran1 = pet.getSI2("hhp");
        int ran2 = pet.getSI2("hmp");
        int ran3 = pet.getSI2("hap");
        int ran4 = pet.getSI2("hsp");
        pet.setHp(pet.getHp() - ran1);
        pet.setMp(pet.getMp() - ran2);
        pet.setAp(pet.getAp() - ran3);
        pet.setSp(pet.getSp() - ran4);
        //先清除之前的龙骨效果
        if (ran1 != 0) {
            four = DrawnitemsAction.Splice(four, "hhp=" + ran1, 4);
        }
        if (ran2 != 0) {
            four = DrawnitemsAction.Splice(four, "hmp=" + ran2, 4);
        }
        if (ran3 != 0) {
            four = DrawnitemsAction.Splice(four, "hap=" + ran3, 4);
        }
        if (ran4 != 0) {
            four = DrawnitemsAction.Splice(four, "hsp=" + ran4, 4);
        }
        ran1 = 0;
        ran2 = 0;
        ran3 = 0;
        ran4 = 0;
        switch (GameServer.random.nextInt(4)) {
            case 0:
                ran1 = 12;
                break;
            case 1:
                ran2 = 12;
                break;
            case 2:
                ran3 = 12;
                break;
            case 3:
                ran4 = 12;
                break;
        }
        pet.setHp(pet.getHp() + ran1);
        pet.setMp(pet.getMp() + ran2);
        pet.setAp(pet.getAp() + ran3);
        pet.setSp(pet.getSp() + ran4);
        if (ran1 != 0) {
            four = DrawnitemsAction.Splice(four, "hhp=" + ran1, 2);
        }
        if (ran2 != 0) {
            four = DrawnitemsAction.Splice(four, "hmp=" + ran2, 2);
        }
        if (ran3 != 0) {
            four = DrawnitemsAction.Splice(four, "hap=" + ran3, 2);
        }
        if (ran4 != 0) {
            four = DrawnitemsAction.Splice(four, "hsp=" + ran4, 2);
        }

        ColorScheme colorScheme = GameServer.getColors(0);
//        if (colorScheme == null) {
//            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("没有该类型的变色方案"));
//            return;
//        }
//        pet.setColorScheme(colorScheme.getValue());
        pet.setFourattributes(four);
        pet.setBasishp(0);
        pet.setBasismp(0);
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
//        assetUpdate.setMsg("召唤兽变色成功");
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    public static void changeMagicSkin(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx) {
        int petSkin = Integer.parseInt(pet.getSummoningskin());
        if (magicSkinMap.containsValue(Integer.valueOf(petSkin))) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的召唤兽#R" + pet.getSummoningname() + "#Y已经拥有幻肤了"));
            return;
        }
        boolean match = false;

        String[] vs = good.getValue().split("\\|");
        for (String v : vs) {
            if (Integer.parseInt(v.split("=")[1]) == petSkin) {
                match = true;
                break;
            }
        }
        if (!match) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("您的召唤兽#R" + pet.getSummoningname() + "#Y不能使用这个幻肤"));
            return;
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        pet.setSummoningskin((new StringBuilder()).append(magicSkinMap.get(Integer.valueOf(petSkin))).append("").toString());
        pet.setColorScheme(null);
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("恭喜你！你的召唤兽#R" + pet.getSummoningname() + "#Y成功使用了幻肤。");
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }


    /**
     * 使用超级聚魄丹
     */
    public void useDraw(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        if (pet.getTurnRount() < 3) {// 判断是否为三转召唤兽
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("该召唤兽未3转！"));
            return;
        }
        if (pet.getPetSkills() == null || pet.getPetSkills().equals("")) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽没有技能"));
            return;
        }
        if (pet.getGoods() != null) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("该召唤兽携带着装备"));
            return;
        }
        if (login.getSummoning_id() != null) {
            if (login.getSummoning_id().compareTo(pet.getSid()) == 0) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("这只召唤兽已在参战中！！！"));
                return;
            }
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        // 成功与否，删除召唤兽
        AllServiceUtil.getRoleSummoningService().deleteRoleSummoningBySid(pet.getSid());
        RoleSummoning pet2 = new RoleSummoning();
        pet2.setSid(pet.getSid());
        assetUpdate.setPet(pet2);
        // 判断聚魄丹概率(20%),成功与否
        if (GameServer.random.nextInt(100) < 40) {
            // 随机取召唤兽的技能提取
            List<String> skills = new ArrayList<>();
            if (pet.getPetSkills() != null && !pet.getPetSkills().equals("")) {
                String[] vs = pet.getPetSkills().split("\\|");
                for (int i = 0; i < vs.length; i++) {
                    if (!vs[i].equals("")) {
                        skills.add(vs[i]);
                    }
                }
            }
            String id = skills.get(GameServer.random.nextInt(skills.size()));
            Skill skill = GameServer.getSkill(id);
            good.setType(2326L);
            int grade = skill.getSkilltype();
            String instru = "技能=" + skill.getSkillname() + "|技能等级=" + (grade == 1 ? "普通" : grade == 2 ? "高级" : grade == 3 ? "终极" : "终极");
            good.setValue(instru);
            AllServiceUtil.getGoodsrecordService().insert(good, null, 1, 9);
            AllServiceUtil.getGoodsTableService().updateGoodRedis(good);
            assetUpdate.setGood(good);
            assetUpdate.setMsg("提取成功!");
        } else {
            useGood(good, 1);
            assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
            assetUpdate.setMsg("提取失败!");
        }
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 终极修炼丹
     */
    public void train(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        if (!pet.getPetSkills().contains("3034")) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("该召唤兽没有???"));
            return;
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        int train = DropUtil.isV(90D) ? 5 : 20;// 随机加的培养值
        assetUpdate.setMsg("#您的 " + pet.getSummoningname() + " 召唤兽" + "提升修炼度:" + train + " 点");
        pet.setTrainNum(pet.getTrainNum() + train);

//		// 培养值满，随机出现一个终极技能
        if (pet.getTrainNum() >= 999) {
            List<String> skills = new ArrayList<>();
            if (pet.getPetSkills() != null && !pet.getPetSkills().equals("")) {
                String[] vs = pet.getPetSkills().split("\\|");
                for (int i = 0; i < vs.length; i++) {
                    if (!vs[i].equals("") && !vs[i].equals("3034")) {
                        skills.add(vs[i]);
                    }
                }
            }

            List<String> ids = new ArrayList<>();
            ids.add("1606");
            ids.add("1607");
            ids.add("1608");
            ids.add("1828");
            ids.add("1829");
            ids.add("1830");
            ids.add("1840");
            ids.add("1841");
            ids.add("1842");
            ids.add("1867");
            ids.add("1868");
            ids.add("1869");
            ids.add("1881");

            for (int i = ids.size() - 1; i >= 0; i--) {
                if (skills.contains(ids.get(i))) {
                    ids.remove(i);
                }
            }
            s:
            while (true) {
                if (ids.size() == 0) {
                    break;
                }
                String id = ids.remove(GameServer.random.nextInt(ids.size()));
                Skill skill = GameServer.getSkill(id);
                if (skill == null) {
                    continue;
                }
                if (skill.getSkillralation() != null && !skill.getSkillralation().equals("")) {
                    String[] chongtu = skill.getSkillralation().split("\\|");
                    for (int i = 0; i < chongtu.length; i++) {
                        if (chongtu[i].equals(skill.getSkillid() + "")) {
                            continue;
                        }
                        if (skills.contains(chongtu[i])) {
                            Skill skill2 = GameServer.getSkill(chongtu[i]);
                            if (skill2 == null) {
                                continue;
                            }
                            continue s;
                        }
                    }
                }
                skills.add(id);
                StringBuilder buffer = new StringBuilder();
                for (int i = 0; i < skills.size(); i++) {
                    if (buffer.length() != 0) {
                        buffer.append("|");
                    }
                    buffer.append(skills.get(i));
                }
                pet.setPetSkills(buffer.toString());
                getskills(skills, pet.getSkill());
                getskills(skills, pet.getBeastSkills());
                pet.setSkillData(skillData(skills));
                assetUpdate.updata("T悟技");
                SuitMixdeal.PYJN(login.getRolename(), pet.getSummoningname(), skill.getSkillname());
                SuitMixdeal.PYJN1(login.getRolename(), pet.getSummoningname(), skill.getSkillname());
                break;
            }
            pet.setTrainNum(0);//培养值重置
        }
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 召唤兽使用伐骨洗髓丹的方法  --洗除召唤兽服用的龙之骨效果
     */
    public void useBoneElution(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        //判断这只召唤兽是否有是使用过龙骨
        if (pet.getDragon() <= 0) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽没有服用过龙之骨"));
            return;
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        // 清除龙骨加的成长率
        pet.setGrowlevel(Arith.sub(Double.parseDouble(pet.getGrowlevel()), (0.01 * pet.getDragon())) + "");
        pet.setDragon(0);//龙骨使用次数清零
        // 清除龙骨加的hp、mp、ap、sp
        pet.setHp(pet.getHp() - pet.getSI2("hp"));
        pet.setMp(pet.getMp() - pet.getSI2("mp"));
        pet.setAp(pet.getAp() - pet.getSI2("ap"));
        pet.setSp(pet.getSp() - pet.getSI2("sp"));
        //清空存储的龙骨加的hp、mp、ap、sp
        String four = pet.getFourattributes();
        four = DrawnitemsAction.Splice(four, "hp=" + pet.getSI2("hp"), 4);
        four = DrawnitemsAction.Splice(four, "mp=" + pet.getSI2("mp"), 4);
        four = DrawnitemsAction.Splice(four, "ap=" + pet.getSI2("ap"), 4);
        four = DrawnitemsAction.Splice(four, "sp=" + pet.getSI2("sp"), 4);
        pet.setFourattributes(four);
        pet.setBasishp(0);
        pet.setBasismp(0);
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("#G龙之骨已经被清除");
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }


    public void useSoulElution(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        if (pet.getDragonSoul() <= 0) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽没有服用过龙之魄"));
            return;
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());

        int dragonSoul = pet.getDragonSoul();
        pet.setHp(pet.getHp() - pet.getSI2(dragonSoul + "php"));
        pet.setMp(pet.getMp() - pet.getSI2(dragonSoul + "pmp"));
        pet.setAp(pet.getAp() - pet.getSI2(dragonSoul + "pap"));
        pet.setSp(pet.getSp() - pet.getSI2(dragonSoul + "psp"));

        String four = pet.getFourattributes();
        four = DrawnitemsAction.Splice(four, dragonSoul + "php=" + pet.getSI2(dragonSoul + "php="), 4);
        four = DrawnitemsAction.Splice(four, dragonSoul + "pmp=" + pet.getSI2(dragonSoul + "pmp="), 4);
        four = DrawnitemsAction.Splice(four, dragonSoul + "pap=" + pet.getSI2(dragonSoul + "pap="), 4);
        four = DrawnitemsAction.Splice(four, dragonSoul + "psp=" + pet.getSI2(dragonSoul + "psp="), 4);
        pet.setFourattributes(four);
        pet.setDragonSoul(pet.getDragonSoul() - 1);
        pet.setBasishp(0);
        pet.setBasismp(0);
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("你的召唤兽#R" + pet.getSummoningname() + "#Y已经成功洗掉了1颗龙之魄");
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 召唤兽使用超级伐骨洗髓丹的方法  --洗除召唤兽服用的超级龙之骨效果
     */
    public void useBoneElutionsp(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        AssetUpdate assetUpdate = new AssetUpdate();
        //判断这只召唤兽是否有是使用过超级龙之骨
//        if (pet.getSpdragon() <= 0 && pet.getLongpo() <= 0 && pet.getLongjing() <= 0 && pet.getDragon() <= 0) {  12月27日修改
        if (pet.getSpdragon() <= 0 && pet.getLongpo() <= 0 && pet.getLongjing() <= 0) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽没有服用过任何物品"));
            return;
        }
	/*	if (pet.getxuepo() <= 0) {
			SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽没有服用过血魄"));
			return;
		}*/
//        if (pet.getLongpo() >= 0 || pet.getSpdragon() >= 0 || pet.getDragon() >= 0) {//12月27日修改
        if (pet.getLongpo() >= 0 || pet.getSpdragon() >= 0) {
            assetUpdate.setType(AssetUpdate.USEGOOD);
            useGood(good, 1);
            assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
            // 清除超级龙之骨加的成长率
            pet.setGrowlevel(Arith.sub(Double.parseDouble(pet.getGrowlevel()), (0.01 * (pet.getSpdragon() + pet.getLongpo()))) + "");
            pet.setSpdragon(0);//超级龙之骨使用次数清零
            pet.setLongpo(0);
            // 清除龙骨加的hp、mp、ap、sp
            pet.setHp(pet.getHp() - pet.getSI2("hps"));
            pet.setMp(pet.getMp() - pet.getSI2("mps"));
            pet.setAp(pet.getAp() - pet.getSI2("aps"));
            pet.setSp(pet.getSp() - pet.getSI2("sps"));
            //清空存储的龙骨加的hp、mp、ap、sp
            String four = pet.getFourattributes();
            four = DrawnitemsAction.Splice(four, "hps=" + pet.getSI2("hps"), 4);
            four = DrawnitemsAction.Splice(four, "mps=" + pet.getSI2("mps"), 4);
            four = DrawnitemsAction.Splice(four, "aps=" + pet.getSI2("aps"), 4);
            four = DrawnitemsAction.Splice(four, "sps=" + pet.getSI2("sps"), 4);
            pet.setFourattributes(four);
//            if (pet.getDragon() >= 0) {
//                // 清除龙骨加的成长率
//                pet.setGrowlevel(Arith.sub(Double.parseDouble(pet.getGrowlevel()), (0.01 * pet.getDragon())) + "");
//                pet.setDragon(0);//龙骨使用次数清零
//                // 清除龙骨加的hp、mp、ap、sp
//                pet.setHp(pet.getHp() - pet.getSI2("hp"));
//                pet.setMp(pet.getMp() - pet.getSI2("mp"));
//                pet.setAp(pet.getAp() - pet.getSI2("ap"));
//                pet.setSp(pet.getSp() - pet.getSI2("sp"));
//                //清空存储的龙骨加的hp、mp、ap、sp
//                four = pet.getFourattributes();
//                four = DrawnitemsAction.Splice(four, "hp=" + pet.getSI2("hp"), 4);
//                four = DrawnitemsAction.Splice(four, "mp=" + pet.getSI2("mp"), 4);
//                four = DrawnitemsAction.Splice(four, "ap=" + pet.getSI2("ap"), 4);
//                four = DrawnitemsAction.Splice(four, "sp=" + pet.getSI2("sp"), 4);
//                pet.setFourattributes(four);
//            }
            pet.setBasishp(0);
            pet.setBasismp(0);
        }
        if (pet.getLongjing() >= 0) {
            pet.setGrowlevel(Arith.sub(Double.parseDouble(pet.getGrowlevel()), (0.1 * pet.getLongjing())) + "");
            pet.setLongjing(0);//龙精使用次数清零
        }
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setMsg("#G服用物品已经被清除");
        assetUpdate.setPet(pet);
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 召唤兽使用龙魄伐骨洗髓丹的方法  --洗除召唤兽服用的龙之骨效果
     */
    public void useLongpoClean(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        //判断这只召唤兽是否有是使用过超级龙之骨
        if (pet.getLongpo() <= 0) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽没有服用过龙魄"));
            return;
        }
	/*	if (pet.getxuepo() <= 0) {
			SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽没有服用过血魄"));
			return;
		}*/
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        // 清除超级龙之骨加的成长率
        pet.setGrowlevel(Arith.sub(Double.parseDouble(pet.getGrowlevel()), (0.01 * pet.getLongpo())) + "");
        pet.setLongpo(0);//超级龙之骨使用次数清零
        // 清除龙骨加的hp、mp、ap、sp
        pet.setHp(pet.getHp() - pet.getSI2("hps"));
        pet.setMp(pet.getMp() - pet.getSI2("mps"));
        pet.setAp(pet.getAp() - pet.getSI2("aps"));
        pet.setSp(pet.getSp() - pet.getSI2("sps"));
        //清空存储的龙骨加的hp、mp、ap、sp
        String four = pet.getFourattributes();
        four = DrawnitemsAction.Splice(four, "hps=" + pet.getSI2("hps"), 4);
        four = DrawnitemsAction.Splice(four, "mps=" + pet.getSI2("mps"), 4);
        four = DrawnitemsAction.Splice(four, "aps=" + pet.getSI2("aps"), 4);
        four = DrawnitemsAction.Splice(four, "sps=" + pet.getSI2("sps"), 4);
        pet.setFourattributes(four);
        pet.setBasishp(0);
        pet.setBasismp(0);
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setMsg("#G超级龙之骨已经被清除");
        assetUpdate.setPet(pet);
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 召唤兽使用龙精伐骨洗髓丹的方法  --洗除召唤兽服用的龙之骨效果
     */
    public void useLongjinClean(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        //判断这只召唤兽是否有是使用过超级龙之骨
        if (pet.getLongjing() <= 0) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽没有服用过龙精"));
            return;
        }
	/*	if (pet.getxuepo() <= 0) {
			SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽没有服用过血魄"));
			return;
		}*/
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        // 清除超级龙之骨加的成长率
        pet.setGrowlevel(Arith.sub(Double.parseDouble(pet.getGrowlevel()), (0.1 * pet.getLongjing())) + "");
        pet.setLongjing(0);//超级龙之骨使用次数清零
//		// 清除龙骨加的hp、mp、ap、sp
//		pet.setHp(pet.getHp() - pet.getSI2("hps"));
//		pet.setMp(pet.getMp() - pet.getSI2("mps"));
//		pet.setAp(pet.getAp() - pet.getSI2("aps"));
//		pet.setSp(pet.getSp() - pet.getSI2("sps"));
//		//清空存储的龙骨加的hp、mp、ap、sp
//		String four = pet.getFourattributes();
//		four = DrawnitemsAction.Splice(four, "hps=" + pet.getSI2("hps"), 4);
//		four = DrawnitemsAction.Splice(four, "mps=" + pet.getSI2("mps"), 4);
//		four = DrawnitemsAction.Splice(four, "aps=" + pet.getSI2("aps"), 4);
//		four = DrawnitemsAction.Splice(four, "sps=" + pet.getSI2("sps"), 4);
//		pet.setFourattributes(four);
//		pet.setBasishp(0);
//		pet.setBasismp(0);
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setMsg("#G龙精已经被清除");
        assetUpdate.setPet(pet);
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 龙涎丸
     */
    public static void dragonSaliva(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        if (!(pet.getSsn().equals("5") || pet.getSummoningid().equals("200125"))) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("不是龙涎丸宝宝"));
            return;
        }
        int drac = pet.getDraC();
        if (drac >= 9) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("已经达到最大使用次数"));
            return;
        }
//		0转50级1  50 0转90级2  90 1转50级3  151 1转90级4  191 1转120级5 221
//		2转50级6  272 2转100级7 322 2转140级8 362 3转70级9  433
        int maxsum = 0;
        if (pet.getGrade() >= 433) {
            maxsum = 9;
        } else if (pet.getGrade() >= 362) {
            maxsum = 8;
        } else if (pet.getGrade() >= 322) {
            maxsum = 7;
        } else if (pet.getGrade() >= 272) {
            maxsum = 6;
        } else if (pet.getGrade() >= 221) {
            maxsum = 5;
        } else if (pet.getGrade() >= 191) {
            maxsum = 4;
        } else if (pet.getGrade() >= 151) {
            maxsum = 3;
        } else if (pet.getGrade() >= 90) {
            maxsum = 2;
        } else if (pet.getGrade() >= 50) {
            maxsum = 1;
        }
        if (drac >= maxsum) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽当前等级最多使用" + maxsum + "个"));
            return;
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        drac++;
        pet.setDraC(drac);
        if (drac == 9) {
            pet.setColorScheme("1|0|255|256|0|0|512|256|0|512|0|256");
        }
        double grow = Double.parseDouble(pet.getGrowlevel()) + 0.02;
        pet.setGrowlevel(Arith.xiaoshu3(grow));
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("#G使用成功");
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 变色丹
     */
    public static void GrowUpDan(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        String value = good.getValue();
        if (value == null || value.equals("")) value = "100|0";
        String[] v = value.split("\\|");
        if (!(v[1].equals("0") || v[1].equals(pet.getSummoningid()))) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽无法使用该类型的元气丹"));
            return;
        }
        //判断丹的类型是变色还是元气
        int type = 0;//0是变色 1是元气
        if (good.getGoodsname().contains("元气")) {
            type = 1;
        }
//        if (type == 0 && pet.getSsn().equals("6")||pet.getSsn().equals("2")||pet.getSsn().equals("4")) {
//            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("该类型召唤兽不能吃变色丹"));
//            return;
//        }
        if (type == 0) {
            if (pet.getSsn().equals("6") || pet.getSsn().equals("2") || pet.getSsn().equals("4")) {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("该类型召唤兽不能吃变色丹"));
                return;
            }
        }

        ColorScheme colorScheme = GameServer.getColors(Integer.parseInt(v[1]));
        if (colorScheme == null) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("没有该类型的变色方案"));
            return;
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());

        int gl = Integer.parseInt(v[0]);
        if (gl < GameServer.random.nextInt(100)) {
            assetUpdate.setMsg("召唤兽吃了一点反应都没有");
        } else {
            if (type == 1) {
                double grow = Double.parseDouble(pet.getGrowlevel());
                grow = Arith.sub(grow, Arith.div(pet.getGrowUpDanNum(), 1000.0));
                int zhi = colorScheme.getMin() + GameServer.random.nextInt(colorScheme.getMax() - colorScheme.getMin() + 1);
                grow = Arith.add(grow, Arith.div(zhi, 1000.0));
                pet.setGrowUpDanNum(zhi);
                pet.setGrowlevel(Arith.xiaoshu3(grow));
                assetUpdate.setMsg("#G使用成功,召唤兽成长发生了变化");
            } else {
                assetUpdate.setMsg("召唤兽变色成功");
            }
            if (!pet.getSsn().equals("6") && type != 1) {
                pet.setColorScheme(colorScheme.getValue());
            }
            AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
            assetUpdate.setPet(pet);
        }
        //宠物跟随
//		LoginResult loginResult = GameServer.getAllLoginRole().get(ctx);
//		List<RoleSummoning> roleSummonings = AllServiceUtil.getRoleSummoningService().selectRoleSummoningsByRoleID(loginResult.getRole_id());
//		loginResult.setShowRoleSummoningList(roleSummonings);
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 使用龙之骨的方法
     */
    public void useKeel(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
		/*// 先判断这只召唤兽的龙骨数量
		if (pet.getDragon() >= 5) {// 不超过3个
			SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("龙之骨数量已达到上限！"));
			return;
		}
		useGood(good, 1);
		pet.setDragon(pet.getDragon() + 1);// 龙骨数量加一
		pet.setGrowlevel(mathDouble(Double.parseDouble(pet.getGrowlevel()), 0.01).toString());// 成长率加0.01
		while (true) {// 随机给这只召唤兽的hp、mp、ap、sp随机加6点
			String four = pet.getFourattributes();
			int ran1 = GameServer.random.nextInt(7);
			int ran2 = GameServer.random.nextInt(7);
			int ran3 = GameServer.random.nextInt(7);
			int ran4 = GameServer.random.nextInt(7);
			if (ran1 + ran2 + ran3 + ran4 == 6) {
				pet.setHp(pet.getHp() + ran1);
				pet.setMp(pet.getMp() + ran2);
				pet.setAp(pet.getAp() + ran3);
				pet.setSp(pet.getSp() + ran4);
				if (ran1 != 0) {
					four = DrawnitemsAction.Splice(four, "hp=" + ran1, 2);
				}
				if (ran2 != 0) {
					four = DrawnitemsAction.Splice(four, "mp=" + ran2, 2);
				}
				if (ran3 != 0) {
					four = DrawnitemsAction.Splice(four, "ap=" + ran3, 2);
				}
				if (ran4 != 0) {
					four = DrawnitemsAction.Splice(four, "sp=" + ran4, 2);
				}
				pet.setFourattributes(four);
				break;
			}
		}
		AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
		AssetUpdate assetUpdate = new AssetUpdate();
		assetUpdate.setType(AssetUpdate.USEGOOD);
		assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
		assetUpdate.setPet(pet);
		SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));*/
        boolean isDragonSpirit = good.getGoodsname().contains("龙之精");
        boolean isDragonSoul = good.getGoodsname().contains("龙之魄");
        boolean isDragonBone = (!isDragonSpirit && !isDragonSoul);
//        if (isDragonBone && pet.getDragon() >= 3) {
//            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("最多只能服用3个龙骨！"));
//            return;
//        }

        ConcurrentHashMap<Integer, Configure> s = GameServer.getAllConfigure();
        Configure configure = s.get(1);
        // 先判断这只召唤兽的龙骨数量
        if (isDragonBone && pet.getDragon() >= Integer.parseInt(configure.getLzgsx())) {// 不超过3个
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("最多只能服用" + Integer.parseInt(configure.getLzgsx()) + "个龙骨！"));
            return;
        }

        int petId = Integer.parseInt(pet.getSummoningid());
        /*if ((isDragonSpirit || isDragonSoul) && petId != 200138 && petId != 200140 && petId != 200142 && petId != 200141 && petId != 200158 && petId != 200192 && petId != 200124 && petId != 200143 && petId != 515 && petId != 200102 && petId != 200103 && petId != 200104 && petId != 200105 && petId != 200106 && petId != 200107 && petId != 200108 && petId != 200109 && petId != 200110 && petId != 200111 && petId != 200112 && petId != 200113 && petId != 200115 && petId != 200123 && petId != 200116 && petId != 200117 && petId != 200097 && petId != 200098 && petId != 200099 && petId != 200100 && petId != 200101 && petId != 516 && petId != 517 && petId != 518 && petId != 519 && petId != 520) {

            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("只有#R五常神兽、六艺神兽、鎏金宝鉴、珍稀神兽、老版神兽、人形神兽#Y才能使用#G龙之精和龙之魄"));
            return;
        }*/
        if (isDragonSpirit && pet.getDragonSpirit() >= 10) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("最多只能服用10个龙之精"));
            return;
        }
        if (isDragonSoul && pet.getDragonSoul() >= 10) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("最多只能服用10个龙之魄"));
            return;
        }
        useGood(good, 1);
        boolean isSuperDragonBone = good.getGoodsname().contains("超级");
        if (isDragonBone) {
            pet.setDragon(pet.getDragon() + 1);
        } else if (isDragonSpirit) {
            pet.setDragonSpirit(pet.getDragonSpirit() + 1);
        } else {
            pet.setDragonSoul(pet.getDragonSoul() + 1);
        }
        if (isDragonSpirit) {
            pet.setGrowlevel(
                    mathDouble(Double.parseDouble(pet.getGrowlevel()), 0.1D).toString());
        } else if (isDragonBone) {
            pet.setGrowlevel(
                    mathDouble(Double.parseDouble(pet.getGrowlevel()), 0.01D).toString());
        }
        if (isDragonBone) {
            while (true) {
                String four = pet.getFourattributes();
                int ran1 = GameServer.random.nextInt(7);
                int ran2 = GameServer.random.nextInt(7);
                int ran3 = GameServer.random.nextInt(7);
                int ran4 = GameServer.random.nextInt(7);
                if (isSuperDragonBone) {
                    ran1 = 0;
                    ran2 = 0;
                    ran3 = 0;
                    ran4 = 0;
                    switch (GameServer.random.nextInt(4)) {
                        case 0:
                            ran1 = 6;
                            break;
                        case 1:
                            ran2 = 6;
                            break;
                        case 2:
                            ran3 = 6;
                            break;
                        case 3:
                            ran4 = 6;
                            break;
                    }
                }
                if (ran1 + ran2 + ran3 + ran4 == 6) {
                    pet.setHp(pet.getHp() + ran1);
                    pet.setMp(pet.getMp() + ran2);
                    pet.setAp(pet.getAp() + ran3);
                    pet.setSp(pet.getSp() + ran4);
                    if (ran1 != 0) {
                        four = DrawnitemsAction.Splice(four, "hp=" + ran1, 2);
                    }
                    if (ran2 != 0) {
                        four = DrawnitemsAction.Splice(four, "mp=" + ran2, 2);
                    }
                    if (ran3 != 0) {
                        four = DrawnitemsAction.Splice(four, "ap=" + ran3, 2);
                    }
                    if (ran4 != 0) {
                        four = DrawnitemsAction.Splice(four, "sp=" + ran4, 2);
                    }
                    pet.setFourattributes(four);

                    break;
                }
            }
        }
        if (isDragonSoul) {
            String four = pet.getFourattributes();
            int prop1 = 0, prop2 = 0, prop3 = 0, prop4 = 0;
            switch (GameServer.random.nextInt(4)) {
                case 0:
                    prop1 = 20;
                    break;
                case 1:
                    prop2 = 20;
                    break;
                case 2:
                    prop3 = 20;
                    break;
                case 3:
                    prop4 = 20;
                    break;
            }
            pet.setHp(pet.getHp() + prop1);
            pet.setMp(pet.getMp() + prop2);
            pet.setAp(pet.getAp() + prop3);
            pet.setSp(pet.getSp() + prop4);

            four = DrawnitemsAction.Splice(four, pet.getDragonSoul() + "php=" + prop1, 2);
            four = DrawnitemsAction.Splice(four, pet.getDragonSoul() + "pmp=" + prop2, 2);
            four = DrawnitemsAction.Splice(four, pet.getDragonSoul() + "pap=" + prop3, 2);
            four = DrawnitemsAction.Splice(four, pet.getDragonSoul() + "psp=" + prop4, 2);
            pet.setFourattributes(four);
        }
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("你的召唤兽#R" + pet.getSummoningname() + "#Y服用了一颗" + (isSuperDragonBone ? "超级龙之骨" : (isDragonBone ? "龙之骨" : (isDragonSpirit ? "龙之精" : "龙之魄"))));
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 使用超级龙之骨的方法
     */
    public void useKeelsp(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        if (good.getGoodsname().contains("龙精")) {
            if (pet.getLongjing() >= 10) {// 不超过2个
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("龙精数量已达到上限！"));
                return;
            }
        }
        if (good.getGoodsname().contains("龙魄")) {
            if (pet.getLongpo() >= 10) {// 不超过2个
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("龙魄数量已达到上限！"));
                return;
            }
        }
        if (good.getGoodsname().contains("超级龙之骨")) {
            ConcurrentHashMap<Integer, Configure> s = GameServer.getAllConfigure();
            Configure configure = s.get(1);

            if (configure != null) {
                if (pet.getSpdragon() >= Integer.parseInt(configure.getCjlzgsx())) {// 不超过
                    SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("超级龙之骨数量已达到上限！"));
                    return;
                }
            } else {
                if (pet.getSpdragon() >= 2) {// 不超过2个
                    SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("超级龙之骨数量已达到上限！"));
                    return;
                }
            }
        }
        if (pet.getPower() <= 4) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽飞升后才能使用！！"));
            return;
        }
        useGood(good, 1);
        boolean isCJ = good.getGoodsname().contains("龙魄") ? true : false;//是否超级龙之骨
        boolean isLJ = good.getGoodsname().contains("龙精") ? true : false;//是否超级龙之骨
        if (!isLJ) {
            pet.setGrowlevel(mathDouble(Double.parseDouble(pet.getGrowlevel()), 0.01).toString());// 成长率加0.01
        } else {
            pet.setGrowlevel(mathDouble(Double.parseDouble(pet.getGrowlevel()), 0.1).toString());// 成长率加0.01
        }
        while (true) {// 随机给这只召唤兽的hp、mp、ap、sp随机加6点
            String four = pet.getFourattributes();
            int ran1 = GameServer.random.nextInt(7);
            int ran2 = GameServer.random.nextInt(7);
            int ran3 = GameServer.random.nextInt(7);
            int ran4 = GameServer.random.nextInt(7);
            if (isCJ) {
                ran1 = 0;
                ran2 = 0;
                ran3 = 0;
                ran4 = 0;
                switch (GameServer.random.nextInt(4)) {
                    case 0:
                        ran1 = 10;
                        break;
                    case 1:
                        ran2 = 10;
                        break;
                    case 2:
                        ran3 = 10;
                        break;
                    case 3:
                        ran4 = 10;
                        break;
                }
            }
            if (ran1 + ran2 + ran3 + ran4 == 10) {
                pet.setHp(pet.getHp() + ran1);
                pet.setMp(pet.getMp() + ran2);
                pet.setAp(pet.getAp() + ran3);
                pet.setSp(pet.getSp() + ran4);
                if (ran1 != 0) {
                    four = DrawnitemsAction.Splice(four, "hps=" + ran1, 2);
                }
                if (ran2 != 0) {
                    four = DrawnitemsAction.Splice(four, "mps=" + ran2, 2);
                }
                if (ran3 != 0) {
                    four = DrawnitemsAction.Splice(four, "aps=" + ran3, 2);
                }
                if (ran4 != 0) {
                    four = DrawnitemsAction.Splice(four, "sps=" + ran4, 2);
                }
                pet.setFourattributes(four);
                break;
            }
        }
        if (!isLJ) {
            if (isCJ) {
                pet.setLongpo(pet.getLongpo() + 1);
            } else {
                pet.setSpdragon(pet.getSpdragon() + 1);// 超级龙骨数量加一
            }
        } else {
            pet.setLongjing(pet.getLongjing() + 1);
        }
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        assetUpdate.setPet(pet);
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 血魄
     */
    public void usexuepo(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {

		/*if (pet.getxuepo() >= 5) {// 不超过2个
			SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("血魄数量已达到上限！"));
			return;
		}*/
        if (pet.getPower() <= 4) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽飞升后才能使用！！"));
            return;
        }
        useGood(good, 1);
        //pet.setSpdragon(pet.getxuepo() + 1);
        String four = pet.getFourattributes();
        int rand = 100;
        pet.setHp(pet.getHp() + rand);
        four = DrawnitemsAction.Splice(four, "hps=" + rand, 2);
        pet.setFourattributes(four);
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        assetUpdate.setPet(pet);
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));

    }

    /**
     * 召唤兽直接变一转（一转之后就不能再吃了） -- 九转易筋丸
     */
    public void useNgauWanPills(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        if (pet.getGrade() > 100) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽已转生"));
            return;
        }
        useGood(good, 1);
        pet.setGrade(101);
        pet.setBone(0);
        pet.setSpir(0);
        pet.setPower(0);
        pet.setSpeed(0);
        pet.setCalm(0);
        pet.setExp(new BigDecimal(0));
        pet.setTurnRount(BattleMixDeal.petTurnRount(101));
        pet.setFriendliness(0L);
        pet.setFaithful(100);

        BigDecimal grow = mathDouble(Double.parseDouble(pet.getGrowlevel()), 0.1);
        pet.setGrowlevel(Arith.xiaoshu3(grow.doubleValue()));
        pet.setBasishp(0);
        pet.setBasismp(0);
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        assetUpdate.setPet(pet);
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 计算Double类型相加的算法
     */
    public static BigDecimal mathDouble(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.add(b2);
    }

    public void oepnqldan(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        int flag = 6;//固定数量9格
        if (pet.getOpenql() >= flag) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽的技能格子都已解开!"));
            return;
        }
        useGood(good, 1);
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        if (GameServer.random.nextInt(100) < 10) {//开启几率20%
            pet.setOpenql(pet.getOpenql() + 1);
            assetUpdate.updata("T格子");
            AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
            assetUpdate.setPet(pet);
            SuitMixdeal.jpd(login.getRolename(), pet.getSummoningname());
        } else {
            assetUpdate.setMsg("开启失败");
        }
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 打开技能封印的方法启魂丹
     */
    public void openqhSeal(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        int flag = 2;//固定数量9格
        if (pet.getOpenqh() >= flag) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽的启魂格子都已解开!"));
            return;
        }
        useGood(good, 1);
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        if (GameServer.random.nextInt(100) < 10) {//开启几率20%
            //if (GameServer.random.nextInt(5) == 3) {
            pet.setOpenqh(pet.getOpenqh() + 1);//召唤兽技能格子+1
            assetUpdate.updata("T格子");
            assetUpdate.setMsg("#W恭喜你，召唤兽#G" + pet.getSummoningname() + "#R解锁了#W一个新的技能格");
            AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
            assetUpdate.setPet(pet);
            SuitMixdeal.qhd(login.getRolename(), pet.getSummoningname());
        } else {
            assetUpdate.setMsg("#W很遗憾，召唤兽#G" + pet.getSummoningname() + "#W没能解锁该格子");
        }
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 打开技能封印的方法聚魄丹
     */
    public void openSkillSeal(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        int flag = 6;//固定数量9格

//        if (!pet.getSsn().equals("2") || !pet.getSsn().equals("3") || !pet.getSsn().equals("4")) {
//            if (pet.getOpenSeal()>=flag){
//                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽的技能格子都已解开!"));
//                return;
//            }
//        }
        //如果召唤兽技能大于或者等于 flag=9 ,会提示“召唤兽的技能格子都已解开”
        if (pet.getOpenSeal() >= flag && pet.getOpenSSskill() >= 1) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽的技能格子都已解开!"));
            return;
        }
        if (pet.getOpenSeal() >= flag) {
            if (pet.getSsn().equals("2") || pet.getSsn().equals("3") || pet.getSsn().equals("4")) {
                useGood(good, 1);
                AssetUpdate assetUpdate = new AssetUpdate();
                assetUpdate.setType(AssetUpdate.USEGOOD);
                assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
                if (GameServer.random.nextInt(100) < 10) {//开启几率20%
                    //if (GameServer.random.nextInt(5) == 3) {
                    pet.setOpenSSskill(pet.getOpenSSskill() + 1);//召唤兽技能格子+1
                    assetUpdate.updata("T格子");
                    assetUpdate.setMsg("#W恭喜你，召唤兽#G" + pet.getSummoningname() + "#R解封了#W一个新的技能格");
                    AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
                    assetUpdate.setPet(pet);
                    SuitMixdeal.jpd(login.getRolename(), pet.getSummoningname());
                } else {
                    assetUpdate.setMsg("#W很遗憾，召唤兽#G" + pet.getSummoningname() + "#W没能解封新技能格");
                }
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
                return;
            } else {
                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽的技能格子都已解开!"));
                return;
            }
        }
        useGood(good, 1);
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        if (GameServer.random.nextInt(100) < 10) {//开启几率20%
            //if (GameServer.random.nextInt(5) == 3) {
            pet.setOpenSeal(pet.getOpenSeal() + 1);//召唤兽技能格子+1
            assetUpdate.updata("T格子");
            assetUpdate.setMsg("#W恭喜你，召唤兽#G" + pet.getSummoningname() + "#R解封了#W一个新的技能格");
            AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
            assetUpdate.setPet(pet);
            SuitMixdeal.jpd(login.getRolename(), pet.getSummoningname());
        } else {
            assetUpdate.setMsg("#W很遗憾，召唤兽#G" + pet.getSummoningname() + "#W没能解封新技能格");
        }
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    public void openSkillSealSS(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        int flag = 8;//固定数量9格

//        if (pet.getSsn().equals("2") || pet.getSsn().equals("3") || pet.getSsn().equals("4")) {
//            flag = 9;
//        }
        int openqh = pet.getOpenqh();
        if (pet.getOpenSeal() == 6 && openqh < 1) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("该技能格未开启启魂!"));
            return;
        } else if (pet.getOpenSeal() == 7 && openqh < 2) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("该技能格未开启启魂!"));
            return;
        }
        //如果召唤兽技能大于或者等于 flag=9 ,会提示“召唤兽的技能格子都已解开”
        if (pet.getOpenSeal() >= flag) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽的技能格子都已解开!"));
            return;
        }

        useGood(good, 1);
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        if (GameServer.random.nextInt(100) < 5) {//开启几率15%
            pet.setOpenSeal(pet.getOpenSeal() + 1);//召唤兽技能格子+1
            assetUpdate.updata("T格子");
            assetUpdate.setMsg("#W恭喜你，召唤兽#G" + pet.getSummoningname() + "#R解封了#W一个新的技能格");
            AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
            assetUpdate.setPet(pet);
            SuitMixdeal.jpd(login.getRolename(), pet.getSummoningname());
        } else {
            assetUpdate.setMsg("#W很遗憾，召唤兽#G" + pet.getSummoningname() + "#W没能解封新技能格");
        }
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }


    /**
     * 添加召唤兽技能的方法
     */
    public static void addPetSkill(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        int sum = pet.getOpenSeal();
        Skill skill = null;
        if (good.getType() == 2326) {//聚魄丹使用技能  addpetqiling
            // 拆分技能名称
            String skillName = good.getValue().split("\\|")[0].split("=")[1];
            // 获取技能
            skill = GameServer.getSkill(skillName);
        } else {
            skill = skillid(good.getValue());
        }
        if (skill == null) {
            return;
        }
        List<String> skills = new ArrayList<>();
        if (pet.getPetSkills() != null && !pet.getPetSkills().equals("")) {
            String[] vs = pet.getPetSkills().split("\\|");
            for (int i = 0; i < vs.length; i++) {
                if (!vs[i].equals("")) {
                    skills.add(vs[i]);
                }
            }
        }

        if (sum <= skills.size() || skills.size() >= 8) {//召唤兽技能格子已经满了
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽技能格子已经满了"));
            return;
        }

        String value = chongfu(skill, pet, skills, true);
        if (value != null) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement(value));
            return;
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        skills.add(skill.getSkillid() + "");
        StringBuilder buffer = new StringBuilder();
        for (int i = 0; i < skills.size(); i++) {
            if (buffer.length() != 0) {
                buffer.append("|");
            }
            buffer.append(skills.get(i));
        }
        pet.setPetSkills(buffer.toString());
        getskills(skills, pet.getSkill());
        getskills(skills, pet.getBeastSkills());
        pet.setSkillData(skillData(skills));
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("你的召唤兽学会了" + skill.getSkillname());
        int Id = skill.getSkillid();
        if ((Id >= 1606 && Id <= 1608) || (Id >= 1828 && Id <= 1830) || (Id >= 1840 && Id <= 1842) || (Id >= 1867 && Id <= 1869) || Id == 3034) {//学习终极技能
            assetUpdate.updata("T悟技");
            SuitMixdeal.JN2(login.getRolename(), pet.getSummoningname(), skill.getSkillname(), "终级");
        } else if ((Id >= 1815 && Id <= 1827) || (Id >= 1600 && Id <= 1605)
                || (Id >= 1610 && Id <= 1612) || Id == 1811 || Id == 1831 || Id == 1833 || (Id >= 1871 && Id <= 1880)) {//学习高级技能
            assetUpdate.updata("T悟技");
            SuitMixdeal.JN2(login.getRolename(), pet.getSummoningname(), skill.getSkillname(), "高级");
        }
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    public static void addpetqiling(RoleSummoning pet, Goodstable good, ChannelHandlerContext ctx, LoginResult login) {
        int sum = pet.getOpenql();
        Skill skill = null;
        if (good.getType() == 2326) {//聚魄丹使用技能
            // 拆分技能名称
            String skillName = good.getValue().split("\\|")[0].split("=")[1];
            // 获取技能
            skill = GameServer.getSkill(skillName);
        } else {
            skill = skillid(good.getValue());
        }
        if (skill == null) {
            return;
        }
        List<String> skills = new ArrayList<>();
        if (pet.getPetQlSkills() != null && !pet.getPetQlSkills().equals("")) {
            String[] vs = pet.getPetQlSkills().split("\\|");
            for (int i = 0; i < vs.length; i++) {
                if (!vs[i].equals("")) {
                    skills.add(vs[i]);
                }
            }
        }

        if (sum <= skills.size() || skills.size() >= 6) {//召唤兽技能格子已经满了
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("召唤兽技能格子已经满了"));
            return;
        }

        String value = chongfu(skill, pet, skills, true);
        if (value != null) {
            SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement(value));
            return;
        }
        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        useGood(good, 1);
        assetUpdate.updata("G" + good.getRgid() + "=" + good.getUsetime());
        skills.add(skill.getSkillid() + "");
        StringBuilder buffer = new StringBuilder();
        for (int i = 0; i < skills.size(); i++) {
            if (buffer.length() != 0) {
                buffer.append("|");
            }
            buffer.append(skills.get(i));
        }
        pet.setPetQlSkills(buffer.toString());
        getskills(skills, pet.getSkill());
        getskills(skills, pet.getBeastSkills());
        pet.setSkillData(skillData(skills));
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);
        assetUpdate.setPet(pet);
        assetUpdate.setMsg("你的召唤兽学会了" + skill.getSkillname());
        int Id = skill.getSkillid();
        if ((Id >= 1606 && Id <= 1608) || (Id >= 1828 && Id <= 1830) || (Id >= 1840 && Id <= 1842) || (Id >= 1867 && Id <= 1869) || Id == 3034) {//学习终极技能
            assetUpdate.updata("T悟技");
            SuitMixdeal.JN(login.getRolename(), pet.getSummoningname(), skill.getSkillname(), "终级");
        } else if ((Id >= 1815 && Id <= 1827) || (Id >= 1600 && Id <= 1605)
                || (Id >= 1610 && Id <= 1612) || Id == 1811 || Id == 1831 || Id == 1833 || (Id >= 1871 && Id <= 1880)) {//学习高级技能
            assetUpdate.updata("T悟技");
            SuitMixdeal.JN(login.getRolename(), pet.getSummoningname(), skill.getSkillname(), "高级");
        }
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));
    }

    /**
     * 获取要学习的技能id
     */
    public static Skill skillid(String value) {
        String[] v = value.split("\\|");
        int up = 0;
        for (int i = 0; i < v.length; i += 2) {
            String jl = v[i].split("=")[1];
            double d = Double.parseDouble(jl);
            if (DropUtil.isV(d + up)) {
                v = v[i + 1].split("=")[1].split("&");
                return GameServer.getSkill(v[GameServer.random.nextInt(v.length)]);
            }
            up += d;
        }
        v = v[1].split("=")[1].split("&");
        return GameServer.getSkill(v[GameServer.random.nextInt(v.length)]);
    }

    /**
     * 判断是否可以学习该技能 true表示没学过
     */
    public static String chongfu(Skill skill, RoleSummoning pet, List<String> lists, boolean l) {
        String skillID = skill.getSkillid() + "";
        if (lists.contains(skillID)) {
            return "召唤兽已经学过" + skill.getSkillname();
        }
        if (skill.getSkillralation() != null && !skill.getSkillralation().equals("")) {
            int lvl = skill.getSkilllevel();
            String[] chongtu = skill.getSkillralation().split("\\|");
            for (int i = 0; i < chongtu.length; i++) {
                if (chongtu[i].equals(skillID)) {
                    continue;
                }
                if (lists.contains(chongtu[i])) {
                    Skill skill2 = GameServer.getGetSkill().get(chongtu[i]);
                    if (skill2 == null) {
                        continue;
                    }
                    if (skill2 != null) {
                        return "不能拥有同类型的技能";
                    }
//					int lvl2=skill2.getSkilllevel();
//					if (l) {
//						if (lvl<lvl2) {
//							return "不能拥有同类型的更高级技能";
//						}else {
//							lists.remove(chongtu[i]);
//						}
//					}else {
//						return "";
//					}
                }
            }
        }
        int id = skill.getSkillid();
        if (id == 1820) {// 金50
            if (Integer.parseInt(pet.getGold()) < 50 && !lists.contains("1815")) {
                return "你的召唤兽金属性不足50";
            }
        } else if (id == 1821) {// 木50
            if (Integer.parseInt(pet.getWood()) < 50 && !lists.contains("1816")) {
                return "你的召唤兽木属性不足50";
            }
        } else if (id == 1822) {// 土50
            if (Integer.parseInt(pet.getSoil()) < 50 && !lists.contains("1819")) {
                return "你的召唤兽土属性不足50";
            }
        } else if (id == 1823) {// 水50
            if (Integer.parseInt(pet.getWater()) < 50 && !lists.contains("1817")) {
                return "你的召唤兽水属性不足50";
            }
        } else if (id == 1824) {// 火50
            if (Integer.parseInt(pet.getFire()) < 50 && !lists.contains("1818")) {
                return "你的召唤兽火属性不足50";
            }
        } else if (id == 1825) {// 木50 根骨500
            if (pet.getBone() < 500) {
                return "你的召唤兽根骨属性不足500";
            }
            if (Integer.parseInt(pet.getWood()) < 50 && !lists.contains("1816")) {
                return "你的召唤兽木属性不足50";
            }
        } else if (id == 1826) {// 火50
            if (Integer.parseInt(pet.getFire()) < 50 && !lists.contains("1818")) {
                return "你的召唤兽火属性不足50";
            }
        } else if (id == 1827) {// 水50
            if (Integer.parseInt(pet.getWater()) < 50 && !lists.contains("1817")) {
                return "你的召唤兽水属性不足50";
            }
        } else if (id == 1246) {
            if (pet.getTurnRount() < 4) {
                return "你的召唤兽未飞升";
            }
            if (pet.getSpir() < 500) {
                return "你的召唤兽灵性不足500";
            }
        } else if (id == 1247) {//
            if (pet.getTurnRount() < 4) {
                return "你的召唤兽未飞升";
            }
            if (pet.getBone() < 500) {
                return "你的召唤兽根骨不足500";
            }
        } else if (id == 1248) {//
            if (pet.getTurnRount() < 4) {
                return "你的召唤兽未飞升";
            }
            if (pet.getPower() < 500) {
                return "你的召唤兽力量不足500";
            }
        } else if (id == 1249) {//
            if (pet.getTurnRount() < 4) {
                return "你的召唤兽未飞升";
            }
            if (pet.getSpeed() < 500) {
                return "你的召唤兽敏捷不足500";
            }
        }
        if (pet.getPetSkillswl() == null || pet.getPetSkillswl().equals("")) {
        } else {
            if (id == 1600) {
                if (pet.getPetSkillswl().contains("1602") || pet.getPetSkillswl().contains("1603") || pet.getPetSkillswl().contains("1604") || pet.getPetSkillswl().contains("1605") || pet.getPetSkillswl().contains("1601")) {
                    return "已开启已开启悟灵技能替换失败";
                }
            } else if (id == 1601) {
                if (pet.getPetSkillswl().contains("1600") || pet.getPetSkillswl().contains("1602") || pet.getPetSkillswl().contains("1603") || pet.getPetSkillswl().contains("1604") || pet.getPetSkillswl().contains("1605")) {
                    return "已开启已开启悟灵技能替换失败";
                }
            } else if (id == 1602) {
                if (pet.getPetSkillswl().contains("1600") || pet.getPetSkillswl().contains("1603") || pet.getPetSkillswl().contains("1604") || pet.getPetSkillswl().contains("1605") || pet.getPetSkillswl().contains("1601")) {
                    return "已开启已开启悟灵技能替换失败";
                }
            } else if (id == 1603) {
                if (pet.getPetSkillswl().contains("1600") || pet.getPetSkillswl().contains("1602") || pet.getPetSkillswl().contains("1604") || pet.getPetSkillswl().contains("1605") || pet.getPetSkillswl().contains("1601")) {
                    return "已开启已开启悟灵技能替换失败";
                }
            } else if (id == 1604) {
                if (pet.getPetSkillswl().contains("1600") || pet.getPetSkillswl().contains("1602") || pet.getPetSkillswl().contains("1603") || pet.getPetSkillswl().contains("1605") || pet.getPetSkillswl().contains("1601")) {
                    return "已开启已开启悟灵技能替换失败";
                }
            } else if (id == 1605) {
                if (pet.getPetSkillswl().contains("1600") || pet.getPetSkillswl().contains("1602") || pet.getPetSkillswl().contains("1603") || pet.getPetSkillswl().contains("1604") || pet.getPetSkillswl().contains("1601")) {
                    return "已开启已开启悟灵技能替换失败";
                }
            } else if (id == 1611) {
                if (pet.getPetSkillswl().indexOf(id) != -1) {
                    return "已开启悟灵技能替换失败";
                }
            } else if (id == 1612) {
                if (pet.getPetSkillswl().indexOf(id) != -1) {
                    return "已开启悟灵技能替换失败";
                }
            } else if (id == 1831) {
                if (pet.getPetSkillswl().contains("1831") || pet.getPetSkillswl().contains("1833")) {
                    return "已开启悟灵技能替换失败";
                }
            } else if (id == 1834) {
                if (pet.getPetSkillswl().contains("1834") || pet.getPetSkillswl().contains("1836")) {
                    return "已开启悟灵技能替换失败";
                }
            } else if (id == 1835) {
                if (pet.getPetSkillswl().indexOf(id) != -1) {
                    return "已开启悟灵技能替换失败";
                }
            } else if (id == 1836) {
                if (pet.getPetSkillswl().contains("1834") || pet.getPetSkillswl().contains("1836")) {
                    return "已开启悟灵技能替换失败";
                }
            } else if (id == 1833) {
                if (pet.getPetSkillswl().contains("1831") || pet.getPetSkillswl().contains("1833")) {
                    return "已开启悟灵技能替换失败";
                }
            } else if (id == 1871) {
                if (pet.getPetSkillswl().indexOf(id) != -1) {
                    return "已开启悟灵技能替换失败";
                }
            } else if (id == 1872) {
                if (pet.getPetSkillswl().indexOf(id) != -1) {
                    return "已开启悟灵技能替换失败";
                }
            } else if (id == 1880) {
                if (pet.getPetSkillswl().indexOf(id) != -1) {
                    return "已开启悟灵技能替换失败";
                }
            } else if (id == 1838) {
                if (pet.getPetSkillswl().indexOf(id) != -1) {
                    return "已开启悟灵技能替换失败";
                }
            }

        }
        return null;
    }

    /**
     * 召唤兽技能刷新数据
     */
    public static String skillData(List<String> skills) {
        // 获取所有的技能id
        String skilldata = null;
        for (int i = 0; i < skills.size(); i++) {
            switch (skills.get(i)) {
                case "1800":
                case "1825":
                    skilldata = DrawnitemsAction.Splice(skilldata, "HP=27000", 2);
                    break;
                case "1801":
                    skilldata = DrawnitemsAction.Splice(skilldata, "MP=27000", 2);
                    break;
                case "1802":
                case "1826":
                    skilldata = DrawnitemsAction.Splice(skilldata, "AP=11000", 2);
                    break;
                case "1803":
                case "1827":
                    skilldata = DrawnitemsAction.Splice(skilldata, "SP=170", 2);
                    break;
                case "1812":
                    skilldata = DrawnitemsAction.Splice(skilldata, "SP=-170", 2);
                    break;
                case "1814":
                    skilldata = DrawnitemsAction.Splice(skilldata, "SP=250", 2);
                    break;
                case "1882":
                    skilldata = DrawnitemsAction.Splice(skilldata, "HP=32000", 2);
                    break;
                case "1883":
                    skilldata = DrawnitemsAction.Splice(skilldata, "MP=32000", 2);
                    break;
                case "1884":
                    skilldata = DrawnitemsAction.Splice(skilldata, "AP=15000", 2);
                    break;
                case "1885":
                    skilldata = DrawnitemsAction.Splice(skilldata, "SP=200", 2);
                    break;

                default:
                    break;
            }
        }
        return skilldata;
    }

    /**
     * 拼接技能
     */
    public static void getskills(List<String> skills, String petskill) {
        if (petskill == null || petskill.equals(""))
            return;
        String[] v = petskill.split("\\|");
        for (int i = 0; i < v.length; i++) {
            skills.add(v[i]);
        }
    }

    /**
     * 初始增加选项
     */
    public void XXPet(ChannelHandlerContext ctx, LoginResult login, String[] vs) {
        long id = Long.parseLong(vs[1]);
        NPCDialogBean bean = maps.remove(id);
        if (bean == null) {
            return;
        }
        RoleSummoning pet = AllServiceUtil.getRoleSummoningService().selectRoleSummoningsByRgID(bean.getOId());
        if (pet == null) {
            return;
        }
        if (pet.getRoleid().compareTo(login.getRole_id()) != 0) {
            return;
        }
        int type = Integer.parseInt(vs[2]);
        if (type == 0) {
            pet.setHp(pet.getHp() + bean.getValue());
        } else if (type == 1) {
            pet.setMp(pet.getMp() + bean.getValue());
        } else if (type == 2) {
            pet.setAp(pet.getAp() + bean.getValue());
        } else if (type == 3) {
            pet.setSp(pet.getSp() + bean.getValue());
        }

        pet.setBasishp(0);
        pet.setBasismp(0);
        AllServiceUtil.getRoleSummoningService().updatePetRedis(pet);

        AssetUpdate assetUpdate = new AssetUpdate();
        assetUpdate.setType(AssetUpdate.USEGOOD);
        assetUpdate.setPet(pet);
        SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().assetAgreement(GsonUtil.getGsonUtil().getgson().toJson(assetUpdate)));

    }

    public static void useGood(Goodstable good, int sum) {
        good.goodxh(sum);//添加记录
        AllServiceUtil.getGoodsrecordService().insert(good, null, 1, 9);
        AllServiceUtil.getGoodsTableService().updateGoodRedis(good);
    }

    //判断变化的是哪一只召唤兽的方法
    public static void otherPetId(RoleSummoning pet, int flag) {
        if (flag == 0) {
            if (pet.getSummoningid().equals("200102")) {
                pet.setSummoningskin("400078");
                pet.setGold("0");
                pet.setWood("0");
                pet.setSoil("5");
                pet.setWater("0");
                pet.setFire("95");
            } else if (pet.getSummoningid().equals("200103")) {
                pet.setSummoningskin("400080");
                pet.setGold("40");
                pet.setWood("0");
                pet.setSoil("0");
                pet.setWater("0");
                pet.setFire("60");
            } else if (pet.getSummoningid().equals("200104")) {
                pet.setSummoningskin("400083");
                pet.setGold("0");
                pet.setWood("60");
                pet.setSoil("40");
                pet.setWater("0");
                pet.setFire("0");
            } else if (pet.getSummoningid().equals("200105")) {
                pet.setSummoningskin("400072");
                pet.setGold("5");
                pet.setWood("0");
                pet.setSoil("0");
                pet.setWater("95");
                pet.setFire("0");
            } else if (pet.getSummoningid().equals("200106")) {
                pet.setSummoningskin("400079");
                pet.setGold("0");
                pet.setWood("60");
                pet.setSoil("0");
                pet.setWater("40");
                pet.setFire("0");
            } else if (pet.getSummoningid().equals("200107")) {
                pet.setSummoningskin("400064");
                pet.setGold("0");
                pet.setWood("0");
                pet.setSoil("15");
                pet.setWater("85");
                pet.setFire("0");
            }
        } else if (flag == 1) {
            if (pet.getSummoningid().equals("200102")) {
                pet.setGold("0");
                pet.setWood("0");
                pet.setSoil("30");
                pet.setWater("70");
                pet.setFire("0");
            } else if (pet.getSummoningid().equals("200103")) {
                pet.setGold("0");
                pet.setWood("70");
                pet.setSoil("30");
                pet.setWater("0");
                pet.setFire("0");
            } else if (pet.getSummoningid().equals("200104")) {
                pet.setGold("25");
                pet.setWood("0");
                pet.setSoil("0");
                pet.setWater("0");
                pet.setFire("75");
            } else if (pet.getSummoningid().equals("200105")) {
                pet.setGold("0");
                pet.setWood("0");
                pet.setSoil("30");
                pet.setWater("70");
                pet.setFire("0");
            } else if (pet.getSummoningid().equals("200106")) {
                pet.setGold("75");
                pet.setWood("0");
                pet.setSoil("0");
                pet.setWater("25");
                pet.setFire("0");
            } else if (pet.getSummoningid().equals("200107")) {
                pet.setGold("40");
                pet.setWood("0");
                pet.setSoil("0");
                pet.setWater("60");
                pet.setFire("0");
            }
        }
    }

    public XyConditionDto parseCondition(String condition, RoleSummoning roleSummoning) {
        condition = condition.replaceAll("\r\n", "");
        XyConditionDto xyConditionDto = new XyConditionDto();
        xyConditionDto.setB(false);

        if (condition.startsWith("等级")) {
            int lvl = Integer.parseInt(condition.split("=")[1]);
            if (roleSummoning.getGrade() >= lvl) {
                xyConditionDto.setB(true);
            }
            xyConditionDto.setText("等级达到" + AnalysisString.petLvl(lvl) + "级");
        } else if (condition.startsWith("亲密")) {
            int qm = Integer.parseInt(condition.split("=")[1]);
            if (roleSummoning.getFriendliness() >= qm) {
                xyConditionDto.setB(true);
            }
            xyConditionDto.setText("亲密达到" + (condition.split("=")[1].length() > 4 ? condition.substring(condition.split("=")[1].length() - 4, condition.split("=")[1].length()) + "万" : qm + ""));
        } else if (condition.startsWith("格子")) {
            int gz = Integer.parseInt(condition.split("=")[1]);
            if (roleSummoning.getOpenSeal() >= gz) {
                xyConditionDto.setB(true);
            }
            xyConditionDto.setText("开启" + gz + "个技能格");
        } else if (condition.startsWith("高级技能")) {
            int cout = 0;
            int jn = Integer.parseInt(condition.split("=")[1]);
            String petSkills = roleSummoning.getPetSkills();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(petSkills)) {
                String[] split = petSkills.split("\\|");
                for (Integer skill : highSkill) {
                    for (String s : split) {
                        if (s.equals(skill.toString())) {
                            cout += 1;
                        }
                    }
                }
            }
            if (cout >= jn) {
                xyConditionDto.setB(true);
            }
            xyConditionDto.setText("携带" + jn + "个高级技能");
        } else if (condition.startsWith("终极技能")) {
            int cout = 0;
            int jn = Integer.parseInt(condition.split("=")[1]);
            String petSkills = roleSummoning.getPetSkills();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(petSkills)) {
                String[] split = petSkills.split("\\|");
                for (Integer skill : zhongjiskill) {
                    for (String s : split) {
                        if (s.equals(skill.toString())) {
                            cout += 1;
                        }
                    }
                }
            }
            if (cout >= jn) {
                xyConditionDto.setB(true);
            }
//            xyConditionDto.setText("携带1个终极技能");
            xyConditionDto.setText("携带" + jn + "个终极技能");
        } else if (condition.startsWith("龙骨")) {
            int lg = Integer.parseInt(condition.split("=")[1]);
            if (roleSummoning.getDragon() >= lg) {
                xyConditionDto.setB(true);
            }
            xyConditionDto.setText("使用" + lg + "个龙之骨");
        } else if (condition.startsWith("超级龙之骨")) {
            int lg = Integer.parseInt(condition.split("=")[1]);
            if (roleSummoning.getSpdragon() >= lg) {
                xyConditionDto.setB(true);
            }
            xyConditionDto.setText("使用" + lg + "个超级龙之骨");
        } else if (condition.startsWith("龙之精")) {
            int lg = Integer.parseInt(condition.split("=")[1]);
            if (roleSummoning.getDragonSpirit() >= lg) {
                xyConditionDto.setB(true);
            }
            xyConditionDto.setText("使用" + lg + "个龙之精");
        } else if (condition.startsWith("龙之魄")) {
            int lg = Integer.parseInt(condition.split("=")[1]);
            if (roleSummoning.getDragonSoul() >= lg) {
                xyConditionDto.setB(true);
            }
            xyConditionDto.setText("使用" + lg + "个龙之魄");
        }
        return xyConditionDto;
    }
}
