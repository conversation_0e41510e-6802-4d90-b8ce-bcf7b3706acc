package com.gl.model;

/**
 * 前端请求参数封装
 *
 * <AUTHOR>
 */
public class UpPetParam {

    private String sid;
    private int grade;
    private int turnRount;
    private long friendliness;
    private int openSeal;
    private Integer lx;
    private String skill;
    private String nds;
    private String growlevel;
    private String jineng;
    private int flyupNum;
    private int czjjd;
    private int dragon;
    private int spdragon;
    private int OtherPoint;
    private int openql;
    private String qljineng;

    public String getQljineng() {
        return qljineng;
    }

    public void setQljineng(String qljineng) {
        this.qljineng = qljineng;
    }

    public int getOpenql() {
        return openql;
    }

    public void setOpenql(int openql) {
        this.openql = openql;
    }

    public int getFlyupNum() {
        return flyupNum;
    }

    public void setFlyupNum(int flyupNum) {
        this.flyupNum = flyupNum;
    }

    public int getCzjjd() {
        return czjjd;
    }

    public void setCzjjd(int czjjd) {
        this.czjjd = czjjd;
    }

    public int getDragon() {
        return dragon;
    }

    public void setDragon(int dragon) {
        this.dragon = dragon;
    }

    public int getSpdragon() {
        return spdragon;
    }

    public void setSpdragon(int spdragon) {
        this.spdragon = spdragon;
    }

    public int getOtherPoint() {
        return OtherPoint;
    }

    public void setOtherPoint(int otherPoint) {
        OtherPoint = otherPoint;
    }

    public String getJineng() {
        return jineng;
    }

    public void setJineng(String jineng) {
        this.jineng = jineng;
    }

    public String getGrowlevel() {
        return growlevel;
    }

    public void setGrowlevel(String growlevel) {
        this.growlevel = growlevel;
    }
    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public int getGrade() {
        return grade;
    }

    public void setGrade(int grade) {
        this.grade = grade;
    }

    public int getTurnRount() {
        return turnRount;
    }

    public void setTurnRount(int turnRount) {
        this.turnRount = turnRount;
    }

    public long getFriendliness() {
        return friendliness;
    }

    public void setFriendliness(long friendliness) {
        this.friendliness = friendliness;
    }

    public int getOpenSeal() {
        return openSeal;
    }

    public void setOpenSeal(int openSeal) {
        this.openSeal = openSeal;
    }

    public Integer getLx() {
        return lx;
    }

    public void setLx(Integer lx) {
        this.lx = lx;
    }

    public String getSkill() {
        return skill;
    }

    public void setSkill(String skill) {
        this.skill = skill;
    }

    public String getNds() {
        return nds;
    }

    public void setNds(String nds) {
        this.nds = nds;
    }
}


