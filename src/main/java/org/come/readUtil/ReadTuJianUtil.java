package org.come.readUtil;

import org.come.handler.MainServerHandler;
import org.come.model.TuJian;
import org.come.model.TuJianAll;
import org.come.model.WitchComposeAttr;
import org.come.servlet.UpXlsAndTxtFile;
import org.come.tool.ReadExelTool;
import org.come.tool.SettModelMemberTool;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class ReadTuJianUtil {
    public static TuJianAll getAllData(String path, StringBuffer buffer) {
        TuJianAll tuJianAll = new TuJianAll();
        String[][] result = ReadExelTool.getResult("config/"+path+".xls");
        List<TuJian> tuJianList = new ArrayList<>();
        for (int i = 1; i < result.length; i++) {
            if (result[i][0].equals("")) {continue;}

            TuJian tuJian = new TuJian();
            for (int j = 0; j < result[i].length; j++) {
                try {
                    SettModelMemberTool.setReflectRelative(tuJian, result[i][j], j);
                } catch (Exception e) {
                    UpXlsAndTxtFile.addStringBufferMessage(buffer, i, j, result[i][j], MainServerHandler.getErrorMessage(e));
                    return null;
                }
            }
            tuJianList.add(tuJian);
        }
        tuJianAll.setTuJians(tuJianList);
        return tuJianAll;
    }
}
