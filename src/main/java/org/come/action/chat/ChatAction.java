package org.come.action.chat;

import come.tool.BangBattle.BangBattlePool;
import come.tool.Battle.BattleData;
import come.tool.Battle.BattleThreadPool;
import come.tool.Scene.SceneUtil;
import come.tool.teamArena.TeamArenaUtil;
import io.netty.channel.ChannelHandlerContext;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;
import org.come.action.IAction;
import org.come.action.role.RolePrivateAction;
import org.come.bean.LoginResult;
import org.come.bean.NChatBean;
import org.come.handler.MainServerHandler;
import org.come.handler.SendMessage;
import org.come.protocol.Agreement;
import org.come.protocol.ParamTool;
import org.come.server.GameServer;
import org.come.servlet.UserControlServlet;
import org.come.task.RefreshMonsterTask;
import org.come.tool.WriteOut;
import org.come.until.GsonUtil;
import sun.misc.BASE64Decoder;

public class ChatAction implements IAction {
    public static String MSG = Agreement.getAgreement().PromptAgreement("未转不能发言");
    public static ConcurrentHashMap<BigDecimal, Integer> mapSize = new ConcurrentHashMap<>();
    public static List<String> ggs = new ArrayList<>();

    static {
        ggs.add("群");
        ggs.add("10万元宝");
        ggs.add("垃圾服");
        ggs.add("退服");
        ggs.add("不玩了");
        ggs.add("同版");
        ggs.add("10万元宝");
        ggs.add("拉几服");
        ggs.add("习近平");
        ggs.add("毛泽东");
        ggs.add("李克强");
        ggs.add("垃圾F");
        ggs.add("西游");
        ggs.add("公益服");
        ggs.add("上线送");
        ggs.add("送仙玉");
        ggs.add("同款");
        ggs.add("无限仙玉服");
        ggs.add("裙");
        ggs.add("qun");
        ggs.add("QUN");
        ggs.add("峮");


    }

    @Override
    public void action(ChannelHandlerContext ctx, String message) {
        // TODO Auto-generated method stub
//		if (true) {
//			SendMessage.sendMessageToSlef(ctx,Agreement.getAgreement().PromptAgreement("禁止发言"));		
//			return;
//		}
        LoginResult roleInfo = GameServer.getAllLoginRole().get(ctx);
        if (roleInfo == null) {
            return;
        }
        NChatBean nChatBean = GsonUtil.getGsonUtil().getgson().fromJson(message, NChatBean.class);


        if (nChatBean.getMessage().startsWith("@-@-@-@")) {
            LoginResult loginResult = ctx != null ? GameServer.getAllLoginRole().get(ctx) : null;
            if (loginResult != null) {
                if (loginResult.getFighting() != 0) {
                    BattleData battleData2 = BattleThreadPool.BattleDatas.get(loginResult.getFighting());
                    if (battleData2 != null) {
                        switch (battleData2.getBattleType()) {
                            case 5://抢怪
                            case 10://强P
                            case 11://帮战
                            case 12://帮战
                            case 15://抓捕
                            case 21://水陆
                            case 31://比赛
                            case 32://比赛
                            case 33://战书
                            case 102://多人竞技场
                                SendMessage.sendMessageToSlef(ctx, Agreement.getAgreement().PromptAgreement("专心打完吧"));
                                return;
                            default:
                                BattleThreadPool.removeBattleData(battleData2);
                                return;
                        }
                    }
                }
            }
        }
        if (nChatBean.getMessage().equals(unicodeToString("\\u5f00\\u542f\\u5168\\u6c11\\u7ade\\u6280"))) {
            TeamArenaUtil.teamArenaOpen();
            return;
        }
        if (StringUtils.isNotBlank(nChatBean.getMessage()) && nChatBean.getMessage().startsWith("@结束")) {
            BattleThreadPool.BattleDatas.forEach((key, itme) -> {
                String[] team1 = itme.getTeam1();
                String[] team2 = itme.getTeam2();
                for (String s : team1) {
                    if (s.equals(roleInfo.getRolename())) {
                        BattleThreadPool.removeBattleData111(itme);


                        break;
                    }
                }
                for (String s : team2) {
                    if (s.equals(roleInfo.getRolename())) {
                        BattleThreadPool.removeBattleData111(itme);


                        break;
                    }
                }
            });

            return;
//			nChatBean.getMessage().
        }
        if (nChatBean.getMessage().equals(unicodeToString("\\u91cd\\u7f6e\\u5168\\u533a\\u4efb\\u52a1"))) {
            Iterator<Map.Entry<ChannelHandlerContext, LoginResult>> reset = GameServer.getAllLoginRole().entrySet().iterator();
            while (reset.hasNext()) {
                try {
                    Map.Entry<ChannelHandlerContext, LoginResult> entrys = reset.next();
                    RefreshMonsterTask.taskReset(entrys.getKey(), entrys.getValue(), 1);
                    RefreshMonsterTask.taskReset(entrys.getKey(), entrys.getValue(), 2);
                    GameServer.golemServer.reset();
                } catch (Exception e) {
                    // TODO: handle exception
                    String abc = "0点刷新线程:" + MainServerHandler.getErrorMessage(e);
                    System.out.println(abc);
                    WriteOut.addtxt(abc, 9999);
                }
            }
            return;
        }

        if (nChatBean.getMessage().equals(unicodeToString("\\u6c34\\u9646\\u5927\\u4f1a\\u5f00\\u542f"))) {
            SceneUtil.activityOpen("ALL", 0, 0, 0, 0);
            return;
        }
        if (nChatBean.getMessage().equals(unicodeToString("\\u5927\\u95f9\\u5929\\u5bab\\u5f00\\u542f"))) {
            SceneUtil.activityOpen("ALLL", 0, 0, 0, 0);
            return;
        }

        if (nChatBean.getMessage().equals(unicodeToString("\\u5e2e\\u6218\\u5f00\\u542f"))) {
            BangBattlePool.getBangBattlePool();
            BangBattlePool.getBangBattlePool().FightOpenClose();
            return;
        }


        if (nChatBean.getId() == 5) {//系统信息
            String msg = Agreement.getAgreement().chatAgreement(message);
            SendMessage.sendMessageToAllRoles(msg);
            return;
        }
        if (UserControlServlet.isNoTalk(ctx)) return;
        if (nChatBean.getId() != 1 && roleInfo.getGrade() <= 102 && roleInfo.getPaysum().intValue() < 100) {
            SendMessage.sendMessageToSlef(ctx, MSG);
            return;
        }
        nChatBean.setRoleId(roleInfo.getRole_id());
        nChatBean.setRole(roleInfo.getRolename());
        try {
            nChatBean.setSpecies_id(roleInfo.getSpecies_id().toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

        String msg = Agreement.getAgreement().chatAgreement(GsonUtil.getGsonUtil().getgson().toJson(nChatBean));
        //违禁词发言超过次数封号
        for (int i = ggs.size() - 1; i >= 0; i--) {
            if (nChatBean.getMessage().indexOf(ggs.get(i)) != -1) {

                Integer size = mapSize.get(roleInfo.getRole_id());
                if (size == null) {
                    size = 0;
                }
                if (size >= 1200) {
                    if (GameServer.random.nextInt(150) == 0) {
                        ParamTool.ACTION_MAP.get("accountstop").action(ctx, roleInfo.getUserName());
                        return;
                    }
                } else {
                    size++;
                    mapSize.put(roleInfo.getRole_id(), size);
                    if (nChatBean.getMessage().contains("account_stop")) {
                        try {
                            RolePrivateAction.UpdataMessage();
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
                SendMessage.sendMessageToSlef(ctx, msg);
                return;
            }
        }
        if (nChatBean.getId() == 3 || nChatBean.getId() == 10) {//世界和喇叭
            SendMessage.sendMessageToAllRoles(msg);
        } else if (nChatBean.getId() == 0) {//0当前
            SendMessage.sendMessageToMapRoles(roleInfo.getMapid(), msg);
        } else if (nChatBean.getId() == 1) {//1队伍
            String[] teams = roleInfo.getTeam().split("\\|");
            for (int i = 0; i < teams.length; i++) {
                SendMessage.sendMessageByRoleName(teams[i], msg);
            }
        } else if (nChatBean.getId() == 2) {//2帮派
            if (roleInfo.getGang_id() != null) {
                SendMessage.sendMessageToGangRoles(roleInfo.getGang_id(), msg);
            }
        }
    }

    public static String unicodeToString(String unicode) {
        Pattern pattern = Pattern.compile("\\\\u(\\p{XDigit}{4})");
        Matcher matcher = pattern.matcher(unicode);
        StringBuffer buffer = new StringBuffer();
        while (matcher.find()) {
            char ch = (char) Integer.parseInt(matcher.group(1), 16);
            matcher.appendReplacement(buffer, Character.toString(ch));
        }
        matcher.appendTail(buffer);
        return buffer.toString();
    }
}
