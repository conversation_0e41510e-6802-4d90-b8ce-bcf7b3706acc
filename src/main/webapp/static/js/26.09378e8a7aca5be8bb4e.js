webpackJsonp([26], {
  O1Xo: function(e, t, l) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
      value: !0
    });
    var o = l("woOf"),
        i = l.n(o),
        a = {
          name: "PlayerInfo",
          data: function() {
            return {
              total: 0,
              page: 1,
              size: 10,
              hide: !1,
              online: !0,
              logining: !1,
              formFileds: {
                rolename: "",
                activity: "",
                role_id: "",
                user_id: "",
                localname: "",
                password: "",
                money: "",
                type: "",
                xianyu: "",
                draw: "",
                unknown: "",
                statues: "",
                goodsid: "",
                sum: 1
              },
              rules: {
                password: [{
                  required: !0,
                  message: "解锁密码不能为空",
                  trigger: "blur"
                }, {
                  min: 6,
                  max: 16,
                  message: "长度在 6 到 16 个字符",
                  trigger: "blur"
                }],
                type: [{
                  required: !0,
                  message: "请选择充值类型"
                }],
                money: [{
                  required: !0,
                  message: "请输入充值金额",
                  trigger: "blur"
                }, {
                  type: "number",
                  message: "充值金额必须为数字"
                }],
                xianyu: [{
                  required: !0,
                  message: "请输入仙玉数量",
                  trigger: "blur"
                }, {
                  type: "number",
                  message: "仙玉数量必须为数字"
                }],
                goodsid: [{
                  required: !0,
                  message: "请选择物品",
                  trigger: "blur"
                }],
                sum: [{
                  required: !0,
                  message: "请输入物品数量",
                  trigger: "blur"
                }, {
                  type: "number",
                  message: "物品数量必须为数字"
                }]
              },
              tableData: [],
              isShowDeleteDialog: !1,
              isShowUpdatePwdDialog: !1,
              isShowUpdateGMDialog: !1,
              isShowUpdateMountDialog: !1,
              isShowEditDialog: !1,
              isShowRechargeDialog: !1,
              isShowControlDialog: !1,
              isShowSendGoodsDialog: !1,
              options: [{
                value: "1",
                label: "仙玉充值"
              }, {
                value: "2",
                label: "VIP特权"
              }, {
                value: "3",
                label: "小资礼包"
              }, {
                value: "4",
                label: "土豪礼包"
              }],
              goodsOptions: [],
              searchValue: "",
              searchType: "1"
            }
          },
          filters: {
            activityStatusName: function(e) {
              return 0 === e ? '<span style="color:green">正常</div>' : 1 === e ?
                  '<span style="color:red">封停</div>' : void 0
            },
            formatDate: function(e) {
              var t = new Date(e),
                  l = t.getFullYear(),
                  o = t.getMonth() + 1;
              o = o < 10 ? "0" + o : o;
              var i = t.getDate();
              i = i < 10 ? "0" + i : i;
              var a = t.getHours();
              a = a < 10 ? "0" + a : a;
              var s = t.getMinutes();
              s = s < 10 ? "0" + s : s;
              var r = t.getSeconds();
              return r = r < 10 ? "0" + r : r, l + "-" + o + "-" + i + " " + a + ":" + s
            }
          },
          created: function() {
            var e = this;
            this.getPageData(), this.$http.get("/api/goods").then(function(t) {
              if (200 == t.data.code)
                for (var l in t.data.data) e.goodsOptions.push({
                  label: l,
                  value: t.data.data[l]
                });
              else e.$alert("数据请求失败", "提示", {
                confirmButtonText: "ok"
              })
            }).catch(function(e) {
              console.log(e)
            })
          },
          methods: {
            setSize: function(e) {
              this.size = e, this.getPageData()
            },
            setPage: function(e) {
              this.page = e, this.getPageData()
            },
            getPageData: function() {
              var e = this,
                  t = new FormData;
              t.append("PageNum", this.page), t.append("PageSize", this.size), t.append("Value1", this.searchType ?
                  this.searchType : ""), t.append("Value2", this.searchValue ? this.searchValue : ""), this.$http
                  .post("/api/role", t).then(function(t) {
                    200 == t.data.code ? (e.tableData = t.data.data.list, e.total = t.data.data.total) : e.$alert(
                        "数据请求失败", "提示", {
                          confirmButtonText: "ok"
                        })
                  }).catch(function(e) {
                    console.log(e)
                  })
            },
            handleEditPwd: function() {
              var e = this;
              this.$refs.editPwdForm.validate(function(t) {
                if (t) {
                  e.logining = !0;
                  var l = new FormData;
                  l.append("Value1", e.formFileds.role_id), l.append("Value2", e.formFileds.password), e.$http
                      .post("/api/lockpwd", l).then(function(t) {
                        200 == t.data.code ? (e.isShowEditDialog = !1, e.$message.success("解锁密码修改成功")) : e.$alert(
                            "解锁密码修改失败", "提示", {
                              confirmButtonText: "ok"
                            }), e.logining = !1
                      }).catch(function(t) {
                        e.logining = !1, console.log(t)
                      })
                }
              })
            },

            //删除接口
            handleDelete: function() {
              var e = this;
              this.$refs.deleteForm.validate(function(t) {
                if (t) {
                  e.logining = !0;
                  var l = new FormData;
                  l.append("Value1", e.formFileds.role_id), l.append("Value2", e.formFileds.password), e.$http
                      .post("/api/deleterole", l).then(function(t) {
                        200 == t.data.code ? (e.isShowDeleteDialog = !1, e.$message.success("删除成功")) : e.$alert(
                            "删除失败", "提示", {
                              confirmButtonText: "ok"
                            }), e.logining = !1
                      }).catch(function(t) {
                        e.logining = !1, console.log(t)
                      })
                }
              })
            },
            //修改密码
            handleUpdatePwd: function() {
              var e = this;
              this.$refs.updatePwdForm.validate(function(t) {
                if (t) {
                  e.logining = !0;
                  var l = new FormData;
                  l.append("Value1", e.formFileds.localname), l.append("Value2", e.formFileds.password), e.$http
                      .post("/api/updatePwdUser", l).then(function(t) {
                        200 == t.data.code ? (e.isShowUpdatePwdDialog = !1, e.$message.success("修改成功")) : e.$alert(
                            "修改失败", "提示", {
                              confirmButtonText: "ok"
                            }), e.logining = !1
                      }).catch(function(t) {
                        e.logining = !1, console.log(t)
                      })
                }
              })
            },
            //设置GM特权
            handleUpdateGM: function() {
              var e = this;
              this.$refs.updateGMForm.validate(function(t) {
                if (t) {
                  e.logining = !0;
                  var l = new FormData;
                  l.append("Value1", e.formFileds.role_id), l.append("Value2", e.formFileds.gmtype), e.$http
                      .post("/api/updateGMRole", l).then(function(t) {
                        200 == t.data.code ? (e.isShowUpdateGMDialog = !1, e.$message.success("设置成功")) : e.$alert(
                            "设置失败", "提示", {
                              confirmButtonText: "ok"
                            }), e.logining = !1
                      }).catch(function(t) {
                        e.logining = !1, console.log(t)
                      })
                }
              })
            },
            //修改坐骑
            handleUpdateMount: function() {
              var e = this;
              this.$refs.updateMountForm.validate(function(t) {
                if (t) {
                  e.logining = !0;
                  var l = new FormData;
                  l.append("Value1", e.formFileds.role_id), l.append("Value2", e.formFileds.mountid), l.append("Value3", e.formFileds.sw), e.$http
                      .post("/api/updateMount", l).then(function(t) {
                        200 == t.data.code ? (e.isShowUpdateMountDialog = !1, e.$message.success("修改成功")) : e.$alert(
                            "修改失败", "提示", {
                              confirmButtonText: "ok"
                            }), e.logining = !1
                      }).catch(function(t) {
                        e.logining = !1, console.log(t)
                      })
                }
              })
            },

            handleRecharge: function() {
              var e = this;
              this.$refs.rechargeForm.validate(function(t) {
                if (t) {
                  e.logining = !0;
                  var l = new FormData;
                  l.append("Value1", e.formFileds.user_id), l.append("Value2", e.formFileds.money), l.append(
                      "Value3", e.formFileds.xianyu ? e.formFileds.xianyu : ""), l.append("Value4", e.formFileds
                      .draw ? e.formFileds.draw : ""), l.append("Value5", e.formFileds.type), e.$http.post(
                      "/api/recharge", l).then(function(t) {
                    200 == t.data.code ? (e.isShowRechargeDialog = !1, e.getPageData(), e.$message.success(
                        "充值完成")) : e.$alert("充值失败", "提示", {
                      confirmButtonText: "ok"
                    }), e.logining = !1
                  }).catch(function(t) {
                    e.logining = !1, console.log(t)
                  })
                }
              })
            },
            handleControl: function(e) {
              var t = this;
              this.$refs.controlForm.validate(function(l) {
                if (l) {
                  t.logining = !0;
                  var o = new FormData;
                  o.append("Value1", t.formFileds.rolename), 1 === e ? o.append("Value2", "1" === t.formFileds
                      .unknown ? "1" : "4") : 2 === e ? o.append("Value2", "1" === t.formFileds.activity ?
                      "3" : "5") : 3 === e && o.append("Value2", "2"), o.append("Value3", t.formFileds.message ?
                      t.formFileds.message : ""), t.$http.post("/api/roleoperation", o).then(function(l) {
                    200 == l.data.code ? (t.getPageData(), t.$message.success("操作成功"), 3 === e && (t.online = !
                        1)) : t.$alert("操作失败", "提示", {
                      confirmButtonText: "ok"
                    }), t.logining = !1
                  }).catch(function(e) {
                    t.logining = !1, console.log(e)
                  })
                }
              })
            },
            handleSendGoods: function() {
              var e = this;
              this.$refs.sendGoodsForm.validate(function(t) {
                if (t) {
                  e.logining = !0;
                  var l = new FormData;
                  l.append("Value1", e.formFileds.rolename), l.append("Value2", e.formFileds.goodsid), l.append(
                      "Value3", e.formFileds.sum), e.$http.post("/api/sendgoods", l).then(function(t) {
                    200 == t.data.code ? (e.isShowSendGoodsDialog = !1, e.$message.success("物品发送完成")) : e
                        .$alert(t.data.message, "提示", {
                          confirmButtonText: "ok"
                        }), e.logining = !1
                  }).catch(function(t) {
                    e.logining = !1, console.log(t)
                  })
                }
              })
            },
            handleRowClick: function(e, t, l) {
              this.setCurRowChecked(e)
            },
            handleCheckedAllAndCheckedNone: function(e) {
              1 != e.length && this.$refs.list.setCurrentRow()
            },
            dialogClose: function() {
              this.logining = !1, this.$refs.editPwdForm.resetFields()
            },

            deleteClose: function() {
              this.logining = !1, this.$refs.deleteForm.resetFields()
            },

            updatePwdClose: function() {
              this.logining = !1, this.$refs.updatePwdForm.resetFields()
            },
            updateMountClose: function() {
              this.logining = !1, this.$refs.updateMountForm.resetFields()
            },

            rechargeClose: function() {
              this.logining = !1, this.$refs.rechargeForm.resetFields()
            },
            controlClose: function() {
              this.logining = !1, this.$refs.controlForm.resetFields()
            },
            sendGoodsClose: function() {
              this.logining = !1, this.$refs.sendGoodsForm.resetFields()
            },
            rowEdit: function(e, t) {
              for (var l in this.setCurRowChecked(t), this.formFileds) this.formFileds[l] = t[l];
              this.isShowEditDialog = !0
            },
            //删除
            rowDelete: function(e, t) {
              for (var l in this.setCurRowChecked(t), this.formFileds) this.formFileds[l] = t[l];
              this.isShowDeleteDialog = !0
            },
            //修改密码
            rowUpdatePwd: function(e, t) {
              for (var l in this.setCurRowChecked(t), this.formFileds) this.formFileds[l] = t[l];
              this.isShowUpdatePwdDialog = !0
            },

            //设置GM特权
            rowUpdateGM: function(e, t) {
              for (var l in this.setCurRowChecked(t), this.formFileds) this.formFileds[l] = t[l];
              this.isShowUpdateGMDialog = !0
            },

            //修改坐骑
            rowUpdateMount: function(e, t) {
              for (var l in this.setCurRowChecked(t), this.formFileds) this.formFileds[l] = t[l];
              this.isShowUpdateMountDialog = !0
            },

            rowRecharge: function(e, t) {
              for (var l in this.setCurRowChecked(t), this.formFileds) this.formFileds[l] = t[l];
              this.isShowRechargeDialog = !0
            },
            rowControl: function(e, t) {
              for (var l in this.setCurRowChecked(t), this.formFileds) "statues" === l && (this.online = "在线" ===
                  t[l]), this.formFileds[l] = t[l] + "";
              this.isShowControlDialog = !0
            },
            rowSendGoods: function(e, t) {
              for (var l in this.setCurRowChecked(t), this.formFileds) this.formFileds[l] = "sum" === l ? 1 : t[l];
              this.isShowSendGoodsDialog = !0
            },
            handleEdit: function(e) {
              var t = this;
              this.$refs.editForm.validate(function(l) {
                l && (i()(t.tableData[e], t.formFileds), t.isShowEditDialog = !1, t.$refs.list.sort("date",
                    "descending"), t.$message.success("编辑成功"))
              })
            },
            rowDel: function(e, t, l) {
              var o = this;
              l.target.blur(), this.$confirm("确定要删除当前行吗？", "删除", {
                comfirmButtonText: "确定",
                cancelButtonText: "取消"
              }).then(function() {
                return o.tableData.splice(t.id, 1), o.$message.success("删除成功"), !1
              })
            },
            setCurRowChecked: function(e) {
              this.$refs.list.clearSelection(), this.$refs.list.toggleRowSelection(e)
            }
          }
        },
        s = {
          render: function() {
            var e = this,
                t = e.$createElement,
                l = e._self._c || t;
            return l("div", [l("el-row", {
              attrs: {
                gutter: 10
              }
            }, [l("el-col", {
              attrs: {
                xs: 24,
                sm: 24,
                md: 8,
                lg: 8,
                xl: 8
              }
            }, [l("el-input", {
              staticClass: "input-with-select",
              attrs: {
                placeholder: "请输入内容"
              },
              model: {
                value: e.searchValue,
                callback: function(t) {
                  e.searchValue = t
                },
                expression: "searchValue"
              }
            }, [l("el-select", {
              staticClass: "search-select",
              attrs: {
                slot: "prepend",
                placeholder: "请选择"
              },
              slot: "prepend",
              model: {
                value: e.searchType,
                callback: function(t) {
                  e.searchType = t
                },
                expression: "searchType"
              }
            }, [l("el-option", {
              attrs: {
                label: "角色ID",
                value: "1"
              }
            }), e._v(" "), l("el-option", {
              attrs: {
                label: "角色名",
                value: "2"
              }
            }), e._v(" "), l("el-option", {
              attrs: {
                label: "账号",
                value: "3"
              }
            })], 1), e._v(" "), l("el-button", {
              attrs: {
                slot: "append",
                icon: "el-icon-search"
              },
              on: {
                click: function(t) {
                  return e.getPageData()
                }
              },
              slot: "append"
            })], 1)], 1), e._v(" "), l("el-col", {
              attrs: {
                xs: 24,
                sm: 24,
                md: 16,
                lg: 16,
                xl: 16
              }
            }, [l("el-pagination", {
              attrs: {
                background: "",
                "page-sizes": [10, 20, 30, 40],
                "page-size": 10,
                total: this.total,
                layout: "prev, pager, next, sizes, total"
              },
              on: {
                "size-change": e.setSize,
                "current-change": e.setPage
              }
            })], 1)], 1), e._v(" "), l("el-table", {
              ref: "list",
              staticStyle: {
                width: "100%"
              },
              attrs: {
                data: e.tableData,
                border: "",
                stripe: "",
                "highlight-current-row": "",
                "default-sort": {
                  prop: "userString",
                  order: "descending"
                }
              },
              on: {
                "row-click": e.handleRowClick,
                "select-all": e.handleCheckedAllAndCheckedNone,
                select: e.handleCheckedAllAndCheckedNone
              }
            }, [l("el-table-column", {
              attrs: {
                type: "selection",
                width: "45",
                align: "center"
              }
            }), e._v(" "), l("el-table-column", {
              attrs: {
                property: "role_id",
                label: "ID",
                width: "50"
              }
            }), e._v(" "), l("el-table-column", {
              attrs: {
                property: "rolename",
                label: "角色名",
                width: "130"
              }
            }), e._v(" "), l("el-table-column", {
              attrs: {
                property: "localname",
                label: "账号",
                width: "130"
              }
            }), e._v(" "), l("el-table-column", {
              attrs: {
                property: "userregidtsertime",
                label: "注册时间",
                width: "180",
                sortable: ""
              },
              scopedSlots: e._u([{
                key: "default",
                fn: function(t) {
                  return [l("span", {
                    staticStyle: {
                      "margin-left": "5px"
                    }
                  }, [e._v(e._s(e._f("formatDate")(t.row.userregidtsertime)))])]
                }
              }])
            }), e._v(" "), l("el-table-column", {
              attrs: {
                property: "activity",
                width: "100",
                label: "封停状态"
              },
              scopedSlots: e._u([{
                key: "default",
                fn: function(t) {
                  return [l("div", {
                    domProps: {
                      innerHTML: e._s(e.$options.filters.activityStatusName(t.row.activity))
                    }
                  })]
                }
              }])
            }), e._v(" "), l("el-table-column", {
              attrs: {
                property: "statues",
                width: "100",
                label: "目前状态"
              }
            }), e._v(" "), l("el-table-column", {
              attrs: {
                property: "codecard",
                width: "130",
                label: "仙玉"
              }
            }), e._v(" "), l("el-table-column", {
              attrs: {
                label: "操作",
                align: "center"
              },
              scopedSlots: e._u([{
                key: "default",
                fn: function(t) {
                  return [l("el-button", {
                    attrs: {
                      circle: "",
                      icon: "el-icon-edit-outline",
                      type: "primary",
                      title: "重置解锁密码",
                      size: "small"
                    },
                    on: {
                      click: function(l) {
                        return e.rowEdit(t.$index, t.row)
                      }
                    }
                  }), e._v(" "), l("el-button", {
                    attrs: {
                      circle: "",
                      icon: "el-icon-edit",
                      type: "danger",
                      title: "修改密码",
                      size: "small"
                    },
                    on: {
                      click: function(l) {
                        return e.rowUpdatePwd(t.$index, t.row)
                      }
                    }
                  }), e._v(" "), l("el-button", {
                    attrs: {
                      circle: "",
                      icon: "el-icon-edit",
                      type: "danger",
                      title: "设置GM特权",
                      size: "small"
                    },
                    on: {
                      click: function(l) {
                        return e.rowUpdateGM(t.$index, t.row)
                      }
                    }
                  }), e._v(" "), l("el-button", {
                    attrs: {
                      circle: "",
                      icon: "el-icon-trophy",
                      type: "warning",
                      title: "充值仙玉、礼包开通",
                      size: "small"
                    },
                    on: {
                      click: function(l) {
                        return e.rowRecharge(t.$index, t.row)
                      }
                    }
                  }), e._v(" "), l("el-button", {
                    attrs: {
                      circle: "",
                      icon: "el-icon-lock",
                      type: "danger",
                      title: "权限控制",
                      size: "small"
                    },
                    on: {
                      click: function(l) {
                        return e.rowControl(t.$index, t.row)
                      }
                    }
                  }), e._v(" "), l("el-button", {
                    attrs: {
                      circle: "",
                      icon: "el-icon-s-promotion",
                      type: "success",
                      title: "物品发送",
                      size: "small"
                    },
                    on: {
                      click: function(l) {
                        return e.rowSendGoods(t.$index, t.row)
                      }
                    }
                  }), e._v(" "), l("el-button", {
                    attrs: {
                      circle: "",
                      icon: "el-icon-bicycle",
                      type: "danger",
                      title: "修改坐骑",
                      size: "small"
                    },
                    on: {
                      click: function(l) {
                        return e.rowUpdateMount(t.$index, t.row)
                      }
                    }
                  }), e._v(" "), l("el-button", {
                    attrs: {
                      circle: "",
                      icon: "el-icon-delete",
                      type: "danger",
                      title: "删除角色",
                      size: "small"
                    },
                    on: {
                      click: function(l) {
                        return e.rowDelete(t.$index, t.row)
                      }
                    }
                  })]
                }
              }])
            })], 1), e._v(" "), l("el-dialog", {
              attrs: {
                title: "重置解锁密码",
                visible: e.isShowEditDialog,
                width: "380px"
              },
              on: {
                "update:visible": function(t) {
                  e.isShowEditDialog = t
                },
                close: e.dialogClose
              }
            }, [l("el-form", {
              ref: "editPwdForm",
              attrs: {
                model: e.formFileds,
                "label-width": "80px",
                rules: e.rules
              }
            }, [l("el-form-item", {
              attrs: {
                label: "角色名",
                prop: "rolename"
              }
            }, [l("el-input", {
              attrs: {
                disabled: !0
              },
              model: {
                value: e.formFileds.rolename,
                callback: function(t) {
                  e.$set(e.formFileds, "rolename", t)
                },
                expression: "formFileds.rolename"
              }
            })], 1), e._v(" "), l("el-form-item", {
              attrs: {
                label: "角色ID",
                prop: "role_id"
              }
            }, [l("el-input", {
              attrs: {
                disabled: !0
              },
              model: {
                value: e.formFileds.role_id,
                callback: function(t) {
                  e.$set(e.formFileds, "role_id", t)
                },
                expression: "formFileds.role_id"
              }
            })], 1), e._v(" "), l("el-alert", {
              staticClass: "pwdalert",
              attrs: {
                "show-icon": "",
                title: "玩家角色必须下线再更改解锁密码才能生效",
                type: "info",
                effect: "dark",
                closable: !1
              }
            }), e._v(" "), l("el-form-item", {
              attrs: {
                label: "解锁密码",
                prop: "password"
              }
            }, [l("el-input", {
              attrs: {
                type: "password",
                "show-password": ""
              },
              model: {
                value: e.formFileds.password,
                callback: function(t) {
                  e.$set(e.formFileds, "password", t)
                },
                expression: "formFileds.password"
              }
            })], 1), e._v(" "), l("el-form-item", [l("el-button", {
              staticClass: "pull-right margin-l-10",
              on: {
                click: function(t) {
                  e.isShowEditDialog = !1
                }
              }
            }, [e._v("取消")]), e._v(" "), l("el-button", {
              staticClass: "pull-right",
              attrs: {
                type: "primary",
                loading: e.logining
              },
              on: {
                click: function(t) {
                  return e.handleEditPwd()
                }
              }
            }, [e._v("确定")])], 1)], 1)], 1),

              //删除角色
              e._v(" "), l("el-dialog", {
                attrs: {
                  title: "删除角色",
                  visible: e.isShowDeleteDialog,
                  width: "380px"
                },
                on: {
                  "update:visible": function(t) {
                    e.isShowDeleteDialog = t
                  },
                  close: e.dialogClose
                }
              }, [l("el-form", {
                ref: "deleteForm",
                attrs: {
                  model: e.formFileds,
                  "label-width": "80px",
                  rules: e.rules
                }
              }, [l("el-form-item", {
                attrs: {
                  label: "角色名",
                  prop: "rolename"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.rolename,
                  callback: function(t) {
                    e.$set(e.formFileds, "rolename", t)
                  },
                  expression: "formFileds.rolename"
                }
              })], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "角色ID",
                  prop: "role_id"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.role_id,
                  callback: function(t) {
                    e.$set(e.formFileds, "role_id", t)
                  },
                  expression: "formFileds.role_id"
                }
              })], 1), e._v(" "), l("el-alert", {
                staticClass: "pwdalert",
                attrs: {
                  "show-icon": "",
                  title: "玩家角色必须下线才能生效",
                  type: "info",
                  effect: "dark",
                  closable: !1
                }
              }), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "解锁密码",
                  prop: "password"
                }
              }, [l("el-input", {
                attrs: {
                  type: "password",
                  "show-password": ""
                },
                model: {
                  value: e.formFileds.password,
                  callback: function(t) {
                    e.$set(e.formFileds, "password", t)
                  },
                  expression: "formFileds.password"
                }
              })], 1), e._v(" "), l("el-form-item", [l("el-button", {
                staticClass: "pull-right margin-l-10",
                on: {
                  click: function(t) {
                    e.isShowDeleteDialog = !1
                  }
                }
              }, [e._v("取消")]), e._v(" "), l("el-button", {
                staticClass: "pull-right",
                attrs: {
                  type: "primary",
                  loading: e.logining
                },
                on: {
                  click: function(t) {
                    return e.handleDelete()
                  }
                }
              }, [e._v("确定")])], 1)], 1)], 1),

              //修改密码
              e._v(" "), l("el-dialog", {
                attrs: {
                  title: "修改密码",
                  visible: e.isShowUpdatePwdDialog,
                  width: "380px"
                },
                on: {
                  "update:visible": function(t) {
                    e.isShowUpdatePwdDialog = t
                  },
                  close: e.dialogClose
                }
              }, [l("el-form", {
                ref: "updatePwdForm",
                attrs: {
                  model: e.formFileds,
                  "label-width": "80px",
                  rules: e.rules
                }
              }, [l("el-form-item", {
                attrs: {
                  label: "角色名",
                  prop: "rolename"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.rolename,
                  callback: function(t) {
                    e.$set(e.formFileds, "rolename", t)
                  },
                  expression: "formFileds.rolename"
                }
              })], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "角色ID",
                  prop: "role_id"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.role_id,
                  callback: function(t) {
                    e.$set(e.formFileds, "role_id", t)
                  },
                  expression: "formFileds.role_id"
                }
              })], 1), e._v(" "), l("el-alert", {
                staticClass: "pwdalert",
                attrs: {
                  "show-icon": "",
                  title: "玩家角色必须下线才能生效",
                  type: "info",
                  effect: "dark",
                  closable: !1
                }
              }), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "新密码",
                  prop: "password"
                }
              }, [l("el-input", {
                attrs: {
                  type: "password",
                  "show-password": ""
                },
                model: {
                  value: e.formFileds.password,
                  callback: function(t) {
                    e.$set(e.formFileds, "password", t)
                  },
                  expression: "formFileds.password"
                }
              })], 1), e._v(" "), l("el-form-item", [l("el-button", {
                staticClass: "pull-right margin-l-10",
                on: {
                  click: function(t) {
                    e.isShowUpdatePwdDialog = !1
                  }
                }
              }, [e._v("取消")]), e._v(" "), l("el-button", {
                staticClass: "pull-right",
                attrs: {
                  type: "primary",
                  loading: e.logining
                },
                on: {
                  click: function(t) {
                    return e.handleUpdatePwd()
                  }
                }
              }, [e._v("确定")])], 1)], 1)], 1),

              //设置GM特权
              e._v(" "), l("el-dialog", {
                attrs: {
                  title: "设置GM特权",
                  visible: e.isShowUpdateGMDialog,
                  width: "380px"
                },
                on: {
                  "update:visible": function(t) {
                    e.isShowUpdateGMDialog = t
                  },
                  close: e.dialogClose
                }
              }, [l("el-form", {
                ref: "updateGMForm",
                attrs: {
                  model: e.formFileds,
                  "label-width": "80px",
                  rules: e.rules
                }
              }, [l("el-form-item", {
                attrs: {
                  label: "角色名",
                  prop: "rolename"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.rolename,
                  callback: function(t) {
                    e.$set(e.formFileds, "rolename", t)
                  },
                  expression: "formFileds.rolename"
                }
              })], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "角色ID",
                  prop: "role_id"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.role_id,
                  callback: function(t) {
                    e.$set(e.formFileds, "role_id", t)
                  },
                  expression: "formFileds.role_id"
                }
              })], 1), e._v(" "), l("el-alert", {
                staticClass: "pwdalert",
                attrs: {
                  "show-icon": "",
                  title: "玩家角色必须下线才能生效",
                  type: "info",
                  effect: "dark",
                  closable: !1
                }
              }), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "特权等级",
                  prop: "gmtype"
                }
              }, [l("el-input", {

                model: {
                  value: e.formFileds.gmtype,
                  callback: function(t) {
                    e.$set(e.formFileds, "gmtype", t)
                  },
                  expression: "formFileds.gmtype"
                }
              })], 1), e._v(" "), l("el-form-item", [l("el-button", {
                staticClass: "pull-right margin-l-10",
                on: {
                  click: function(t) {
                    e.isShowUpdateGMDialog = !1
                  }
                }
              }, [e._v("取消")]), e._v(" "), l("el-button", {
                staticClass: "pull-right",
                attrs: {
                  type: "primary",
                  loading: e.logining
                },
                on: {
                  click: function(t) {
                    return e.handleUpdateGM()
                  }
                }
              }, [e._v("确定")])], 1)], 1)], 1),

              //修改坐骑
              e._v(" "), l("el-dialog", {
                attrs: {
                  title: "修改坐骑",
                  visible: e.isShowUpdateMountDialog,
                  width: "380px"
                },
                on: {
                  "update:visible": function(t) {
                    e.isShowUpdateMountDialog = t
                  },
                  close: e.dialogClose
                }
              }, [l("el-form", {
                ref: "updateMountForm",
                attrs: {
                  model: e.formFileds,
                  "label-width": "80px",
                  rules: e.rules
                }
              }, [l("el-form-item", {
                attrs: {
                  label: "角色名",
                  prop: "rolename"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.rolename,
                  callback: function(t) {
                    e.$set(e.formFileds, "rolename", t)
                  },
                  expression: "formFileds.rolename"
                }
              })], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "角色ID",
                  prop: "role_id"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.role_id,
                  callback: function(t) {
                    e.$set(e.formFileds, "role_id", t)
                  },
                  expression: "formFileds.role_id"
                }
              })], 1), e._v(" "), l("el-alert", {
                staticClass: "pwdalert",
                attrs: {
                  "show-icon": "",
                  title: "玩家角色必须下线才能生效",
                  type: "info",
                  effect: "dark",
                  closable: !1
                }
              }), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "坐骑阶数",
                  prop: "mountid"
                }
              }, [l("el-input", {
                model: {
                  value: e.formFileds.mountid,
                  callback: function(t) {
                    e.$set(e.formFileds, "mountid", e._n(t))
                  },
                  expression: "formFileds.mountid"
                }
              }, [l("template", {
                slot: "append"
              }, [e._v("阶")])], 2)], 1),
                e._v(" "), l("el-alert", {
                  staticClass: "pwdalert",
                  attrs: {
                    "show-icon": "",
                    title: "三维格式, 灵力|力量|根骨。例：30,50,70",
                    type: "info",
                    effect: "dark",
                    closable: !1
                  }
                }),
                //三维
                e._v(" "), l("el-form-item", {
                  attrs: {
                    label: "三维初值",
                    prop: "sw"
                  }
                }, [l("el-input", {

                  model: {
                    value: e.formFileds.sw,
                    callback: function(t) {
                      e.$set(e.formFileds, "sw", t)
                    },
                    expression: "formFileds.sw"
                  }
                })], 1),



                e._v(" "), l("el-form-item", [l("el-button", {
                  staticClass: "pull-right margin-l-10",
                  on: {
                    click: function(t) {
                      e.isShowUpdateMountDialog = !1
                    }
                  }
                }, [e._v("取消")]), e._v(" "), l("el-button", {
                  staticClass: "pull-right",
                  attrs: {
                    type: "primary",
                    loading: e.logining
                  },
                  on: {
                    click: function(t) {
                      return e.handleUpdateMount()
                    }
                  }
                }, [e._v("确定")])], 1)], 1)], 1),
              //充值
              e._v(" "), l("el-dialog", {
                attrs: {
                  title: "充值管理",
                  visible: e.isShowRechargeDialog,
                  width: "380px"
                },
                on: {
                  "update:visible": function(t) {
                    e.isShowRechargeDialog = t
                  },
                  close: e.rechargeClose
                }
              }, [l("el-form", {
                ref: "rechargeForm",
                attrs: {
                  model: e.formFileds,
                  "label-width": "80px",
                  rules: e.rules
                }
              }, [l("el-form-item", {
                directives: [{
                  name: "show",
                  rawName: "v-show",
                  value: e.hide,
                  expression: "hide"
                }],
                attrs: {
                  label: "用户ID",
                  prop: "user_id"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.user_id,
                  callback: function(t) {
                    e.$set(e.formFileds, "user_id", t)
                  },
                  expression: "formFileds.user_id"
                }
              })], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "角色名",
                  prop: "rolename"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.rolename,
                  callback: function(t) {
                    e.$set(e.formFileds, "rolename", t)
                  },
                  expression: "formFileds.rolename"
                }
              })], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "角色ID",
                  prop: "role_id"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.role_id,
                  callback: function(t) {
                    e.$set(e.formFileds, "role_id", t)
                  },
                  expression: "formFileds.role_id"
                }
              })], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "充值金额",
                  prop: "money"
                }
              }, [l("el-input", {
                model: {
                  value: e.formFileds.money,
                  callback: function(t) {
                    e.$set(e.formFileds, "money", e._n(t))
                  },
                  expression: "formFileds.money"
                }
              }, [l("template", {
                slot: "append"
              }, [e._v("元")])], 2)], 1), e._v(" "), "2" === e.formFileds.type ? l("el-form-item", [l(
                  "el-tag", {
                    staticClass: "pull-right",
                    attrs: {
                      type: "warning"
                    }
                  }, [e._v("VIP特权按充值金额1元1天计算")])], 1) : e._e(), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "充值类型",
                  prop: "type"
                }
              }, [l("el-select", {
                attrs: {
                  placeholder: "请选择"
                },
                model: {
                  value: e.formFileds.type,
                  callback: function(t) {
                    e.$set(e.formFileds, "type", t)
                  },
                  expression: "formFileds.type"
                }
              }, e._l(e.options, function(e) {
                return l("el-option", {
                  key: e.value,
                  attrs: {
                    label: e.label,
                    value: e.value
                  }
                })
              }), 1)], 1), e._v(" "), "1" === e.formFileds.type ? l("el-form-item", {
                attrs: {
                  label: "仙玉数量",
                  prop: "xianyu"
                }
              }, [l("el-input", {
                model: {
                  value: e.formFileds.xianyu,
                  callback: function(t) {
                    e.$set(e.formFileds, "xianyu", e._n(t))
                  },
                  expression: "formFileds.xianyu"
                }
              }, [l("template", {
                slot: "append"
              }, [e._v("仙玉")])], 2)], 1) : e._e(), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "抽奖次数",
                  prop: "draw"
                }
              }, [l("el-input", {
                model: {
                  value: e.formFileds.draw,
                  callback: function(t) {
                    e.$set(e.formFileds, "draw", e._n(t))
                  },
                  expression: "formFileds.draw"
                }
              }, [l("template", {
                slot: "append"
              }, [e._v("次")])], 2)], 1), e._v(" "), l("el-form-item", [l("el-button", {
                staticClass: "pull-right margin-l-10",
                on: {
                  click: function(t) {
                    e.isShowRechargeDialog = !1
                  }
                }
              }, [e._v("取消")]), e._v(" "), l("el-button", {
                staticClass: "pull-right",
                attrs: {
                  type: "primary",
                  loading: e.logining
                },
                on: {
                  click: function(t) {
                    return e.handleRecharge()
                  }
                }
              }, [e._v("确定")])], 1)], 1)], 1), e._v(" "), l("el-dialog", {
                attrs: {
                  title: "玩家权限控制",
                  visible: e.isShowControlDialog,
                  width: "350px"
                },
                on: {
                  "update:visible": function(t) {
                    e.isShowControlDialog = t
                  },
                  close: e.controlClose
                }
              }, [l("el-form", {
                ref: "controlForm",
                attrs: {
                  model: e.formFileds,
                  "label-width": "80px",
                  rules: e.rules
                }
              }, [l("el-form-item", {
                attrs: {
                  label: "角色名",
                  prop: "rolename"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.rolename,
                  callback: function(t) {
                    e.$set(e.formFileds, "rolename", t)
                  },
                  expression: "formFileds.rolename"
                }
              })], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "角色ID",
                  prop: "role_id"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.role_id,
                  callback: function(t) {
                    e.$set(e.formFileds, "role_id", t)
                  },
                  expression: "formFileds.role_id"
                }
              })], 1), e._v(" "), l("el-form-item", {
                directives: [{
                  name: "show",
                  rawName: "v-show",
                  value: !1,
                  expression: "false"
                }],
                attrs: {
                  label: "在线状态",
                  prop: "statues"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.statues,
                  callback: function(t) {
                    e.$set(e.formFileds, "statues", t)
                  },
                  expression: "formFileds.statues"
                }
              })], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "发言控制",
                  prop: "unknown"
                }
              }, [l("el-switch", {
                attrs: {
                  "active-text": "开启发言",
                  "inactive-text": "禁止发言",
                  "active-value": "0",
                  "inactive-value": "1"
                },
                on: {
                  change: function(t) {
                    return e.handleControl(1)
                  }
                },
                model: {
                  value: e.formFileds.unknown,
                  callback: function(t) {
                    e.$set(e.formFileds, "unknown", t)
                  },
                  expression: "formFileds.unknown"
                }
              })], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "登录控制",
                  prop: "activity"
                }
              }, [l("el-switch", {
                attrs: {
                  "active-text": "允许登录",
                  "inactive-text": "禁止登录",
                  "active-value": "0",
                  "inactive-value": "1"
                },
                on: {
                  change: function(t) {
                    return e.handleControl(2)
                  }
                },
                model: {
                  value: e.formFileds.activity,
                  callback: function(t) {
                    e.$set(e.formFileds, "activity", t)
                  },
                  expression: "formFileds.activity"
                }
              })], 1), e._v(" "), l("el-form-item", [l("el-button", {
                staticClass: "pull-right margin-l-10",
                on: {
                  click: function(t) {
                    e.isShowControlDialog = !1
                  }
                }
              }, [e._v("取消")]), e._v(" "), l("el-button", {
                staticClass: "pull-right",
                attrs: {
                  type: "danger",
                  disabled: !e.online,
                  loading: e.logining
                },
                on: {
                  click: function(t) {
                    return e.handleControl(3)
                  }
                }
              }, [e._v("踢他下线")])], 1)], 1)], 1), e._v(" "), l("el-dialog", {
                attrs: {
                  title: "物品发送",
                  visible: e.isShowSendGoodsDialog,
                  width: "380px"
                },
                on: {
                  "update:visible": function(t) {
                    e.isShowSendGoodsDialog = t
                  },
                  close: e.sendGoodsClose
                }
              }, [l("el-form", {
                ref: "sendGoodsForm",
                attrs: {
                  model: e.formFileds,
                  "label-width": "80px",
                  rules: e.rules
                }
              }, [l("el-form-item", {
                attrs: {
                  label: "角色名",
                  prop: "rolename"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.rolename,
                  callback: function(t) {
                    e.$set(e.formFileds, "rolename", t)
                  },
                  expression: "formFileds.rolename"
                }
              })], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "角色ID",
                  prop: "role_id"
                }
              }, [l("el-input", {
                attrs: {
                  disabled: !0
                },
                model: {
                  value: e.formFileds.role_id,
                  callback: function(t) {
                    e.$set(e.formFileds, "role_id", t)
                  },
                  expression: "formFileds.role_id"
                }
              })], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "选择物品",
                  prop: "goodsid"
                }
              }, [l("el-select", {
                attrs: {
                  filterable: "",
                  placeholder: "可输入搜索"
                },
                model: {
                  value: e.formFileds.goodsid,
                  callback: function(t) {
                    e.$set(e.formFileds, "goodsid", t)
                  },
                  expression: "formFileds.goodsid"
                }
              }, e._l(e.goodsOptions, function(e) {
                return l("el-option", {
                  key: e.value,
                  attrs: {
                    label: e.label,
                    value: e.value
                  }
                })
              }), 1)], 1), e._v(" "), l("el-form-item", {
                attrs: {
                  label: "数量",
                  prop: "sum"
                }
              }, [l("el-input-number", {
                attrs: {
                  min: 1,
                  max: 999
                },
                model: {
                  value: e.formFileds.sum,
                  callback: function(t) {
                    e.$set(e.formFileds, "sum", e._n(t))
                  },
                  expression: "formFileds.sum"
                }
              })], 1), e._v(" "), l("el-form-item", [l("el-button", {
                staticClass: "pull-right margin-l-10",
                on: {
                  click: function(t) {
                    e.isShowSendGoodsDialog = !1
                  }
                }
              }, [e._v("取消")]), e._v(" "), l("el-button", {
                staticClass: "pull-right",
                attrs: {
                  type: "primary",
                  loading: e.logining
                },
                on: {
                  click: function(t) {
                    return e.handleSendGoods()
                  }
                }
              }, [e._v("发送")])], 1)], 1)], 1)
            ], 1)
          },
          staticRenderFns: []
        };
    var r = l("VU/8")(a, s, !1, function(e) {
      l("Pe2G")
    }, "data-v-12e57dd7", null);
    t.default = r.exports
  },
  Pe2G: function(e, t) {}
});
//# sourceMappingURL=26.09378e8a7aca5be8bb4e.js.map
