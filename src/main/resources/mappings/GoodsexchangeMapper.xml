<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.come.mapper.GoodsexchangeMapper" >
  <resultMap id="BaseResultMap" type="org.come.entity.Goodsexchange" >
    <id column="GOODSGUID" property="goodsguid" jdbcType="VARCHAR" />
    <result column="FLAG" property="flag" jdbcType="DECIMAL" />
    <result column="GOODSID" property="goodsid" jdbcType="VARCHAR" />
    <result column="OUTTIME" property="outtime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    GOODSGUID, FLAG, GOODSID, OUTTIME
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="org.come.entity.GoodsexchangeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from GOODSEXCHANGE
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <where>
		<if test="goodsguid != null and goodsguid != ''">
			and GOODSGUID like '%'||#{goodsguid}||'%'
		</if>
		<if test="goodsid != null and goodsid != ''">
			and GOODSID = #{goodsid}
		</if>
		<if test="flag != null">
			and FLAG = #{flag}
		</if>
	</where>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from GOODSEXCHANGE
    where GOODSGUID = #{goodsguid,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from GOODSEXCHANGE
    where GOODSGUID = #{goodsguid,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="org.come.entity.GoodsexchangeExample" >
    delete from GOODSEXCHANGE
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="org.come.entity.Goodsexchange" >
  	<selectKey keyProperty="goodsguid" resultType="java.lang.String"
			order="BEFORE">
			select sys_guid() from dual
		</selectKey>
    insert into GOODSEXCHANGE (goodsguid,goodsid)
    values (#{goodsguid,jdbcType=VARCHAR},#{goodsid,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.come.entity.Goodsexchange" >
    insert into GOODSEXCHANGE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="goodsguid != null" >
        GOODSGUID,
      </if>
      <if test="flag != null" >
        FLAG,
      </if>
      <if test="goodsid != null" >
        GOODSID,
      </if>
      <if test="outtime != null" >
        OUTTIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="goodsguid != null" >
        #{goodsguid,jdbcType=VARCHAR},
      </if>
      <if test="flag != null" >
        #{flag,jdbcType=DECIMAL},
      </if>
      <if test="goodsid != null" >
        #{goodsid,jdbcType=VARCHAR},
      </if>
      <if test="outtime != null" >
        #{outtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="org.come.entity.GoodsexchangeExample" resultType="java.lang.Integer" >
    select count(*) from GOODSEXCHANGE
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
     <where>
		<if test="goodsguid != null and goodsguid != ''">
			and GOODSGUID like '%'||#{goodsguid}||'%'
		</if>
		<if test="goodsid != null and goodsid != ''">
			and GOODSID = #{goodsid}
		</if>
		<if test="flag != null">
			and FLAG = #{flag}
		</if>
	</where>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update GOODSEXCHANGE
    <set >
      <if test="record.goodsguid != null" >
        GOODSGUID = #{record.goodsguid,jdbcType=VARCHAR},
      </if>
      <if test="record.flag != null" >
        FLAG = #{record.flag,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsid != null" >
        GOODSID = #{record.goodsid,jdbcType=VARCHAR},
      </if>
      <if test="record.outtime != null" >
        OUTTIME = #{record.outtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update GOODSEXCHANGE
    set GOODSGUID = #{record.goodsguid,jdbcType=VARCHAR},
      FLAG = #{record.flag,jdbcType=DECIMAL},
      GOODSID = #{record.goodsid,jdbcType=VARCHAR},
      OUTTIME = #{record.outtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="org.come.entity.Goodsexchange" >
    update GOODSEXCHANGE
    <set >
      <if test="flag != null" >
        FLAG = #{flag,jdbcType=DECIMAL},
      </if>
      <if test="goodsid != null" >
        GOODSID = #{goodsid,jdbcType=VARCHAR},
      </if>
      <if test="outtime != null" >
        OUTTIME = #{outtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where GOODSGUID = #{goodsguid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.come.entity.Goodsexchange" >
    update GOODSEXCHANGE
    set FLAG = #{flag,jdbcType=DECIMAL},
      GOODSID = #{goodsid,jdbcType=VARCHAR},
      OUTTIME = #{outtime,jdbcType=TIMESTAMP}
    where GOODSGUID = #{goodsguid,jdbcType=VARCHAR}
  </update>
</mapper>