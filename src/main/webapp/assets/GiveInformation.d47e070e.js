var w=Object.defineProperty;var u=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable;var _=(e,a,l)=>a in e?w(e,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[a]=l,m=(e,a)=>{for(var l in a||(a={}))C.call(a,l)&&_(e,l,a[l]);if(u)for(var l of u(a))x.call(a,l)&&_(e,l,a[l]);return e};import{G as I,D as g,r as c,o as N,b as T,e as s,f as t,w as i,g as q}from"./vendor.45bae780.js";import{m as D}from"./index.ce5e7832.js";import"./request.3dc6f249.js";import{_ as S}from"./index.7c530c9d.js";const k={name:"giveinformation",setup(){const e=I({Value1:"roleId",Value2:"",pageNum:1,pageSize:10,type:2}),a=g([]),l=g(0),o=()=>{D(m({},e)).then(n=>{a.value=n.data.list,l.value=n.data.total||0}).catch(function(n){console.log(n)})};return o(),{query:e,tableData:a,pageTotal:l,handleSearch:()=>{e.pageNum=1,e.roleId="",e.roleName="",e[e.Value1]=e.Value2,o()},handlePageChange:n=>{e.pageNum=n,o()},getType:n=>{switch(n){case 0:return"\u5546\u5E97\u6216\u5546\u57CE\u8D2D\u4E70";case 1:return"\u6446\u644A\u8D2D\u4E70";case 2:return"\u7ED9\u4E0E";case 3:return"\u793C\u5305\u83B7\u5F97";case 4:return"\u5176\u4ED6"}}}}},j={class:"container"},z={class:"handle-box"},G=["innerHTML"],B={class:"pagination"};function P(e,a,l,o,h,b){const p=c("el-option"),n=c("el-select"),y=c("el-input"),f=c("el-button"),r=c("el-table-column"),v=c("el-table"),V=c("el-pagination");return N(),T("div",null,[s("div",j,[s("div",z,[t(n,{modelValue:o.query.Value1,"onUpdate:modelValue":a[0]||(a[0]=d=>o.query.Value1=d),placeholder:"\u8BF7\u9009\u62E9",class:"handle-select mr10"},{default:i(()=>[t(p,{label:"\u89D2\u8272ID",value:"roleId"}),t(p,{label:"\u89D2\u8272\u540D\u79F0",value:"roleName"})]),_:1},8,["modelValue"]),t(y,{modelValue:o.query.Value2,"onUpdate:modelValue":a[1]||(a[1]=d=>o.query.Value2=d),placeholder:"\u7528\u6237\u540D",class:"handle-input mr10"},null,8,["modelValue"]),t(f,{type:"primary",icon:"el-icon-search",onClick:o.handleSearch},{default:i(()=>[q("\u641C\u7D22")]),_:1},8,["onClick"])]),t(v,{data:o.tableData,border:"",class:"table",ref:"multipleTable","header-cell-class-name":"table-header"},{default:i(()=>[t(r,{property:"grid",width:"80",label:"ID"}),t(r,{property:"recordtype",width:"120",label:"\u8BB0\u5F55\u7C7B\u578B"},{default:i(d=>[s("div",{innerHTML:o.getType(d.row.recordtype)},null,8,G)]),_:1}),t(r,{property:"roleid",width:"120",label:"\u89D2\u8272ID"}),t(r,{property:"otherrole",width:"120",label:"\u5BF9\u65B9\u89D2\u8272ID"}),t(r,{property:"goods",label:"\u64CD\u4F5C\u7269\u54C1"}),t(r,{property:"goodsnum",width:"120",label:"\u7269\u54C1\u6570\u91CF"}),t(r,{property:"rolename",label:"\u89D2\u8272\u540D\u79F0",width:"120",sortable:""}),t(r,{property:"othername",label:"\u5BF9\u65B9\u89D2\u8272\u540D\u79F0",width:"150",sortable:""}),t(r,{property:"recordtimeString",label:"\u8BB0\u5F55\u65F6\u95F4"})]),_:1},8,["data"]),s("div",B,[t(V,{background:"",layout:"total, prev, pager, next","current-page":o.query.pageNum,"page-size":o.query.pageSize,total:o.pageTotal,onCurrentChange:o.handlePageChange},null,8,["current-page","page-size","total","onCurrentChange"])])])])}var R=S(k,[["render",P],["__scopeId","data-v-7973f42e"]]);export{R as default};
