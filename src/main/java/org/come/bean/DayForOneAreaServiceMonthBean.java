package org.come.bean;

import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 * 按照日统计报表
 *
 */
public class DayForOneAreaServiceMonthBean {
	 /**
     * 服务区ID
     */
    private BigDecimal sid;
    
    /**
     * 月份
     */
    private String month;
	private BigDecimal one=new BigDecimal(0);
	private BigDecimal two=new BigDecimal(0) ;
	private BigDecimal three=new BigDecimal(0) ;
	private BigDecimal four=new BigDecimal(0) ;
	private BigDecimal five =new BigDecimal(0);
     
	private BigDecimal six =new BigDecimal(0);
	private BigDecimal seven=new BigDecimal(0);
	private BigDecimal eight =new BigDecimal(0);
	private BigDecimal nine=new BigDecimal(0) ;
	private BigDecimal ten =new BigDecimal(0);
     
	private BigDecimal elev=new BigDecimal(0) ;
	private BigDecimal tween=new BigDecimal(0);
	private BigDecimal thirteen=new BigDecimal(0) ;
	private BigDecimal fourteen=new BigDecimal(0) ;
	private BigDecimal fivteen=new BigDecimal(0);
     
	private BigDecimal sixteen=new BigDecimal(0);
	private BigDecimal seventeen =new BigDecimal(0);
	private BigDecimal eightteen=new BigDecimal(0) ;
	private BigDecimal nineteen=new BigDecimal(0);
	private BigDecimal tweity=new BigDecimal(0);
     
	private BigDecimal twyone=new BigDecimal(0) ;
	private BigDecimal twytwo =new BigDecimal(0);
	private BigDecimal twythree=new BigDecimal(0) ;
	private BigDecimal twyfour =new BigDecimal(0);
	private BigDecimal twyfive =new BigDecimal(0);
     
	private BigDecimal twysix=new BigDecimal(0) ;
	private BigDecimal twyseven=new BigDecimal(0) ;
	private BigDecimal twyeight=new BigDecimal(0) ;
	private BigDecimal twynine=new BigDecimal(0) ;
	private BigDecimal thirty=new BigDecimal(0) ;
     
	private BigDecimal thyone=new BigDecimal(0) ;

	public BigDecimal getOne() {
		return one;
	}

	public String getMonth() {
		return month;
	}

	public void setMonth(String month) {
		this.month = month;
	}

	public void setOne(BigDecimal one) {
		this.one = one;
	}

	public BigDecimal getTwo() {
		return two;
	}

	public void setTwo(BigDecimal two) {
		this.two = two;
	}

	public BigDecimal getThree() {
		return three;
	}

	public void setThree(BigDecimal three) {
		this.three = three;
	}

	public BigDecimal getFour() {
		return four;
	}

	public void setFour(BigDecimal four) {
		this.four = four;
	}

	public BigDecimal getFive() {
		return five;
	}

	public void setFive(BigDecimal five) {
		this.five = five;
	}

	public BigDecimal getSix() {
		return six;
	}

	public void setSix(BigDecimal six) {
		this.six = six;
	}

	public BigDecimal getSeven() {
		return seven;
	}

	public void setSeven(BigDecimal seven) {
		this.seven = seven;
	}

	public BigDecimal getEight() {
		return eight;
	}

	public void setEight(BigDecimal eight) {
		this.eight = eight;
	}

	public BigDecimal getNine() {
		return nine;
	}

	public void setNine(BigDecimal nine) {
		this.nine = nine;
	}

	public BigDecimal getTen() {
		return ten;
	}

	public void setTen(BigDecimal ten) {
		this.ten = ten;
	}

	public BigDecimal getElev() {
		return elev;
	}

	public void setElev(BigDecimal elev) {
		this.elev = elev;
	}

	public BigDecimal getTween() {
		return tween;
	}

	public void setTween(BigDecimal tween) {
		this.tween = tween;
	}

	public BigDecimal getThirteen() {
		return thirteen;
	}

	public void setThirteen(BigDecimal thirteen) {
		this.thirteen = thirteen;
	}

	public BigDecimal getFourteen() {
		return fourteen;
	}

	public void setFourteen(BigDecimal fourteen) {
		this.fourteen = fourteen;
	}

	public BigDecimal getFivteen() {
		return fivteen;
	}

	public void setFivteen(BigDecimal fivteen) {
		this.fivteen = fivteen;
	}

	public BigDecimal getSixteen() {
		return sixteen;
	}

	public void setSixteen(BigDecimal sixteen) {
		this.sixteen = sixteen;
	}

	public BigDecimal getSeventeen() {
		return seventeen;
	}

	public void setSeventeen(BigDecimal seventeen) {
		this.seventeen = seventeen;
	}

	public BigDecimal getEightteen() {
		return eightteen;
	}

	public void setEightteen(BigDecimal eightteen) {
		this.eightteen = eightteen;
	}

	public BigDecimal getNineteen() {
		return nineteen;
	}

	public void setNineteen(BigDecimal nineteen) {
		this.nineteen = nineteen;
	}

	public BigDecimal getTweity() {
		return tweity;
	}

	public void setTweity(BigDecimal tweity) {
		this.tweity = tweity;
	}

	public BigDecimal getTwyone() {
		return twyone;
	}

	public void setTwyone(BigDecimal twyone) {
		this.twyone = twyone;
	}

	public BigDecimal getTwytwo() {
		return twytwo;
	}

	public void setTwytwo(BigDecimal twytwo) {
		this.twytwo = twytwo;
	}

	public BigDecimal getTwythree() {
		return twythree;
	}

	public void setTwythree(BigDecimal twythree) {
		this.twythree = twythree;
	}

	public BigDecimal getTwyfour() {
		return twyfour;
	}

	public void setTwyfour(BigDecimal twyfour) {
		this.twyfour = twyfour;
	}

	public BigDecimal getTwyfive() {
		return twyfive;
	}

	public void setTwyfive(BigDecimal twyfive) {
		this.twyfive = twyfive;
	}

	public BigDecimal getTwysix() {
		return twysix;
	}

	public void setTwysix(BigDecimal twysix) {
		this.twysix = twysix;
	}

	public BigDecimal getTwyseven() {
		return twyseven;
	}

	public void setTwyseven(BigDecimal twyseven) {
		this.twyseven = twyseven;
	}

	public BigDecimal getTwyeight() {
		return twyeight;
	}

	public void setTwyeight(BigDecimal twyeight) {
		this.twyeight = twyeight;
	}

	public BigDecimal getTwynine() {
		return twynine;
	}

	public void setTwynine(BigDecimal twynine) {
		this.twynine = twynine;
	}

	public BigDecimal getThirty() {
		return thirty;
	}

	public void setThirty(BigDecimal thirty) {
		this.thirty = thirty;
	}

	public BigDecimal getThyone() {
		return thyone;
	}

	public void setThyone(BigDecimal thyone) {
		this.thyone = thyone;
	}

	public BigDecimal getSid() {
		return sid;
	}

	public void setSid(BigDecimal sid) {
		this.sid = sid;
	}

}
