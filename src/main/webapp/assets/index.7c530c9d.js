import {
    _ as A,
    a as N,
    A as X,
    b as m,
    B as Y,
    c as b,
    d as E,
    e as d,
    f as p,
    F as k,
    g as v,
    h as H,
    i as S,
    j as z,
    k as T,
    K as W,
    l as q,
    m as F,
    n as I,
    o as c,
    p as B,
    q as U,
    r as _,
    s as J,
    t as x,
    T as K,
    u as L,
    v as G,
    w as r,
    x as j,
    y as M,
    z as Q
} from "./vendor.45bae780.js";

const Z = function () {
    const o = document.createElement("link").relList;
    if (o && o.supports && o.supports("modulepreload")) return;
    for (const s of document.querySelectorAll('link[rel="modulepreload"]')) e(s);
    new MutationObserver(s => {
        for (const i of s) if (i.type === "childList") for (const l of i.addedNodes) l.tagName === "LINK" && l.rel === "modulepreload" && e(l)
    }).observe(document, {childList: !0, subtree: !0});

    function a(s) {
        const i = {};
        return s.integrity && (i.integrity = s.integrity), s.referrerpolicy && (i.referrerPolicy = s.referrerpolicy), s.crossorigin === "use-credentials" ? i.credentials = "include" : s.crossorigin === "anonymous" ? i.credentials = "omit" : i.credentials = "same-origin", i
    }

    function e(s) {
        if (s.ep) return;
        s.ep = !0;
        const i = a(s);
        fetch(s.href, i)
    }
};
Z();
var $ = (t, o) => {
    const a = t.__vccOpts || t;
    for (const [e, s] of o) a[e] = s;
    return a
};
const ee = {};

function te(t, o, a, e, s, i) {
    const l = _("router-view");
    return c(), b(l)
}

var oe = $(ee, [["render", te]]);
const ne = "modulepreload", O = {}, se = "./", y = function (o, a) {
    return !a || a.length === 0 ? o() : Promise.all(a.map(e => {
        if (e = `${se}${e}`, e in O) return;
        O[e] = !0;
        const s = e.endsWith(".css"), i = s ? '[rel="stylesheet"]' : "";
        if (document.querySelector(`link[href="${e}"]${i}`)) return;
        const l = document.createElement("link");
        if (l.rel = s ? "stylesheet" : ne, s || (l.as = "script", l.crossOrigin = ""), l.href = e, document.head.appendChild(l), s) return new Promise((h, f) => {
            l.addEventListener("load", h), l.addEventListener("error", f)
        })
    })).then(() => o())
}, C = E("sidebar", {
    state: () => ({collapse: !1}), getters: {}, actions: {
        handleCollapse() {
            this.collapse = !this.collapse
        }
    }
}), R = E("tags", {
    state: () => ({list: []}),
    getters: {show: t => t.list.length > 0, nameList: t => t.list.map(o => o.name)},
    actions: {
        delTagsItem(t) {
            this.list.splice(t, 1)
        }, setTagsItem(t) {
            this.list.push(t)
        }, clearTags() {
            this.list = []
        }, closeTagsOther(t) {
            this.list = t
        }, closeCurrentTag(t) {
            console.log(t);
            for (let o = 0, a = this.list.length; o < a; o++) if (this.list[o].path === t.$route.fullPath) {
                o < a - 1 ? t.$router.push(this.list[o + 1].path) : o > 0 ? t.$router.push(this.list[o - 1].path) : t.$router.push("/"), this.list.splice(o, 1);
                break
            }
        }
    }
});
var ae = "./assets/1.1ad1b94e.jpg";
const le = {
        setup() {
            const t = localStorage.getItem("ms_username"), o = 2, a = C(), e = () => {
                a.handleCollapse()
            };
            N(() => {
                document.body.clientWidth < 1500 && e()
            });
            const s = L();
            return {
                sidebar: a, username: t, message: o, collapseChage: e, handleCommand: l => {
                    l == "loginout" ? (localStorage.removeItem("ms_username"), localStorage.removeItem("token"), localStorage.removeItem("userName"), s.push("/login")) : l == "user" && s.push("/user")
                }
            }
        }
    }, P = t => (B("data-v-fe7876f4"), t = t(), H(), t), ie = {class: "header"}, re = {key: 0, class: "el-icon-s-fold"},
    ce = {key: 1, class: "el-icon-s-unfold"},
    de = P(() => d("div", {class: "logo"}, "\u8D85\u7EA7\u6E38\u620F\u540E\u53F0", -1)), ue = {class: "header-right"},
    _e = {class: "header-user-con"}, pe = P(() => d("div", {class: "user-avator"}, [d("img", {src: ae})], -1)),
    me = {class: "el-dropdown-link"}, he = P(() => d("i", {class: "el-icon-caret-bottom"}, null, -1));

function fe(t, o, a, e, s, i) {
    const l = _("el-dropdown-item"), h = _("el-dropdown-menu"), f = _("el-dropdown");
    return c(), m("div", ie, [d("div", {
        class: "collapse-btn",
        onClick: o[0] || (o[0] = (...n) => e.collapseChage && e.collapseChage(...n))
    }, [e.sidebar.collapse ? (c(), m("i", ce)) : (c(), m("i", re))]), de, d("div", ue, [d("div", _e, [pe, p(f, {
        class: "user-name",
        trigger: "click",
        onCommand: e.handleCommand
    }, {
        dropdown: r(() => [p(h, null, {
            default: r(() => [p(l, {
                divided: "",
                command: "loginout"
            }, {default: r(() => [v("\u9000\u51FA\u767B\u5F55")]), _: 1})]), _: 1
        })]), default: r(() => [d("span", me, [v(x(e.username) + " ", 1), he])]), _: 1
    }, 8, ["onCommand"])])])])
}

var ge = $(le, [["render", fe], ["__scopeId", "data-v-fe7876f4"]]);
const ve = {
    setup() {
        const t = [{
                icon: "el-icon-lx-redpacket_fill",
                index: "/dashbord",
                title: "\u7CFB\u7EDF\u9996\u9875"
            }, {
                icon: "el-icon-lx-redpacket_fill",
                index: "/role",
                title: "\u89D2\u8272\u7BA1\u7406"
            }, {
                icon: "el-icon-lx-redpacket_fill",
                index: "/basecharts",
                title: "\u4EA4\u6613\u4FE1\u606F\u7BA1\u7406"
            }, {
                icon: "el-icon-lx-redpacket_fill",
                index: "/giveinformation",
                title: "\u7ED9\u4E88\u4FE1\u606F\u7BA1\u7406"
            }, {
                icon: "el-icon-lx-redpacket_fill",
                index: "/jiaotan",
                title: "\u4EA4\u8C08\u4FE1\u606F\u7BA1\u7406"
            }, {
                icon: "el-icon-lx-redpacket_fill",
                index: "/loginuserip",
                title: "\u767B\u5F55IP\u7BA1\u7406"
            }, {icon: "el-icon-lx-redpacket_fill", index: "/fengjinuserip", title: "\u5C01\u7981IP\u7BA1\u7406"}], o = S(),
            a = z(() => o.path), e = C();
        return {items: t, onRoutes: a, sidebar: e}
    }
}, be = {class: "sidebar"};

function ye(t, o, a, e, s, i) {
    const l = _("el-menu-item"), h = _("el-submenu"), f = _("el-menu");
    return c(), m("div", be, [p(f, {
        class: "sidebar-el-menu",
        "default-active": e.onRoutes,
        collapse: t.collapse,
        "background-color": "#324157",
        "text-color": "#bfcbd9",
        "active-text-color": "#20a0ff",
        "unique-opened": "",
        router: ""
    }, {
        default: r(() => [(c(!0), m(k, null, T(e.items, n => (c(), m(k, null, [n.subs ? (c(), b(h, {
            index: n.index,
            key: n.index
        }, {
            title: r(() => [d("i", {class: I(n.icon)}, null, 2), d("span", null, x(n.title), 1)]),
            default: r(() => [(c(!0), m(k, null, T(n.subs, u => (c(), m(k, null, [u.subs ? (c(), b(h, {
                index: u.index,
                key: u.index
            }, {
                title: r(() => [v(x(u.title), 1)]),
                default: r(() => [(c(!0), m(k, null, T(u.subs, (g, w) => (c(), b(l, {
                    key: w,
                    index: g.index
                }, {default: r(() => [v(x(g.title), 1)]), _: 2}, 1032, ["index"]))), 128))]),
                _: 2
            }, 1032, ["index"])) : (c(), b(l, {index: u.index, key: u.index}, {
                default: r(() => [v(x(u.title), 1)]),
                _: 2
            }, 1032, ["index"]))], 64))), 256))]),
            _: 2
        }, 1032, ["index"])) : (c(), b(l, {index: n.index, key: n.index}, {
            title: r(() => [v(x(n.title), 1)]),
            default: r(() => [d("i", {class: I(n.icon)}, null, 2)]),
            _: 2
        }, 1032, ["index"]))], 64))), 256))]), _: 1
    }, 8, ["default-active", "collapse"])])
}

var xe = $(ve, [["render", ye], ["__scopeId", "data-v-502f4e57"]]);
const ke = {
        setup() {
            const t = S(), o = L(), a = n => n === t.fullPath, e = R(), s = n => {
                const u = e.list[n];
                e.delTagsItem(n);
                const g = e.list[n] ? e.list[n] : e.list[n - 1];
                g ? u.path === t.fullPath && o.push(g.path) : o.push("/")
            }, i = n => {
                e.list.some(g => g.path === n.fullPath) || (e.list.length >= 8 && e.delTagsItem(0), e.setTagsItem({
                    name: n.name,
                    title: n.meta.title,
                    path: n.fullPath
                }))
            };
            i(t), q(n => {
                i(n)
            });
            const l = () => {
                e.clearTags(), o.push("/")
            }, h = () => {
                const n = e.list.filter(u => u.path === t.fullPath);
                e.closeTagsOther(n)
            };
            return {
                isActive: a, tags: e, closeTags: s, handleTags: n => {
                    n === "other" ? h() : l()
                }
            }
        }
    }, $e = {key: 0, class: "tags"}, we = ["onClick"], Te = d("i", {class: "el-icon-close"}, null, -1), Ie = [Te],
    Ce = {class: "tags-close-box"}, Pe = d("i", {class: "el-icon-arrow-down el-icon--right"}, null, -1);

function Ee(t, o, a, e, s, i) {
    const l = _("router-link"), h = _("el-button"), f = _("el-dropdown-item"), n = _("el-dropdown-menu"),
        u = _("el-dropdown");
    return e.tags.show ? (c(), m("div", $e, [d("ul", null, [(c(!0), m(k, null, T(e.tags.list, (g, w) => (c(), m("li", {
        class: I(["tags-li", {active: e.isActive(g.path)}]),
        key: w
    }, [p(l, {to: g.path, class: "tags-li-title"}, {
        default: r(() => [v(x(g.title), 1)]),
        _: 2
    }, 1032, ["to"]), d("span", {
        class: "tags-li-icon",
        onClick: He => e.closeTags(w)
    }, Ie, 8, we)], 2))), 128))]), d("div", Ce, [p(u, {onCommand: e.handleTags}, {
        dropdown: r(() => [p(n, {size: "small"}, {
            default: r(() => [p(f, {command: "other"}, {
                default: r(() => [v("\u5173\u95ED\u5176\u4ED6")]),
                _: 1
            }), p(f, {command: "all"}, {default: r(() => [v("\u5173\u95ED\u6240\u6709")]), _: 1})]), _: 1
        })]),
        default: r(() => [p(h, {
            size: "mini",
            type: "primary"
        }, {default: r(() => [v(" \u6807\u7B7E\u9009\u9879 "), Pe]), _: 1})]),
        _: 1
    }, 8, ["onCommand"])])])) : F("", !0)
}

var Le = $(ke, [["render", Ee]]);
const Se = {
    components: {vHeader: ge, vSidebar: xe, vTags: Le}, setup() {
        const t = C();
        return {tags: R(), sidebar: t}
    }
}, je = {class: "about"}, Ae = {class: "content"};

function Oe(t, o, a, e, s, i) {
    const l = _("v-header"), h = _("v-sidebar"), f = _("v-tags"), n = _("router-view");
    return c(), m("div", je, [p(l), p(h), d("div", {class: I(["content-box", {"content-collapse": e.sidebar.collapse}])}, [p(f), d("div", Ae, [p(n, null, {
        default: r(({Component: u}) => [p(K, {
            name: "move",
            mode: "out-in"
        }, {
            default: r(() => [(c(), b(W, {include: e.tags.nameList}, [(c(), b(U(u)))], 1032, ["include"]))]),
            _: 2
        }, 1024)]), _: 1
    })])], 2)])
}

var Re = $(Se, [["render", Oe]]);
const Ve = [{
    path: "/",
    name: "Home",
    component: Re,
    children: [{
        path: "/dashbord",
        name: "dashbord",
        meta: {title: "\u7CFB\u7EDF\u9996\u9875"},
        component: () => y(() => import("./Dashbord.21072fb3.js"), ["assets/Dashbord.21072fb3.js", "assets/Dashbord.8f3cdb30.css", "assets/vendor.45bae780.js", "assets/index.ce5e7832.js", "assets/request.3dc6f249.js"])
    }, {
        path: "/role",
        name: "role",
        meta: {title: "\u89D2\u8272\u7BA1\u7406"},
        component: () => y(() => import("./Role.8f9adab7.js"), ["assets/Role.8f9adab7.js", "assets/Role.00d3631a.css", "assets/vendor.45bae780.js", "assets/index.ce5e7832.js", "assets/request.3dc6f249.js"])
    }, {
        path: "/basecharts",
        name: "basecharts",
        meta: {title: "\u4EA4\u6613\u7BA1\u7406"},
        component: () => y(() => import("./TradeInformation.d55f8183.js"), ["assets/TradeInformation.d55f8183.js", "assets/TradeInformation.eedc57e7.css", "assets/vendor.45bae780.js", "assets/index.ce5e7832.js", "assets/request.3dc6f249.js"])
    }, {
        path: "/giveinformation",
        name: "giveinformation",
        meta: {title: "\u7ED9\u4E88\u7BA1\u7406"},
        component: () => y(() => import("./GiveInformation.d47e070e.js"), ["assets/GiveInformation.d47e070e.js", "assets/GiveInformation.eb86694f.css", "assets/vendor.45bae780.js", "assets/index.ce5e7832.js", "assets/request.3dc6f249.js"])
    }, {
        path: "/jiaotan",
        name: "jiaotan",
        meta: {title: "\u4EA4\u8C08\u7BA1\u7406"},
        component: () => y(() => import("./Jiaotan.01362dba.js"), ["assets/Jiaotan.01362dba.js", "assets/Jiaotan.1bd783d1.css", "assets/vendor.45bae780.js", "assets/index.ce5e7832.js", "assets/request.3dc6f249.js"])
    }, {
        path: "/loginuserip",
        name: "loginuserip",
        meta: {title: "\u767B\u5F55IP\u7BA1\u7406"},
        component: () => y(() => import("./LoginUserIp.2c75a090.js"), ["assets/LoginUserIp.2c75a090.js", "assets/LoginUserIp.67ff45f4.css", "assets/vendor.45bae780.js", "assets/index.ce5e7832.js", "assets/request.3dc6f249.js"])
    }, {
        path: "/fengjinuserip",
        name: "fengjinuserip",
        meta: {title: "\u5C01\u7981IP\u7BA1\u7406"},
        component: () => y(() => import("./FengJinUserIp.d70e329f.js"), ["assets/FengJinUserIp.d70e329f.js", "assets/FengJinUserIp.ed7792a8.css", "assets/vendor.45bae780.js", "assets/index.ce5e7832.js", "assets/request.3dc6f249.js"])
    }]
}, {
    path: "/login",
    name: "Login",
    meta: {title: "\u767B\u5F55"},
    component: () => y(() => import("./Login.36e583c7.js"), ["assets/Login.36e583c7.js", "assets/Login.15a8f397.css", "assets/vendor.45bae780.js", "assets/request.3dc6f249.js"])
}], V = J({history: G(), routes: Ve});
V.beforeEach((t, o, a) => {
    document.title = `${t.meta.title} | \u81EA\u5B9A\u4E49`;
    const e = localStorage.getItem("token");
    !e && t.path !== "/login" ? a("/login") : t.meta.permission && e ? a() : e && t.path == "/login" || e && t.path == "/" ? a("/dashbord") : a()
});
var De = {
    "zh-cn": {
        i18n: {
            breadcrumb: "\u56FD\u9645\u5316\u4EA7\u54C1",
            tips: "\u901A\u8FC7\u5207\u6362\u8BED\u8A00\u6309\u94AE\uFF0C\u6765\u6539\u53D8\u5F53\u524D\u5185\u5BB9\u7684\u8BED\u8A00\u3002",
            btn: "\u5207\u6362\u82F1\u6587",
            title1: "\u5E38\u7528\u7528\u6CD5",
            p1: "\u8981\u662F\u4F60\u628A\u4F60\u7684\u79D8\u5BC6\u544A\u8BC9\u4E86\u98CE\uFF0C\u90A3\u5C31\u522B\u602A\u98CE\u628A\u5B83\u5E26\u7ED9\u6811\u3002",
            p2: "\u6CA1\u6709\u4EC0\u4E48\u6BD4\u4FE1\u5FF5\u66F4\u80FD\u652F\u6491\u6211\u4EEC\u5EA6\u8FC7\u8270\u96BE\u7684\u65F6\u5149\u4E86\u3002",
            p3: "\u53EA\u8981\u80FD\u628A\u81EA\u5DF1\u7684\u4E8B\u505A\u597D\uFF0C\u5E76\u8BA9\u81EA\u5DF1\u5FEB\u4E50\uFF0C\u4F60\u5C31\u9886\u5148\u4E8E\u5927\u591A\u6570\u4EBA\u4E86\u3002"
        }
    },
    en: {
        i18n: {
            breadcrumb: "International Products",
            tips: "Click on the button to change the current language. ",
            btn: "Switch Chinese",
            title1: "Common usage",
            p1: "If you reveal your secrets to the wind you should not blame the wind for  revealing them to the trees.",
            p2: "Nothing can help us endure dark times better than our faith. ",
            p3: "If you can do what you do best and be happy, you're further along in life  than most people."
        }
    }
};
const Ne = j({locale: A.name, fallbackLocale: M.name, messages: De});
var Be = t => {
    t.use(Q, {locale: A}), t.use(Ne)
};
const D = X(oe);
Be(D);
D.use(Y()).use(V).use(j).mount("#app");
export {$ as _, ae as a, R as u};
