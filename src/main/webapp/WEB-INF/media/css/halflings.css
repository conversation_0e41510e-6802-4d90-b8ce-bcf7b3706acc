/*!
 *
 *  Project:  GLYPHICONS HALFLINGS
 *  Author:   <PERSON> - www.glyphicons.com
 *  Twitter:  @jan<PERSON><PERSON>
 *
 */
html,
html .halflings {
  -webkit-font-smoothing: antialiased !important;
}
@font-face {
  font-family: 'Glyphicons Halflings';
  src: url('../image/glyphiconshalflings-regular.eot');
  src: url('../image/glyphiconshalflings-regular.eot') format('embedded-opentype'), url('../image/glyphiconshalflings-regular.woff') format('woff'), url('../image/glyphiconshalflings-regular.ttf') format('truetype'), url('../image/glyphiconshalflings-regular.svg#glyphicons_halflingsregular') format('svg');
  font-weight: normal;
  font-style: normal;
}
.halflings {
  display: inline-block;
  position: relative;
  padding: 0 0 0 25px;
  color: #1d1d1b;
  text-decoration: none;
  *display: inline;
  *zoom: 1;
}
.halflings i:before {
  position: absolute;
  left: 0;
  top: 0;
  font: 12px/1em 'Glyphicons Halflings';
  font-style: normal;
  color: #1d1d1b;
}
.halflings.white i:before {
  color: #fff;
}
.halflings.glass i:before {
  content: "\e001";
}
.halflings.music i:before {
  content: "\e002";
}
.halflings.search i:before {
  content: "\e003";
}
.halflings.envelope i:before {
  content: "\2709";
}
.halflings.heart i:before {
  content: "\e005";
}
.halflings.star i:before {
  content: "\e006";
}
.halflings.star-empty i:before {
  content: "\e007";
}
.halflings.user i:before {
  content: "\e008";
}
.halflings.film i:before {
  content: "\e009";
}
.halflings.th-large i:before {
  content: "\e010";
}
.halflings.th i:before {
  content: "\e011";
}
.halflings.th-list i:before {
  content: "\e012";
}
.halflings.ok i:before {
  content: "\e013";
}
.halflings.remove i:before {
  content: "\e014";
}
.halflings.zoom-in i:before {
  content: "\e015";
}
.halflings.zoom-out i:before {
  content: "\e016";
}
.halflings.off i:before {
  content: "\e017";
}
.halflings.signal i:before {
  content: "\e018";
}
.halflings.cog i:before {
  content: "\e019";
}
.halflings.trash i:before {
  content: "\e020";
}
.halflings.home i:before {
  content: "\e021";
}
.halflings.file i:before {
  content: "\e022";
}
.halflings.time i:before {
  content: "\e023";
}
.halflings.road i:before {
  content: "\e024";
}
.halflings.download-alt i:before {
  content: "\e025";
}
.halflings.download i:before {
  content: "\e026";
}
.halflings.upload i:before {
  content: "\e027";
}
.halflings.inbox i:before {
  content: "\e028";
}
.halflings.play-circle i:before {
  content: "\e029";
}
.halflings.repeat i:before {
  content: "\e030";
}
.halflings.refresh i:before {
  content: "\e031";
}
.halflings.list-alt i:before {
  content: "\e032";
}
.halflings.lock i:before {
  content: "\e033";
}
.halflings.flag i:before {
  content: "\e034";
}
.halflings.headphones i:before {
  content: "\e035";
}
.halflings.volume-off i:before {
  content: "\e036";
}
.halflings.volume-down i:before {
  content: "\e037";
}
.halflings.volume-up i:before {
  content: "\e038";
}
.halflings.qrcode i:before {
  content: "\e039";
}
.halflings.barcode i:before {
  content: "\e040";
}
.halflings.tag i:before {
  content: "\e041";
}
.halflings.tags i:before {
  content: "\e042";
}
.halflings.book i:before {
  content: "\e043";
}
.halflings.bookmark i:before {
  content: "\e044";
}
.halflings.print i:before {
  content: "\e045";
}
.halflings.camera i:before {
  content: "\e046";
}
.halflings.font i:before {
  content: "\e047";
}
.halflings.bold i:before {
  content: "\e048";
}
.halflings.italic i:before {
  content: "\e049";
}
.halflings.text-height i:before {
  content: "\e050";
}
.halflings.text-width i:before {
  content: "\e051";
}
.halflings.align-left i:before {
  content: "\e052";
}
.halflings.align-center i:before {
  content: "\e053";
}
.halflings.align-right i:before {
  content: "\e054";
}
.halflings.align-justify i:before {
  content: "\e055";
}
.halflings.list i:before {
  content: "\e056";
}
.halflings.indent-left i:before {
  content: "\e057";
}
.halflings.indent-right i:before {
  content: "\e058";
}
.halflings.facetime-video i:before {
  content: "\e059";
}
.halflings.picture i:before {
  content: "\e060";
}
.halflings.pencil i:before {
  content: "\270f";
}
.halflings.map-marker i:before {
  content: "\e062";
}
.halflings.adjust i:before {
  content: "\e063";
}
.halflings.tint i:before {
  content: "\e064";
}
.halflings.edit i:before {
  content: "\e065";
}
.halflings.share i:before {
  content: "\e066";
}
.halflings.check i:before {
  content: "\e067";
}
.halflings.move i:before {
  content: "\e068";
}
.halflings.step-backward i:before {
  content: "\e069";
}
.halflings.fast-backward i:before {
  content: "\e070";
}
.halflings.backward i:before {
  content: "\e071";
}
.halflings.play i:before {
  content: "\e072";
}
.halflings.pause i:before {
  content: "\e073";
}
.halflings.stop i:before {
  content: "\e074";
}
.halflings.forward i:before {
  content: "\e075";
}
.halflings.fast-forward i:before {
  content: "\e076";
}
.halflings.step-forward i:before {
  content: "\e077";
}
.halflings.eject i:before {
  content: "\e078";
}
.halflings.chevron-left i:before {
  content: "\e079";
}
.halflings.chevron-right i:before {
  content: "\e080";
}
.halflings.plus-sign i:before {
  content: "\e081";
}
.halflings.minus-sign i:before {
  content: "\e082";
}
.halflings.remove-sign i:before {
  content: "\e083";
}
.halflings.ok-sign i:before {
  content: "\e084";
}
.halflings.question-sign i:before {
  content: "\e085";
}
.halflings.info-sign i:before {
  content: "\e086";
}
.halflings.screenshot i:before {
  content: "\e087";
}
.halflings.remove-circle i:before {
  content: "\e088";
}
.halflings.ok-circle i:before {
  content: "\e089";
}
.halflings.ban-circle i:before {
  content: "\e090";
}
.halflings.arrow-left i:before {
  content: "\e091";
}
.halflings.arrow-right i:before {
  content: "\e092";
}
.halflings.arrow-up i:before {
  content: "\e093";
}
.halflings.arrow-down i:before {
  content: "\e094";
}
.halflings.share-alt i:before {
  content: "\e095";
}
.halflings.resize-full i:before {
  content: "\e096";
}
.halflings.resize-small i:before {
  content: "\e097";
}
.halflings.plus i:before {
  content: "\002b";
}
.halflings.minus i:before {
  content: "\2212";
}
.halflings.asterisk i:before {
  content: "\002a";
}
.halflings.exclamation-sign i:before {
  content: "\e101";
}
.halflings.gift i:before {
  content: "\e102";
}
.halflings.leaf i:before {
  content: "\e103";
}
.halflings.fire i:before {
  content: "\e104";
}
.halflings.eye-open i:before {
  content: "\e105";
}
.halflings.eye-close i:before {
  content: "\e106";
}
.halflings.warning-sign i:before {
  content: "\e107";
}
.halflings.plane i:before {
  content: "\e108";
}
.halflings.calendar i:before {
  content: "\e109";
}
.halflings.random i:before {
  content: "\e110";
}
.halflings.comments i:before {
  content: "\e111";
}
.halflings.magnet i:before {
  content: "\e113";
}
.halflings.chevron-up i:before {
  content: "\e113";
}
.halflings.chevron-down i:before {
  content: "\e114";
}
.halflings.retweet i:before {
  content: "\e115";
}
.halflings.shopping-cart i:before {
  content: "\e116";
}
.halflings.folder-close i:before {
  content: "\e117";
}
.halflings.folder-open i:before {
  content: "\e118";
}
.halflings.resize-vertical i:before {
  content: "\e119";
}
.halflings.resize-horizontal i:before {
  content: "\e120";
}
.halflings.hdd i:before {
  content: "\e121";
}
.halflings.bullhorn i:before {
  content: "\e122";
}
.halflings.bell i:before {
  content: "\e123";
}
.halflings.certificate i:before {
  content: "\e124";
}
.halflings.thumbs-up i:before {
  content: "\e125";
}
.halflings.thumbs-down i:before {
  content: "\e126";
}
.halflings.hand-right i:before {
  content: "\e127";
}
.halflings.hand-left i:before {
  content: "\e128";
}
.halflings.hand-top i:before {
  content: "\e129";
}
.halflings.hand-down i:before {
  content: "\e130";
}
.halflings.circle-arrow-right i:before {
  content: "\e131";
}
.halflings.circle-arrow-left i:before {
  content: "\e132";
}
.halflings.circle-arrow-top i:before {
  content: "\e133";
}
.halflings.circle-arrow-down i:before {
  content: "\e134";
}
.halflings.globe i:before {
  content: "\e135";
}
.halflings.wrench i:before {
  content: "\e136";
}
.halflings.tasks i:before {
  content: "\e137";
}
.halflings.filter i:before {
  content: "\e138";
}
.halflings.briefcase i:before {
  content: "\e139";
}
.halflings.fullscreen i:before {
  content: "\e140";
}
.halflings.dashboard i:before {
  content: "\e141";
}
.halflings.paperclip i:before {
  content: "\e142";
}
.halflings.heart-empty i:before {
  content: "\e143";
}
.halflings.link i:before {
  content: "\e144";
}
.halflings.phone i:before {
  content: "\e145";
}
.halflings.pushpin i:before {
  content: "\e146";
}
.halflings.euro i:before {
  content: "\20ac";
}
.halflings.usd i:before {
  content: "\e148";
}
.halflings.gbp i:before {
  content: "\e149";
}
.halflings.sort i:before {
  content: "\e150";
}
.halflings.sort-by-alphabet i:before {
  content: "\e151";
}
.halflings.sort-by-alphabet-alt i:before {
  content: "\e152";
}
.halflings.sort-by-order i:before {
  content: "\e153";
}
.halflings.sort-by-order-alt i:before {
  content: "\e154";
}
.halflings.sort-by-attributes i:before {
  content: "\e155";
}
.halflings.sort-by-attributes-alt i:before {
  content: "\e156";
}
.halflings.unchecked i:before {
  content: "\e157";
}
.halflings.expand i:before {
  content: "\e158";
}
.halflings.collapse i:before {
  content: "\e159";
}
.halflings.collapse-top i:before {
  content: "\e160";
}
.halflings-icon {
  display: inline-block;
  width: 14px;
  height: 14px;
  line-height: 14px;
  vertical-align: text-top;
  background-image: url(../image/glyphicons_halflings.svg);
  background-position: 0 0;
  background-repeat: no-repeat;
  vertical-align: top;
  *display: inline;
  *zoom: 1;
  *margin-right: .3em;
}
.no-inlinesvg .halflings-icon {
  background-image: url(../image/glyphicons_halflings.png);
}
.halflings-icon.white {
  background-image: url(../image/glyphicons_halflings-white.svg);
}
.no-inlinesvg .halflings-icon.white {
  background-image: url(../image/glyphicons_halflings-white.png);
}
.halflings-icon.glass {
  background-position: 0 0;
}
.halflings-icon.music {
  background-position: -24px 0;
}
.halflings-icon.search {
  background-position: -48px 0;
}
.halflings-icon.envelope {
  background-position: -72px 0;
}
.halflings-icon.heart {
  background-position: -96px 0;
}
.halflings-icon.star {
  background-position: -120px 0;
}
.halflings-icon.star-empty {
  background-position: -144px 0;
}
.halflings-icon.user {
  background-position: -168px 0;
}
.halflings-icon.film {
  background-position: -192px 0;
}
.halflings-icon.th-large {
  background-position: -216px 0;
}
.halflings-icon.th {
  background-position: -240px 0;
}
.halflings-icon.th-list {
  background-position: -264px 0;
}
.halflings-icon.ok {
  background-position: -288px 0;
}
.halflings-icon.remove {
  background-position: -312px 0;
}
.halflings-icon.zoom-in {
  background-position: -336px 0;
}
.halflings-icon.zoom-out {
  background-position: -360px 0;
}
.halflings-icon.off {
  background-position: -384px 0;
}
.halflings-icon.signal {
  background-position: -408px 0;
}
.halflings-icon.cog {
  background-position: -432px 0;
}
.halflings-icon.trash {
  background-position: -456px 0;
}
.halflings-icon.home {
  background-position: 0 -24px;
}
.halflings-icon.file {
  background-position: -24px -24px;
}
.halflings-icon.time {
  background-position: -48px -24px;
}
.halflings-icon.road {
  background-position: -72px -24px;
}
.halflings-icon.download-alt {
  background-position: -96px -24px;
}
.halflings-icon.download {
  background-position: -120px -24px;
}
.halflings-icon.upload {
  background-position: -144px -24px;
}
.halflings-icon.inbox {
  background-position: -168px -24px;
}
.halflings-icon.play-circle {
  background-position: -192px -24px;
}
.halflings-icon.repeat {
  background-position: -216px -24px;
}
.halflings-icon.refresh {
  background-position: -240px -24px;
}
.halflings-icon.list-alt {
  background-position: -264px -24px;
}
.halflings-icon.lock {
  background-position: -287px -24px;
}
.halflings-icon.flag {
  background-position: -312px -24px;
}
.halflings-icon.headphones {
  background-position: -336px -24px;
}
.halflings-icon.volume-off {
  background-position: -360px -24px;
}
.halflings-icon.volume-down {
  background-position: -384px -24px;
}
.halflings-icon.volume-up {
  background-position: -408px -24px;
}
.halflings-icon.qrcode {
  background-position: -432px -24px;
}
.halflings-icon.barcode {
  background-position: -456px -24px;
}
.halflings-icon.tag {
  background-position: 0 -48px;
}
.halflings-icon.tags {
  background-position: -25px -48px;
}
.halflings-icon.book {
  background-position: -48px -48px;
}
.halflings-icon.bookmark {
  background-position: -72px -48px;
}
.halflings-icon.print {
  background-position: -96px -48px;
}
.halflings-icon.camera {
  background-position: -120px -48px;
}
.halflings-icon.font {
  background-position: -144px -48px;
}
.halflings-icon.bold {
  background-position: -167px -48px;
}
.halflings-icon.italic {
  background-position: -192px -48px;
}
.halflings-icon.text-height {
  background-position: -216px -48px;
}
.halflings-icon.text-width {
  background-position: -240px -48px;
}
.halflings-icon.align-left {
  background-position: -264px -48px;
}
.halflings-icon.align-center {
  background-position: -288px -48px;
}
.halflings-icon.align-right {
  background-position: -312px -48px;
}
.halflings-icon.align-justify {
  background-position: -336px -48px;
}
.halflings-icon.list {
  background-position: -360px -48px;
}
.halflings-icon.indent-left {
  background-position: -384px -48px;
}
.halflings-icon.indent-right {
  background-position: -408px -48px;
}
.halflings-icon.facetime-video {
  background-position: -432px -48px;
}
.halflings-icon.picture {
  background-position: -456px -48px;
}
.halflings-icon.pencil {
  background-position: 0 -72px;
}
.halflings-icon.map-marker {
  background-position: -24px -72px;
}
.halflings-icon.adjust {
  background-position: -48px -72px;
}
.halflings-icon.tint {
  background-position: -72px -72px;
}
.halflings-icon.edit {
  background-position: -96px -72px;
}
.halflings-icon.share {
  background-position: -120px -72px;
}
.halflings-icon.check {
  background-position: -144px -72px;
}
.halflings-icon.move {
  background-position: -168px -72px;
}
.halflings-icon.step-backward {
  background-position: -192px -72px;
}
.halflings-icon.fast-backward {
  background-position: -216px -72px;
}
.halflings-icon.backward {
  background-position: -240px -72px;
}
.halflings-icon.play {
  background-position: -264px -72px;
}
.halflings-icon.pause {
  background-position: -288px -72px;
}
.halflings-icon.stop {
  background-position: -312px -72px;
}
.halflings-icon.forward {
  background-position: -336px -72px;
}
.halflings-icon.fast-forward {
  background-position: -360px -72px;
}
.halflings-icon.step-forward {
  background-position: -384px -72px;
}
.halflings-icon.eject {
  background-position: -408px -72px;
}
.halflings-icon.chevron-left {
  background-position: -432px -72px;
}
.halflings-icon.chevron-right {
  background-position: -456px -72px;
}
.halflings-icon.plus-sign {
  background-position: 0 -96px;
}
.halflings-icon.minus-sign {
  background-position: -24px -96px;
}
.halflings-icon.remove-sign {
  background-position: -48px -96px;
}
.halflings-icon.ok-sign {
  background-position: -72px -96px;
}
.halflings-icon.question-sign {
  background-position: -96px -96px;
}
.halflings-icon.info-sign {
  background-position: -120px -96px;
}
.halflings-icon.screenshot {
  background-position: -144px -96px;
}
.halflings-icon.remove-circle {
  background-position: -168px -96px;
}
.halflings-icon.ok-circle {
  background-position: -192px -96px;
}
.halflings-icon.ban-circle {
  background-position: -216px -96px;
}
.halflings-icon.arrow-left {
  background-position: -240px -96px;
}
.halflings-icon.arrow-right {
  background-position: -264px -96px;
}
.halflings-icon.arrow-up {
  background-position: -289px -96px;
}
.halflings-icon.arrow-down {
  background-position: -312px -96px;
}
.halflings-icon.share-alt {
  background-position: -336px -96px;
}
.halflings-icon.resize-full {
  background-position: -360px -96px;
}
.halflings-icon.resize-small {
  background-position: -384px -96px;
}
.halflings-icon.plus {
  background-position: -408px -96px;
}
.halflings-icon.minus {
  background-position: -433px -96px;
}
.halflings-icon.asterisk {
  background-position: -456px -96px;
}
.halflings-icon.exclamation-sign {
  background-position: 0 -120px;
}
.halflings-icon.gift {
  background-position: -24px -120px;
}
.halflings-icon.leaf {
  background-position: -48px -120px;
}
.halflings-icon.fire {
  background-position: -72px -120px;
}
.halflings-icon.eye-open {
  background-position: -96px -120px;
}
.halflings-icon.eye-close {
  background-position: -120px -120px;
}
.halflings-icon.warning-sign {
  background-position: -144px -120px;
}
.halflings-icon.plane {
  background-position: -168px -120px;
}
.halflings-icon.calendar {
  background-position: -192px -120px;
}
.halflings-icon.random {
  background-position: -216px -120px;
}
.halflings-icon.comments {
  background-position: -240px -120px;
}
.halflings-icon.magnet {
  background-position: -264px -120px;
}
.halflings-icon.chevron-up {
  background-position: -288px -120px;
}
.halflings-icon.chevron-down {
  background-position: -313px -119px;
}
.halflings-icon.retweet {
  background-position: -336px -120px;
}
.halflings-icon.shopping-cart {
  background-position: -360px -120px;
}
.halflings-icon.folder-close {
  background-position: -384px -120px;
}
.halflings-icon.folder-open {
  background-position: -408px -120px;
}
.halflings-icon.resize-vertical {
  background-position: -432px -119px;
}
.halflings-icon.resize-horizontal {
  background-position: -456px -118px;
}
.halflings-icon.hdd {
  background-position: 0px -144px;
}
.halflings-icon.bullhorn {
  background-position: -24px -144px;
}
.halflings-icon.bell {
  background-position: -48px -144px;
}
.halflings-icon.certificate {
  background-position: -72px -144px;
}
.halflings-icon.thumbs-up {
  background-position: -96px -144px;
}
.halflings-icon.thumbs-down {
  background-position: -120px -144px;
}
.halflings-icon.hand-right {
  background-position: -144px -144px;
}
.halflings-icon.hand-left {
  background-position: -168px -144px;
}
.halflings-icon.hand-top {
  background-position: -192px -144px;
}
.halflings-icon.hand-down {
  background-position: -216px -144px;
}
.halflings-icon.circle-arrow-right {
  background-position: -240px -144px;
}
.halflings-icon.circle-arrow-left {
  background-position: -264px -144px;
}
.halflings-icon.circle-arrow-top {
  background-position: -288px -144px;
}
.halflings-icon.circle-arrow-down {
  background-position: -313px -144px;
}
.halflings-icon.globe {
  background-position: -336px -144px;
}
.halflings-icon.wrench {
  background-position: -360px -144px;
}
.halflings-icon.tasks {
  background-position: -384px -144px;
}
.halflings-icon.filter {
  background-position: -408px -144px;
}
.halflings-icon.briefcase {
  background-position: -432px -144px;
}
.halflings-icon.fullscreen {
  background-position: -456px -144px;
}
.halflings-icon.dashboard {
  background-position: 0px -168px;
}
.halflings-icon.paperclip {
  background-position: -24px -168px;
}
.halflings-icon.heart-empty {
  background-position: -48px -168px;
}
.halflings-icon.link {
  background-position: -72px -168px;
}
.halflings-icon.phone {
  background-position: -96px -168px;
}
.halflings-icon.pushpin {
  background-position: -120px -168px;
}
.halflings-icon.euro {
  background-position: -144px -168px;
}
.halflings-icon.usd {
  background-position: -168px -168px;
}
.halflings-icon.gbp {
  background-position: -192px -168px;
}
.halflings-icon.sort {
  background-position: -216px -168px;
}
.halflings-icon.sort-by-alphabet {
  background-position: -240px -168px;
}
.halflings-icon.sort-by-alphabet-alt {
  background-position: -264px -168px;
}
.halflings-icon.sort-by-order {
  background-position: -288px -168px;
}
.halflings-icon.sort-by-order-alt {
  background-position: -313px -168px;
}
.halflings-icon.sort-by-attributes {
  background-position: -336px -168px;
}
.halflings-icon.sort-by-attributes-alt {
  background-position: -360px -168px;
}
.halflings-icon.unchecked {
  background-position: -384px -168px;
}
.halflings-icon.expand {
  background-position: -408px -168px;
}
.halflings-icon.collapse {
  background-position: -432px -168px;
}
.halflings-icon.collapse-top {
  background-position: -456px -168px;
}
