webpackJsonp([16],{IwvZ:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});n("eNfa");var s=n("VsUZ"),a={data:function(){return{roleId:""}},methods:{laborClear:function(){var e=this,t={roleId:this.roleId};Object(s.k)(t).then(function(t){"200"==t.status?e.$message.success("操作成功"):e.$message.error("操作失败")})}},mounted:function(){}},r={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"cm-title"},[e._v("清空抽奖次数")]),e._v(" "),n("div",{staticClass:"link-top"}),e._v(" "),n("div",{staticClass:"cm-search-div"},[n("el-input",{staticClass:"cm-search-input",attrs:{placeholder:"请输入角色ID"},model:{value:e.roleId,callback:function(t){e.roleId=t},expression:"roleId"}}),e._v(" "),n("el-button",{attrs:{slot:"append",type:"primary",icon:"el-icon-s-promotion"},on:{click:function(t){return e.laborClear()}},slot:"append"},[e._v("清空")])],1)])},staticRenderFns:[]};var o=n("VU/8")(a,r,!1,function(e){n("PUVb")},"data-v-4c42cb74",null);t.default=o.exports},PUVb:function(e,t){}});
//# sourceMappingURL=16.18f13902810fe982feb5.js.map