import {s as t} from "./request.3dc6f249.js";

const r = e => t({url: "/api/role", method: "post", params: e}),
    a = e => t({url: "/api/goods", method: "get", params: e}),
    o = e => t({url: "/api/good/getUserGood?roleId=" + e, method: "get"}),
    p = () => t({url: "/api/stat", method: "get"}), u = e => t({url: "/api/getUserGood", method: "post", params: e}),
    d = e => t({url: "/api/getUserPet", method: "post", params: e}),
    n = e => t({url: "/api/cb/updUserPet", method: "post", params: e}),
    l = e => t({url: "/api/getUserMount", method: "post", params: e}),
    m = e => t({url: "/api/updUserMount", method: "post", params: e}),
    g = e => t({url: "/api/getUserLing", method: "post", params: e}),
    c = e => t({url: "/api/updUserLing", method: "post", params: e}),
    i = e => t({url: "/api/getUserBaby", method: "post", params: e}),
    U = e => t({url: "/api/updUserBaby", method: "post", params: e}),
    h = e => t({url: "/api/updUserGood", method: "post", params: e}),
    G = e => (delete e.Value1, delete e.Value2, t({url: "/api/selectGoodsRecordNew", method: "post", params: e})),
    V = e => (delete e.Value1, delete e.Value2, t({url: "/api/getUserIp", method: "post", params: e})),
    b = e => (delete e.Value1, delete e.Value2, t({url: "/api/fengjin/getUserIp", method: "post", params: e})),
    f = e => (e.Value1, e.Value2, e.Value3, t({url: "/api/selectWeChatrecord", method: "post", params: e}));
export {
    c as a,
    m as b,
    n as c,
    h as d,
    a as e,
    r as f,
    u as g,
    d as h,
    l as i,
    g as j,
    i as k,
    o as l,
    G as m,
    f as n,
    V as o,
    b as p,
    p as s,
    U as u
};
